package com.ruoyi.app.qualityCost.controller;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.qualityCost.domain.CostCenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.qualityCost.domain.QualityCostDetail;
import com.ruoyi.app.qualityCost.service.IQualityCostDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 兴澄特钢质量成本总Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/qualityCost/qualityCostDetail")
public class QualityCostDetailController extends BaseController
{
    @Autowired
    private IQualityCostDetailService qualityCostDetailService;

    /**
     * 查询兴澄特钢质量成本总列表
     */
    @GetMapping("/list")
    public TableDataInfo list(QualityCostDetail qualityCostDetail)
    {
//        startPage();

        if(qualityCostDetail.getCostCenterCname()==null)
        {
            //此处需要求要求总合
            List<QualityCostDetail> list = qualityCostDetailService.selectQualityCostDetailListSum(qualityCostDetail);
            return getDataTable(list);
        }
        else
        {
            List<QualityCostDetail> list = qualityCostDetailService.selectQualityCostDetailList(qualityCostDetail);
            return getDataTable(list);
        }


    }

    @GetMapping("/listAllQualityCostDetail")
    public AjaxResult listAllQualityCostDetail(@RequestParam String yearMonth)
    {
        QualityCostDetail qualityCostDetail = new QualityCostDetail();
        qualityCostDetail.setYearMonth(yearMonth);
        List<QualityCostDetail> list = qualityCostDetailService.selectQualityCostDetailList(qualityCostDetail);
        return AjaxResult.success(list);
    }


    @GetMapping("/detaillist")
    public TableDataInfo detaillist(QualityCostDetail qualityCostDetail)
    {
        startPage();
        List<QualityCostDetail> list = qualityCostDetailService.selectQualityCostDetailList(qualityCostDetail);
        return getDataTable(list);
    }

    @GetMapping("/listAll")
    public AjaxResult listAll(QualityCostDetail qualityCostDetail)
    {
        return AjaxResult.success(qualityCostDetailService.selectMonthlyQualityCostDetailList(qualityCostDetail));
    }

    @GetMapping("/costCenterlist")
    public AjaxResult costCenterlist(QualityCostDetail qualityCostDetail)
    {
        List<JSONObject> list =new ArrayList<>();
        List<CostCenter> re = qualityCostDetailService.selectCostCenterList(qualityCostDetail);
        for(CostCenter item:re)
        {
            JSONObject newadd=new JSONObject();
            newadd.put("value",item.getCostCenterCname());
            newadd.put("label",item.getCostCenterCname());
            newadd.put("key",item.getCostCenter());
            list.add(newadd);
        }
        // 定义排序顺序
        List<String> sortOrder = Arrays.asList(
            "公司", "试验检测所", "一炼", "一轧", "二炼", "二轧大棒",
            "二轧小棒", "三轧钢", "高线", "杨市棒扁", "杨市盘卷",
            "银亮材", "线材深加工", "特板厂炼钢", "厚板",
            "中板", "钢板深加工", "三炼钢"
        );

        // 打印排序前的列表
        System.out.println("排序前的成本中心列表:");
        for (int i = 0; i < list.size(); i++) {
            JSONObject item = list.get(i);
            System.out.println((i + 1) + ". " + item.getString("value") + " (key: " + item.getString("key") + ")");
        }

        // 过滤掉不在排序列表中的元素
        list = list.stream()
                .filter(item -> sortOrder.contains(item.getString("value")))
                .collect(Collectors.toList());

        // 按指定顺序排序
        list.sort((a, b) -> {
            String valueA = a.getString("value");
            String valueB = b.getString("value");

            int indexA = sortOrder.indexOf(valueA);
            int indexB = sortOrder.indexOf(valueB);

            // 都在排序列表中，按索引排序
            return Integer.compare(indexA, indexB);
        });

        // 打印排序后的列表
        System.out.println("\n排序后的成本中心列表:");
        for (int i = 0; i < list.size(); i++) {
            JSONObject item = list.get(i);
            System.out.println((i + 1) + ". " + item.getString("value") + " (key: " + item.getString("key") + ")");
        }
        

//        JSONObject newadd=new JSONObject();
//        newadd.put("value","智信部");
//        newadd.put("label","智信部");
//        newadd.put("key","ZXB");
//        list.add(newadd);


        return AjaxResult.success(list);
    }

    /**
     * 查询预防成本数据
     */
    @GetMapping("/preventionCost")
    public AjaxResult getPreventionCost(QualityCostDetail qualityCostDetail)
    {
        List<QualityCostDetail> list = qualityCostDetailService.selectPreventionCostList(qualityCostDetail);
        return AjaxResult.success(list);
    }

    /**
     * 查询鉴定成本数据
     */
    @GetMapping("/appraisalCost")
    public AjaxResult getAppraisalCost(@RequestParam String costCenterCname, @RequestParam String yearMonth) {
        List<QualityCostDetail> list = qualityCostDetailService.selectAppraisalCost(costCenterCname, yearMonth);
        return AjaxResult.success(list);
    }

    /**
     * 查询内部损失成本数据
     */
    @GetMapping("/internalFailureCost")
    public AjaxResult getInternalFailureCost(@RequestParam String costCenterCname, @RequestParam String yearMonth) {
        List<QualityCostDetail> list = qualityCostDetailService.selectInternalFailureCost(costCenterCname, yearMonth);
        return AjaxResult.success(list);
    }

    /**
     * 查询外部损失成本数据
     */
    @GetMapping("/externalFailureCost")
    public TableDataInfo getExternalFailureCost(String costCenterCname, String yearMonth) {
        List<QualityCostDetail> list = qualityCostDetailService.selectExternalFailureCost(costCenterCname, yearMonth);
        return getDataTable(list);
    }

    /**
     * 导出兴澄特钢质量成本总列表
     */
    @Log(title = "兴澄特钢质量成本总", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(QualityCostDetail qualityCostDetail)
    {
        List<QualityCostDetail> list = qualityCostDetailService.selectQualityCostDetailList(qualityCostDetail);
        ExcelUtil<QualityCostDetail> util = new ExcelUtil<QualityCostDetail>(QualityCostDetail.class);
        return util.exportExcel(list, "qualityCostDetail");
    }

    /**
     * 获取兴澄特钢质量成本总详细信息
     */
    @GetMapping(value = "/{costCenterCname}")
    public AjaxResult getInfo(@PathVariable("costCenterCname") String costCenterCname)
    {
        return AjaxResult.success(qualityCostDetailService.selectQualityCostDetailById(costCenterCname));
    }

    /**
     * 新增兴澄特钢质量成本总
     */
    @Log(title = "兴澄特钢质量成本总", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QualityCostDetail qualityCostDetail)
    {
        return toAjax(qualityCostDetailService.insertQualityCostDetail(qualityCostDetail));
    }

    /**
     * 修改兴澄特钢质量成本总
     */
    @Log(title = "兴澄特钢质量成本总", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QualityCostDetail qualityCostDetail)
    {
        return toAjax(qualityCostDetailService.updateQualityCostDetail(qualityCostDetail));
    }

    /**
     * 删除兴澄特钢质量成本总
     */
    @Log(title = "兴澄特钢质量成本总", businessType = BusinessType.DELETE)
	@DeleteMapping("/{costCenterCnames}")
    public AjaxResult remove(@PathVariable String[] costCenterCnames)
    {
        return toAjax(qualityCostDetailService.deleteQualityCostDetailByIds(costCenterCnames));
    }

    /**
     * 查询产品挽救处理项列表
     */
    @GetMapping("/salvageItemsList")
    public AjaxResult getSalvageItemsList(String costCenterCname, String yearMonth) {
        List<QualityCostDetail> list = qualityCostDetailService.selectSalvageItemsList(costCenterCname, yearMonth);
        return AjaxResult.success(list);
    }

    /**
     * 导出兴澄特钢质量成本表（页面版）
     */
    @Log(title = "兴澄特钢质量成本表", businessType = BusinessType.EXPORT)
    @GetMapping("/exportPage")
    public AjaxResult exportPage(QualityCostDetail qualityCostDetail)
    {
        return qualityCostDetailService.exportQualityCostPage(qualityCostDetail);
    }

    /**
     * 导出工厂成本汇总表
     */
    @Log(title = "工厂成本汇总表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportFactoryCostSummary")
    public AjaxResult exportFactoryCostSummary(@RequestBody Map<String, Object> requestData)
    {
        return qualityCostDetailService.exportFactoryCostSummary(requestData);
    }
}
