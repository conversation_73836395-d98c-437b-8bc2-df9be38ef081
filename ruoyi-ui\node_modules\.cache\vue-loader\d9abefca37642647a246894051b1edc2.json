{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\regradeDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\regradeDetail\\index.vue", "mtime": 1754372909833}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBjb3N0Q2VudGVybGlzdCB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3F1YWxpdHlDb3N0RGV0YWlsIjsNCmltcG9ydCB7IGxpc3RBbGxSZWdyYWRlRGV0YWlsLCBnZXRTdW0gfSBmcm9tICJAL2FwaS9xdWFsaXR5Q29zdC9yZWdyYWRlRGV0YWlsIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiUmVncmFkZURldGFpbCIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIH0sDQogICAgICAvLyDmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ8NCiAgICAgIGNvc3RDZW50ZXI6ICcnLA0KICAgICAgYWNjb3VudGluZ1BlcmlvZDogdGhpcy5nZXREZWZhdWx0WWVhck1vbnRoKCksDQogICAgICAvLyDmlrDlop7vvJrmmK/lkKborqHliJLlhoXnrZvpgIkNCiAgICAgIHBsYW5GbGFnOiAnMScsDQogICAgICBwbGFuRmxhZ09wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogJ+WFqOmDqCcsIHZhbHVlOiAnJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5pivJywgdmFsdWU6ICcxJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5ZCmJywgdmFsdWU6ICcwJyB9DQogICAgICBdLA0KICAgICAgLy8g6KGo5qC85pWw5o2uDQogICAgICB0YWJsZURhdGE6IFtdLA0KICAgICAgLy8g6KGo5qC85Yqg6L2954q25oCBDQogICAgICB0YWJsZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g5oiQ5pys5Lit5b+D5YiX6KGoDQogICAgICBjb3N0Q2VudGVyT3B0aW9uczogW10sDQogICAgICBjb3N0Q2VudGVyTG9hZGluZzogZmFsc2UsDQogICAgICBzdW1EYXRhOiB7fSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5paw5aKe77ya5pCc57Si5Y+C5pWwDQogICAgICBzZWFyY2hQYXJhbXM6IHsNCiAgICAgICAgb2xkU2dTaWduOiAnJywNCiAgICAgICAgb2xkU2dTdGQ6ICcnLA0KICAgICAgICBuZXdTZ1NpZ246ICcnLA0KICAgICAgICBuZXdTZ1N0ZDogJycsDQogICAgICAgIHJlYXNvbjogJycsDQogICAgICAgIHNlYXJjaE1vZGU6ICfmqKHns4rmkJzntKInDQogICAgICB9LA0KICAgICAgc2VhcmNoTW9kZU9wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogJ+aooeeziuaQnOe0oicsIHZhbHVlOiAn5qih57OK5pCc57SiJyB9LA0KICAgICAgICB7IGxhYmVsOiAn57K+56Gu5pCc57SiJywgdmFsdWU6ICfnsr7noa7mkJzntKInIH0NCiAgICAgIF0sDQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDorqHnrpflsI/orqHmlbDmja4NCiAgICBzdWJ0b3RhbERhdGEoKSB7DQogICAgICBpZiAoIXRoaXMudGFibGVEYXRhIHx8IHRoaXMudGFibGVEYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gW3sNCiAgICAgICAgICBsYWJlbDogIuS6p+WTgeaUueWIpOaNn+WkseWwj+iuoSIsDQogICAgICAgICAgdG90YWxUb25uYWdlOiAwLA0KICAgICAgICAgIHRvdGFsUHJpY2VEaWZmZXJlbmNlOiAwLA0KICAgICAgICAgIHRvdGFsQW1vdW50OiAwDQogICAgICAgIH1dOw0KICAgICAgfQ0KDQogICAgICBjb25zdCB0b3RhbFRvbm5hZ2UgPSB0aGlzLnN1bURhdGEuY29zdFRvbjsNCg0KICAgICAgY29uc3QgdG90YWxQcmljZURpZmZlcmVuY2UgPSB0aGlzLnN1bURhdGEuY29zdFBlclRvbjsNCg0KICAgICAgY29uc3QgdG90YWxBbW91bnQgPSB0aGlzLnN1bURhdGEuY29zdEV4Ow0KDQogICAgICByZXR1cm4gW3sNCiAgICAgICAgbGFiZWw6ICLkuqflk4HmlLnliKTmjZ/lpLHlsI/orqEiLA0KICAgICAgICB0b3RhbFRvbm5hZ2U6IHRvdGFsVG9ubmFnZSwNCiAgICAgICAgdG90YWxQcmljZURpZmZlcmVuY2U6IHRvdGFsUHJpY2VEaWZmZXJlbmNlLA0KICAgICAgICB0b3RhbEFtb3VudDogdG90YWxBbW91bnQNCiAgICAgIH1dOw0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAvLyDnm5HlkKzmiJDmnKzkuK3lv4Plj5jljJYNCiAgICBjb3N0Q2VudGVyOiB7DQogICAgICBoYW5kbGVyKCkgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgICB0aGlzLmZldGNoVGFibGVEYXRhKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDnm5HlkKzkvJrorqHmnJ/lj5jljJYNCiAgICBhY2NvdW50aW5nUGVyaW9kOiB7DQogICAgICBoYW5kbGVyKCkgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgICB0aGlzLmZldGNoVGFibGVEYXRhKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlop7vvJrnm5HlkKzmmK/lkKborqHliJLlhoXlj5jljJYNCiAgICBwbGFuRmxhZzogew0KICAgICAgaGFuZGxlcigpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgdGhpcy5mZXRjaFRhYmxlRGF0YSgpOw0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldENvc3RDZW50ZXJMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAgLyoqIOiOt+WPlum7mOiupOS8muiuoeacnyAqLw0KICAgICAgZ2V0RGVmYXVsdFllYXJNb250aCgpIHsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCB5ZWFyID0gbm93LmdldEZ1bGxZZWFyKCk7DQogICAgICBjb25zdCBtb250aCA9IG5vdy5nZXRNb250aCgpICsgMTsgLy8gMS0xMg0KICAgICAgY29uc3QgZGF5ID0gbm93LmdldERhdGUoKTsNCiAgICAgIGNvbnN0IGhvdXIgPSBub3cuZ2V0SG91cnMoKTsNCg0KICAgICAgLy8g5aaC5p6c5LuK5aSp5piv5pys5pyIMjXlj7c454K55YmN77yI5ZCrMjXlj7c3OjU577yJ77yM5YiZ55So5LiK5Liq5pyIDQogICAgICBpZiAoZGF5IDwgMjggfHwgKGRheSA9PT0gMjggJiYgaG91ciA8IDEpKSB7DQogICAgICAgIC8vIOWkhOeQhjHmnIjml7bnmoTot6jlubQNCiAgICAgICAgY29uc3QgcHJldk1vbnRoID0gbW9udGggPT09IDEgPyAxMiA6IG1vbnRoIC0gMTsNCiAgICAgICAgY29uc3QgcHJldlllYXIgPSBtb250aCA9PT0gMSA/IHllYXIgLSAxIDogeWVhcjsNCiAgICAgICAgcmV0dXJuIGAke3ByZXZZZWFyfS0ke1N0cmluZyhwcmV2TW9udGgpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBgJHt5ZWFyfS0ke1N0cmluZyhtb250aCkucGFkU3RhcnQoMiwgJzAnKX1gOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlupPljLrmlbDmja7ovazmjaINCiAgICBjb252ZXJ0U3RvY2tBcmVhKHN0b2NrQXJlYSkgew0KICAgICAgaWYgKCFzdG9ja0FyZWEpIHJldHVybiAnJzsNCg0KICAgICAgY29uc3QgYXJlYVN0ciA9IHN0b2NrQXJlYS50b1N0cmluZygpLnRvVXBwZXJDYXNlKCk7DQoNCiAgICAgIGlmIChhcmVhU3RyID09PSAnUCcpIHsNCiAgICAgICAgcmV0dXJuICflna/mlpknOw0KICAgICAgfSBlbHNlIGlmIChhcmVhU3RyID09PSAnTScpIHsNCiAgICAgICAgcmV0dXJuICflnKjliLblk4EnOw0KICAgICAgfSBlbHNlIGlmIChhcmVhU3RyID09PSAnQycpIHsNCiAgICAgICAgcmV0dXJuICfmiJDlk4EnOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIHN0b2NrQXJlYTsgLy8g5aaC5p6c5LiN5pivUOOAgU3jgIFD77yM6L+U5Zue5Y6f5YC8DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluaIkOacrOS4reW/g+WIl+ihqA0KICAgIGdldENvc3RDZW50ZXJMaXN0KCkgew0KICAgICAgdGhpcy5jb3N0Q2VudGVyTG9hZGluZyA9IHRydWU7DQogICAgICBjb3N0Q2VudGVybGlzdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmNvc3RDZW50ZXJPcHRpb25zID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCiAgICAgICAgLy8g5aaC5p6c5pyJ5pWw5o2u77yM6K6+572u6buY6K6k6YCJ5Lit56ys5LiA5LiqDQogICAgICAgIGlmICh0aGlzLmNvc3RDZW50ZXJPcHRpb25zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLmNvc3RDZW50ZXIgPSB0aGlzLmNvc3RDZW50ZXJPcHRpb25zWzBdLmtleTsNCiAgICAgICAgICAvLyDorr7nva7pu5jorqTlgLzlkI7vvIzkuLvliqjop6blj5HkuIDmrKHmlbDmja7ojrflj5YNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmZldGNoVGFibGVEYXRhKCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5oiQ5pys5Lit5b+D5YiX6KGo5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5oiQ5pys5Lit5b+D5YiX6KGo5aSx6LSlJyk7DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5jb3N0Q2VudGVyTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluihqOagvOaVsOaNrg0KICAgIGZldGNoVGFibGVEYXRhKCkgew0KICAgICAgLy8g5Y+q5pyJ5b2T5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyf6YO95pyJ5YC85pe25omN6K+35rGCDQogICAgICBpZiAoIXRoaXMuY29zdENlbnRlciB8fCAhdGhpcy5hY2NvdW50aW5nUGVyaW9kKSB7DQogICAgICAgIHRoaXMudGFibGVEYXRhID0gW107DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdGhpcy50YWJsZUxvYWRpbmcgPSB0cnVlOw0KDQogICAgICAvLyDlvZPpgInmi6ki5rGf6Zi05YW05r6E54m556eN6ZKi6ZOBIuaXtu+8jOafpeivouaJgOacieaVsOaNru+8iOS4jeS8oGNvc3RDZW50ZXLlj4LmlbDvvIkNCiAgICAgIGxldCBjb3N0Q2VudGVyUGFyYW0gPSB0aGlzLmNvc3RDZW50ZXI7DQogICAgICBjb25zdCBzZWxlY3RlZE9wdGlvbiA9IHRoaXMuY29zdENlbnRlck9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0ua2V5ID09PSB0aGlzLmNvc3RDZW50ZXIpOw0KICAgICAgaWYgKHNlbGVjdGVkT3B0aW9uICYmIHNlbGVjdGVkT3B0aW9uLmxhYmVsID09PSAn5YWs5Y+4Jykgew0KICAgICAgICBjb3N0Q2VudGVyUGFyYW0gPSAnJzsgLy8g6K6+572u5Li656m65a2X56ym5Liy77yM5p+l6K+i5omA5pyJ5pWw5o2uDQogICAgICB9DQoNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY29zdENlbnRlciA9IGNvc3RDZW50ZXJQYXJhbTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMueWVhck1vbnRoID0gdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyk7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBsYW5GbGFnID0gdGhpcy5wbGFuRmxhZzsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2VhcmNoTW9kZSA9IHRoaXMuc2VhcmNoUGFyYW1zLnNlYXJjaE1vZGU7IC8vIOa3u+WKoOaQnOe0ouaooeW8j+WPguaVsA0KDQogICAgICAvLyDlkIjlubbmkJzntKLlj4LmlbDliLDmn6Xor6Llj4LmlbANCiAgICAgIE9iamVjdC5hc3NpZ24odGhpcy5xdWVyeVBhcmFtcywgdGhpcy5zZWFyY2hQYXJhbXMpOw0KDQogICAgICBjb25zb2xlLmxvZygn5p+l6K+i5Y+C5pWwOicsIHRoaXMucXVlcnlQYXJhbXMpOw0KDQogICAgICBsaXN0QWxsUmVncmFkZURldGFpbCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgLy90aGlzLnRhYmxlRGF0YSA9IChyZXNwb25zZS5yb3dzIHx8IFtdKS5maWx0ZXIoaXRlbSA9PiBpdGVtLmNvc3RFeCAhPT0gbnVsbCAmJiBpdGVtLmNvc3RFeCAhPT0gdW5kZWZpbmVkICYmIGl0ZW0uY29zdEV4ICE9PSAwKTsNCiAgICAgICAgdGhpcy50YWJsZURhdGEgPSByZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwgfHwgMDsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5pS55Yik5o2f5aSx5pWw5o2u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5pS55Yik5o2f5aSx5pWw5o2u5aSx6LSlJyk7DQogICAgICAgIHRoaXMudGFibGVEYXRhID0gW107DQogICAgICAgIHRoaXMudG90YWwgPSAwOw0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCg0KICAgICAgZ2V0U3VtKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnN1bURhdGEgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmlbDmja7lpLHotKUnKTsNCiAgICAgICAgdGhpcy5zdW1EYXRhID0gW107DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy50YWJsZUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5aSE55CG6K6h5YiS5YaF5qCH5b+X5YC8DQogICAgZ2V0UGxhbkZsYWdWYWx1ZShwbGFuRmxhZykgew0KICAgICAgaWYgKHBsYW5GbGFnID09PSAnMCcgfHwgcGxhbkZsYWcgPT09IDApIHsNCiAgICAgICAgcmV0dXJuICflkKYnOw0KICAgICAgfSBlbHNlIGlmIChwbGFuRmxhZyA9PT0gJzEnIHx8IHBsYW5GbGFnID09PSAxKSB7DQogICAgICAgIHJldHVybiAn5pivJzsNCiAgICAgIH0NCiAgICAgIHJldHVybiAn5pyq55+lJzsgLy8g5pei5LiN5pivMOS5n+S4jeaYrzHml7bmmL7npLrmnKrnn6UNCiAgICB9LA0KICAgIC8vIOiOt+WPluiuoeWIkuWGheagh+W/l+agh+etvuexu+Weiw0KICAgIGdldFBsYW5GbGFnVGFnVHlwZShwbGFuRmxhZykgew0KICAgICAgaWYgKHBsYW5GbGFnID09PSAnMCcgfHwgcGxhbkZsYWcgPT09IDApIHsNCiAgICAgICAgcmV0dXJuICdkYW5nZXInOyAvLyDnu7/oibINCiAgICAgIH0gZWxzZSBpZiAocGxhbkZsYWcgPT09ICcxJyB8fCBwbGFuRmxhZyA9PT0gMSkgew0KICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnOyAvLyDnuqLoibINCiAgICAgIH0NCiAgICAgIHJldHVybiAnd2FybmluZyc7IC8vIOm7hOiJsu+8iOacquefpeeKtuaAge+8iQ0KICAgIH0sDQogICAgLy8g6I635Y+W5b2T5YmN5bm05pyIDQogICAgZ2V0Q3VycmVudE1vbnRoKCkgew0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgIGNvbnN0IHllYXIgPSBub3cuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKG5vdy5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofWA7DQogICAgfSwNCiAgICAvLyDmoLzlvI/ljJbmlbDlrZfmmL7npLoNCiAgICBmb3JtYXROdW1iZXIodmFsdWUsIGRlY2ltYWxzID0gMikgew0KICAgICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQpIHJldHVybiAnJzsNCiAgICAgIHJldHVybiBOdW1iZXIodmFsdWUpLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiBkZWNpbWFscywNCiAgICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiBkZWNpbWFscw0KICAgICAgfSk7DQogICAgfSwNCiAgICBzdWJ0b3RhbFNwYW5NZXRob2QoeyByb3csIGNvbHVtbiwgcm93SW5kZXgsIGNvbHVtbkluZGV4IH0pIHsNCiAgICAgIC8vIOWQiOW5tuWJjTjliJfkuLrlsI/orqHmoIfnrb4NCiAgICAgIGlmIChjb2x1bW5JbmRleCA+PSAwICYmIGNvbHVtbkluZGV4IDw9IDgpIHsNCiAgICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAwKSB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHJvd3NwYW46IDEsDQogICAgICAgICAgICBjb2xzcGFuOiA5DQogICAgICAgICAgfTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgcm93c3BhbjogMCwNCiAgICAgICAgICAgIGNvbHNwYW46IDANCiAgICAgICAgICB9Ow0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyDlhbbku5bliJfkv53mjIHkuI3lj5gNCiAgICAgIHJldHVybiB7DQogICAgICAgIHJvd3NwYW46IDEsDQogICAgICAgIGNvbHNwYW46IDENCiAgICAgIH07DQogICAgfSwNCiAgICAvLyDmkJzntKLmjInpkq7ngrnlh7vkuovku7YNCiAgICBoYW5kbGVTZWFyY2goKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOyAvLyDmkJzntKLml7bph43nva7pobXnoIENCiAgICAgIHRoaXMuZmV0Y2hUYWJsZURhdGEoKTsNCiAgICB9LA0KICAgIC8vIOmHjee9ruaMiemSrueCueWHu+S6i+S7tg0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgdGhpcy5zZWFyY2hQYXJhbXMgPSB7DQogICAgICAgIG9sZFNnU2lnbjogJycsDQogICAgICAgIG9sZFNnU3RkOiAnJywNCiAgICAgICAgbmV3U2dTaWduOiAnJywNCiAgICAgICAgbmV3U2dTdGQ6ICcnLA0KICAgICAgICByZWFzb246ICcnLA0KICAgICAgICBzZWFyY2hNb2RlOiAn5qih57OK5pCc57SiJw0KICAgICAgfTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7IC8vIOmHjee9rumhteeggQ0KICAgICAgdGhpcy5mZXRjaFRhYmxlRGF0YSgpOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6KA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/regradeDetail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"regrade-detail-container\">\r\n      <!-- 表格标题 -->\r\n      <div class=\"table-title\">\r\n        <h2>兴澄特钢质量成本表-产品改判损失</h2>\r\n      </div>\r\n\r\n      <!-- 表格头部信息 -->\r\n      <div class=\"table-header-info\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">成本中心名称：</span>\r\n          <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\">\r\n            <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">会计期：</span>\r\n          <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"2025-06\" format=\"yyyy-MM\"\r\n            value-format=\"yyyy-MM\" style=\"width: 150px;\">\r\n          </el-date-picker>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">是否计划内：</span>\r\n          <el-select v-model=\"planFlag\" placeholder=\"全部\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in planFlagOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"search-bar-row\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判前钢种：</span>\r\n          <el-input v-model=\"searchParams.oldSgSign\" placeholder=\"请输入改判前钢种\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判前标准：</span>\r\n          <el-input v-model=\"searchParams.oldSgStd\" placeholder=\"请输入改判前标准\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判后钢种：</span>\r\n          <el-input v-model=\"searchParams.newSgSign\" placeholder=\"请输入改判后钢种\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判后标准：</span>\r\n          <el-input v-model=\"searchParams.newSgStd\" placeholder=\"请输入改判后标准\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判原因：</span>\r\n          <el-input v-model=\"searchParams.reason\" placeholder=\"请输入改判原因\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">搜索模式：</span>\r\n          <el-select v-model=\"searchParams.searchMode\" placeholder=\"请选择搜索模式\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in searchModeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item button-group\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\" size=\"small\">搜索</el-button>\r\n          <el-button @click=\"handleReset\" size=\"small\">重置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主表格 -->\r\n      <div class=\"main-table\">\r\n        <el-table :data=\"tableData\" border style=\"width: 100%; table-layout: fixed;\" class=\"regrade-detail-table\" v-loading=\"tableLoading\"\r\n          element-loading-text=\"加载中...\">\r\n          <el-table-column prop=\"oldSgSign\" label=\"改判前钢种\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"oldSgStd\" label=\"改判前标准\" align=\"center\" width=\"150\" />\r\n          <el-table-column prop=\"newSgSign\" label=\"改判后钢种\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"newSgStd\" label=\"改判后标准\" align=\"center\" width=\"150\" />\r\n          <el-table-column prop=\"crShp\" label=\"截面\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"thick\" label=\"厚度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.thick !== null && scope.row.thick !== undefined\">\r\n                {{ scope.row.thick }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"width\" label=\"宽度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.width !== null && scope.row.width !== undefined\">\r\n                {{ scope.row.width }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"len\" label=\"长度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.len !== null && scope.row.len !== undefined\">\r\n                {{ scope.row.len }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costPerTon\" label=\"售价差（元/吨）\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costPerTon !== null && scope.row.costPerTon !== undefined\">\r\n                {{ formatNumber(scope.row.costPerTon) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costTon\" label=\"吨位\" align=\"right\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costTon !== null && scope.row.costTon !== undefined\">\r\n                {{ formatNumber(scope.row.costTon, 2) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"costEx\" label=\"损失金额（元）\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costEx !== null && scope.row.costEx !== undefined\">\r\n                {{ formatNumber(scope.row.costEx) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"currStock\" label=\"当前库存\" align=\"center\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.currStock !== null && scope.row.currStock !== undefined && scope.row.currStock !== ''\">\r\n                {{ convertStockArea(scope.row.currStock) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"reason\" label=\"改判原因\" align=\"center\" min-width=\"120\" />\r\n          <el-table-column prop=\"planFlag\" label=\"是否计划内\" align=\"center\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"getPlanFlagTagType(scope.row.planFlag)\">\r\n                {{ getPlanFlagValue(scope.row.planFlag) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 小计行 -->\r\n      <div class=\"subtotal-section\">\r\n        <el-table :data=\"subtotalData\" border style=\"width: 100%; table-layout: fixed;\" class=\"subtotal-table\" :show-header=\"false\"\r\n          :span-method=\"subtotalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" width=\"150\"/>\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" width=\"150\"/>\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty8\" label=\"\" align=\"center\" width=\"130\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalPriceDifference\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalPriceDifference) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty9\" label=\"\" align=\"center\" width=\"100\" />\r\n          <el-table-column prop=\"empty10\" label=\"\" align=\"center\" min-width=\"120\" />\r\n          <el-table-column prop=\"empty11\" label=\"\" align=\"center\" width=\"100\" />\r\n        </el-table>\r\n      </div>\r\n      <pagination :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"fetchTableData\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { listAllRegradeDetail, getSum } from \"@/api/qualityCost/regradeDetail\";\r\n\r\nexport default {\r\n  name: \"RegradeDetail\",\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: this.getDefaultYearMonth(),\r\n      // 新增：是否计划内筛选\r\n      planFlag: '1',\r\n      planFlagOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '是', value: '1' },\r\n        { label: '否', value: '0' }\r\n      ],\r\n      // 表格数据\r\n      tableData: [],\r\n      // 表格加载状态\r\n      tableLoading: false,\r\n      // 成本中心列表\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      sumData: {},\r\n      total: 0,\r\n      // 新增：搜索参数\r\n      searchParams: {\r\n        oldSgSign: '',\r\n        oldSgStd: '',\r\n        newSgSign: '',\r\n        newSgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      },\r\n      searchModeOptions: [\r\n        { label: '模糊搜索', value: '模糊搜索' },\r\n        { label: '精确搜索', value: '精确搜索' }\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算小计数据\r\n    subtotalData() {\r\n      if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品改判损失小计\",\r\n          totalTonnage: 0,\r\n          totalPriceDifference: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.sumData.costTon;\r\n\r\n      const totalPriceDifference = this.sumData.costPerTon;\r\n\r\n      const totalAmount = this.sumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品改判损失小计\",\r\n        totalTonnage: totalTonnage,\r\n        totalPriceDifference: totalPriceDifference,\r\n        totalAmount: totalAmount\r\n      }];\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 新增：监听是否计划内变化\r\n    planFlag: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n  },\r\n  methods: {\r\n     /** 获取默认会计期 */\r\n      getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    // 库区数据转换\r\n    convertStockArea(stockArea) {\r\n      if (!stockArea) return '';\r\n\r\n      const areaStr = stockArea.toString().toUpperCase();\r\n\r\n      if (areaStr === 'P') {\r\n        return '坯料';\r\n      } else if (areaStr === 'M') {\r\n        return '在制品';\r\n      } else if (areaStr === 'C') {\r\n        return '成品';\r\n      } else {\r\n        return stockArea; // 如果不是P、M、C，返回原值\r\n      }\r\n    },\r\n\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据获取\r\n          this.$nextTick(() => {\r\n            this.fetchTableData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n\r\n    // 获取表格数据\r\n    fetchTableData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod) {\r\n        this.tableData = [];\r\n        return;\r\n      }\r\n\r\n      this.tableLoading = true;\r\n\r\n      // 当选择\"江阴兴澄特种钢铁\"时，查询所有数据（不传costCenter参数）\r\n      let costCenterParam = this.costCenter;\r\n      const selectedOption = this.costCenterOptions.find(item => item.key === this.costCenter);\r\n      if (selectedOption && selectedOption.label === '公司') {\r\n        costCenterParam = ''; // 设置为空字符串，查询所有数据\r\n      }\r\n\r\n      this.queryParams.costCenter = costCenterParam;\r\n      this.queryParams.yearMonth = this.accountingPeriod.replace('-', '');\r\n      this.queryParams.planFlag = this.planFlag;\r\n      this.queryParams.searchMode = this.searchParams.searchMode; // 添加搜索模式参数\r\n\r\n      // 合并搜索参数到查询参数\r\n      Object.assign(this.queryParams, this.searchParams);\r\n\r\n      console.log('查询参数:', this.queryParams);\r\n\r\n      listAllRegradeDetail(this.queryParams).then(response => {\r\n        //this.tableData = (response.rows || []).filter(item => item.costEx !== null && item.costEx !== undefined && item.costEx !== 0);\r\n        this.tableData = response.rows || [];\r\n        this.total = response.total || 0;\r\n      }).catch(error => {\r\n        console.error('获取改判损失数据失败:', error);\r\n        this.$message.error('获取改判损失数据失败');\r\n        this.tableData = [];\r\n        this.total = 0;\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getSum(this.queryParams).then(response => {\r\n        this.sumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.sumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n    },\r\n    // 处理计划内标志值\r\n    getPlanFlagValue(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return '否';\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return '是';\r\n      }\r\n      return '未知'; // 既不是0也不是1时显示未知\r\n    },\r\n    // 获取计划内标志标签类型\r\n    getPlanFlagTagType(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return 'danger'; // 绿色\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return 'success'; // 红色\r\n      }\r\n      return 'warning'; // 黄色（未知状态）\r\n    },\r\n    // 获取当前年月\r\n    getCurrentMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      return `${year}-${month}`;\r\n    },\r\n    // 格式化数字显示\r\n    formatNumber(value, decimals = 2) {\r\n      if (value === null || value === undefined) return '';\r\n      return Number(value).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: decimals,\r\n        maximumFractionDigits: decimals\r\n      });\r\n    },\r\n    subtotalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前8列为小计标签\r\n      if (columnIndex >= 0 && columnIndex <= 8) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 9\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 搜索按钮点击事件\r\n    handleSearch() {\r\n      this.queryParams.pageNum = 1; // 搜索时重置页码\r\n      this.fetchTableData();\r\n    },\r\n    // 重置按钮点击事件\r\n    handleReset() {\r\n      this.searchParams = {\r\n        oldSgSign: '',\r\n        oldSgStd: '',\r\n        newSgSign: '',\r\n        newSgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      };\r\n      this.queryParams.pageNum = 1; // 重置页码\r\n      this.fetchTableData();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.regrade-detail-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.table-header-info {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.header-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-item .label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .value {\r\n  color: #303133;\r\n}\r\n\r\n.header-item:first-child .label {\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.search-bar-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 12px;\r\n}\r\n\r\n.button-group {\r\n  margin-left: auto;\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.main-table {\r\n  margin-bottom: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.subtotal-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n/* 表格样式定制 */\r\n.regrade-detail-table {\r\n  font-size: 14px;\r\n  table-layout: fixed !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header-wrapper) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th) {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n  padding: 12px 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body td) {\r\n  box-sizing: border-box;\r\n  padding: 12px 8px;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body tr:nth-child(odd)) {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body tr:hover) {\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n/* 小计表格样式 */\r\n.subtotal-table {\r\n  font-size: 14px;\r\n  table-layout: fixed !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body tr) {\r\n  background-color: #f0f9ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body td) {\r\n  background-color: #f0f9ff !important;\r\n  padding: 12px 8px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 确保表格容器对齐 */\r\n.main-table,\r\n.subtotal-section {\r\n  width: 100%;\r\n}\r\n\r\n.main-table .el-table,\r\n.subtotal-section .el-table {\r\n  width: 100% !important;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1400px) {\r\n  .table-header-info {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .regrade-detail-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .regrade-detail-table :deep(.el-table__body td) {\r\n    padding: 8px 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .regrade-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .main-table {\r\n    overflow-x: auto;\r\n  }\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.header-item .el-input {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n/* 360浏览器兼容性修复 */\r\n.regrade-detail-table :deep(.el-table__header),\r\n.regrade-detail-table :deep(.el-table__body) {\r\n  table-layout: fixed;\r\n  width: 100%;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header colgroup),\r\n.regrade-detail-table :deep(.el-table__body colgroup) {\r\n  width: 100%;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body) {\r\n  table-layout: fixed;\r\n  width: 100%;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body colgroup) {\r\n  width: 100%;\r\n}\r\n\r\n/* 强制表格列宽度同步 */\r\n.regrade-detail-table :deep(.el-table__header th),\r\n.regrade-detail-table :deep(.el-table__body td) {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 修复边框对齐问题 */\r\n.regrade-detail-table :deep(.el-table),\r\n.subtotal-table :deep(.el-table) {\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th),\r\n.regrade-detail-table :deep(.el-table__body td),\r\n.subtotal-table :deep(.el-table__body td) {\r\n  border-right: 1px solid #ebeef5;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th:last-child),\r\n.regrade-detail-table :deep(.el-table__body td:last-child),\r\n.subtotal-table :deep(.el-table__body td:last-child) {\r\n  border-right: 1px solid #ebeef5;\r\n}\r\n</style>\r\n"]}]}