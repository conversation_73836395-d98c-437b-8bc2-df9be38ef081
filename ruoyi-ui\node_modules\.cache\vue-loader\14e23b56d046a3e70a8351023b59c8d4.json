{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\regradeDetail\\index.vue?vue&type=template&id=02a4eb2a&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\regradeDetail\\index.vue", "mtime": 1754372909833}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}