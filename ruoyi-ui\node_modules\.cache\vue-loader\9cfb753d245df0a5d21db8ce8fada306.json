{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\qualityCostPage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\qualityCostPage\\index.vue", "mtime": 1754380715769}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UXVhbGl0eUNvc3RQYWdlLCBjb3N0Q2VudGVybGlzdCB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3F1YWxpdHlDb3N0RGV0YWlsIjsNCmltcG9ydCB7IGV4cG9ydFF1YWxpdHlDb3N0UGFnZSB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3F1YWxpdHlDb3N0UGFnZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlF1YWxpdHlDb3N0IiwNCiAgY29tcG9uZW50czogew0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g6LSo6YeP5oiQ5pys5pWw5o2u5YiX6KGoDQogICAgICBxdWFsaXR5Q29zdExpc3Q6IFtdLA0KICAgICAgLy8g5oiQ5pys5Lit5b+D6YCJ6aG5DQogICAgICBjb3N0Q2VudGVyT3B0aW9uczogW10sDQogICAgICAvLyDkuqfph4/kv6Hmga8NCiAgICAgIHByb2R1Y3Rpb25JbmZvOiBudWxsLA0KICAgICAgLy8g5ZCI5bm25Y2V5YWD5qC86YWN572uDQogICAgICBtZXJnZUNlbGxzOiBbXSwNCiAgICAgIC8vIOaIkOacrOexu+Wei+mAiemhuQ0KICAgICAgY29zdFR5cGVPcHRpb25zOiBbDQogICAgICAgIHsgdmFsdWU6ICcnLCBsYWJlbDogJ+WFqOmDqCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0EnLCBsYWJlbDogJ0Et6aKE6Ziy5oiQ5pysJyB9LA0KICAgICAgICB7IHZhbHVlOiAnQicsIGxhYmVsOiAnQi3pibTlrprmiJDmnKwnIH0sDQogICAgICAgIHsgdmFsdWU6ICdDJywgbGFiZWw6ICdDLeWGhemDqOaNn+WkseaIkOacrCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0QnLCBsYWJlbDogJ0Qt5aSW6YOo5o2f5aSx5oiQ5pysJyB9DQogICAgICBdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBjb3N0Q2VudGVyOiAnSllYQ1RaRycsIC8vIOm7mOiupOmAieS4reWFtOa+hOeJuemSouaxh+aAuw0KICAgICAgICB5ZWFyTW9udGg6IG51bGwsDQogICAgICAgIGNvc3RUeXBlOiBbJyddDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLnF1ZXJ5UGFyYW1zLnllYXJNb250aCA9IHRoaXMuZ2V0RGVmYXVsdFllYXJNb250aCgpOw0KICAgIHRoaXMuZ2V0Q29zdENlbnRlckxpc3QoKTsNCiAgICAvLyDpobXpnaLliqDovb3ml7boh6rliqjop6blj5HmkJzntKINCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog6I635Y+W5oiQ5pys5Lit5b+D5YiX6KGoICovDQogICAgZ2V0Q29zdENlbnRlckxpc3QoKSB7DQogICAgICBjb3N0Q2VudGVybGlzdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zdCBvcHRpb25zID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCiAgICAgICAgLy8g5bCGSllYQ1RaR+mAiemhueaOkuWcqOesrOS4gOS9jQ0KICAgICAgICBjb25zdCBqeXhjdHpnT3B0aW9uID0gb3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS5rZXkgPT09ICdKWVhDVFpHJyk7DQogICAgICAgIGNvbnN0IG90aGVyT3B0aW9ucyA9IG9wdGlvbnMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5rZXkgIT09ICdKWVhDVFpHJyk7DQoNCiAgICAgICAgaWYgKGp5eGN0emdPcHRpb24pIHsNCiAgICAgICAgICB0aGlzLmNvc3RDZW50ZXJPcHRpb25zID0gW2p5eGN0emdPcHRpb24sIC4uLm90aGVyT3B0aW9uc107DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5jb3N0Q2VudGVyT3B0aW9ucyA9IG9wdGlvbnM7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5oiQ5pys5Lit5b+D5YiX6KGo5aSx6LSlJyk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluaIkOacrOexu+WeiyAqLw0KICAgIGdldENvc3RUeXBlKHR5cGVDb2RlKSB7DQogICAgICBpZiAoIXR5cGVDb2RlKSByZXR1cm4gJyc7DQogICAgICBjb25zdCBmaXJzdENoYXIgPSB0eXBlQ29kZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKTsNCiAgICAgIHN3aXRjaCAoZmlyc3RDaGFyKSB7DQogICAgICAgIGNhc2UgJ0EnOiByZXR1cm4gJ+mihOmYsuaIkOacrCc7DQogICAgICAgIGNhc2UgJ0InOiByZXR1cm4gJ+mJtOWumuaIkOacrCc7DQogICAgICAgIGNhc2UgJ0MnOiByZXR1cm4gJ+WGhemDqOaNn+WkseaIkOacrCc7DQogICAgICAgIGNhc2UgJ0QnOiByZXR1cm4gJ+WklumDqOaNn+WkseaIkOacrCc7DQogICAgICAgIGRlZmF1bHQ6IHJldHVybiAn5rGH5oC7JzsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiuoeeul+WQiOW5tuWNleWFg+agvCAqLw0KICAgIGNhbGN1bGF0ZU1lcmdlQ2VsbHMoKSB7DQogICAgICBjb25zdCBtZXJnZUNlbGxzID0gW107DQogICAgICBjb25zdCBjb3N0VHlwZUdyb3VwcyA9IHt9Ow0KDQogICAgICAvLyDmjInmiJDmnKznsbvlnovliIbnu4QNCiAgICAgIHRoaXMucXVhbGl0eUNvc3RMaXN0LmZvckVhY2goKHJvdywgaW5kZXgpID0+IHsNCiAgICAgICAgY29uc3QgY29zdFR5cGUgPSByb3cuY29zdFR5cGU7DQogICAgICAgIGlmICghY29zdFR5cGVHcm91cHNbY29zdFR5cGVdKSB7DQogICAgICAgICAgY29zdFR5cGVHcm91cHNbY29zdFR5cGVdID0gW107DQogICAgICAgIH0NCiAgICAgICAgY29zdFR5cGVHcm91cHNbY29zdFR5cGVdLnB1c2goaW5kZXgpOw0KICAgICAgfSk7DQoNCiAgICAgIC8vIOeUn+aIkOWQiOW5tumFjee9rg0KICAgICAgT2JqZWN0LnZhbHVlcyhjb3N0VHlwZUdyb3VwcykuZm9yRWFjaChncm91cCA9PiB7DQogICAgICAgIGlmIChncm91cC5sZW5ndGggPiAxKSB7DQogICAgICAgICAgbWVyZ2VDZWxscy5wdXNoKHsNCiAgICAgICAgICAgIHJvdzogZ3JvdXBbMF0sDQogICAgICAgICAgICBjb2w6IDAsDQogICAgICAgICAgICByb3dzcGFuOiBncm91cC5sZW5ndGgsDQogICAgICAgICAgICBjb2xzcGFuOiAxDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICB0aGlzLm1lcmdlQ2VsbHMgPSBtZXJnZUNlbGxzOw0KICAgIH0sDQoNCiAgICAvKiog5qC85byP5YyW5pWw5a2XICovDQogICAgZm9ybWF0TnVtYmVyKG51bSkgew0KICAgICAgaWYgKG51bSA9PT0gbnVsbCB8fCBudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09ICcnIHx8IG51bSA9PT0gMCkgew0KICAgICAgICByZXR1cm4gJy0nOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIE51bWJlcihudW0pLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLA0KICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDINCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5qC85byP5YyW55m+5YiG5q+UICovDQogICAgZm9ybWF0UGVyY2VudGFnZShudW0pIHsNCiAgICAgIGlmIChudW0gPT09IG51bGwgfHwgbnVtID09PSB1bmRlZmluZWQgfHwgbnVtID09PSAnJyB8fCBudW0gPT09IDApIHsNCiAgICAgICAgcmV0dXJuICctJzsNCiAgICAgIH0NCiAgICAgIHJldHVybiAoTnVtYmVyKG51bSkgKiAxMDApLnRvRml4ZWQoMykgKyAnJSc7DQogICAgfSwNCg0KICAgIC8qKiDmoLzlvI/ljJbotKfluIEgKi8NCiAgICBmb3JtYXRDdXJyZW5jeShudW0pIHsNCiAgICAgIGlmIChudW0gPT09IG51bGwgfHwgbnVtID09PSB1bmRlZmluZWQgfHwgbnVtID09PSAnJyB8fCBudW0gPT09IDApIHsNCiAgICAgICAgcmV0dXJuICctJzsNCiAgICAgIH0NCiAgICAgIHJldHVybiBOdW1iZXIobnVtKS50b0xvY2FsZVN0cmluZygnemgtQ04nLCB7DQogICAgICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMiwNCiAgICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiAyDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOagvOW8j+WMluS6p+mHjy/plIDph48gKi8NCiAgICBmb3JtYXRQcm9kdWN0aW9uKG51bSkgew0KICAgICAgaWYgKG51bSA9PT0gbnVsbCB8fCBudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09ICcnIHx8IG51bSA9PT0gMCkgew0KICAgICAgICByZXR1cm4gJy0nOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIE51bWJlcihudW0pLnRvRml4ZWQoMyk7DQogICAgfSwNCg0KICAgIC8qKiDmiJDmnKznsbvlnovlj5jljJblpITnkIYgKi8NCiAgICBoYW5kbGVDb3N0VHlwZUNoYW5nZSh2YWx1ZSkgew0KICAgICAgLy8g5aaC5p6c6YCJ5oup5LqGIuWFqOmDqCLvvIzmuIXnqbrlhbbku5bpgInpobkNCiAgICAgIGlmICh2YWx1ZS5pbmNsdWRlcygnJykpIHsNCiAgICAgICAgaWYgKHZhbHVlLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgICAvLyDlpoLmnpzlkIzml7bpgInmi6nkuoYi5YWo6YOoIuWSjOWFtuS7lumAiemhue+8jOWPquS/neeVmSLlhajpg6giDQogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3N0VHlwZSA9IFsnJ107DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOayoeaciemAieaLqSLlhajpg6gi77yM56Gu5L+dIuWFqOmDqCLkuI3lnKjpgInmi6nliJfooajkuK0NCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3N0VHlwZSA9IHZhbHVlLmZpbHRlcihpdGVtID0+IGl0ZW0gIT09ICcnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluS6p+mHj+agh+etviAqLw0KICAgIGdldFByb2R1Y3Rpb25MYWJlbCgpIHsNCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RDZW50ZXIgPT09ICdKWVhDVFpHJykgew0KICAgICAgICByZXR1cm4gJ+mUgOmHjyc7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ+S6p+mHjyc7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmn6Xor6LotKjph4/miJDmnKzliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgLy8g6aqM6K+B5b+F5aGr5Y+C5pWwDQogICAgICBpZiAoIXRoaXMucXVlcnlQYXJhbXMueWVhck1vbnRoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5Lya6K6h5pyfJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCg0KICAgICAgLy8g5aSE55CG5pel5pyf5qC85byP77ya5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgY29uc3QgZm9ybWF0WWVhck1vbnRoID0gdGhpcy5xdWVyeVBhcmFtcy55ZWFyTW9udGgucmVwbGFjZSgnLScsICcnKTsNCg0KICAgICAgLy8g5aSE55CG5oiQ5pys57G75Z6L5Y+C5pWwDQogICAgICBsZXQgdHlwZUNvZGVMaXN0ID0gW107DQogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5jb3N0VHlwZSAmJiB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RUeXBlLmxlbmd0aCA+IDApIHsNCiAgICAgICAgLy8g5aaC5p6c6YCJ5oup5LqGIuWFqOmDqCLmiJbmlbDnu4TkuLrnqbrvvIzkvKDpgJLnqbrmlbDnu4QNCiAgICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuY29zdFR5cGUuaW5jbHVkZXMoJycpIHx8IHRoaXMucXVlcnlQYXJhbXMuY29zdFR5cGUubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgdHlwZUNvZGVMaXN0ID0gW107DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdHlwZUNvZGVMaXN0ID0gdGhpcy5xdWVyeVBhcmFtcy5jb3N0VHlwZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBjb25zdCByZXF1ZXN0UGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RDZW50ZXIgfHwgJycsDQogICAgICAgIHllYXJNb250aDogZm9ybWF0WWVhck1vbnRoLA0KICAgICAgICB0eXBlQ29kZUxpc3Q6IHR5cGVDb2RlTGlzdA0KICAgICAgfTsNCg0KICAgICAgbGlzdFF1YWxpdHlDb3N0UGFnZShyZXF1ZXN0UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmRhdGEgfHwge307DQoNCiAgICAgICAgLy8g5LuO6L+U5Zue55qEUXVhbGl0eUNvc3RNb250aGx5Vk/lr7nosaHkuK3ojrflj5bmlbDmja4NCiAgICAgICAgbGV0IHF1YWxpdHlDb3N0RGV0YWlsTGlzdCA9IGRhdGEucXVhbGl0eUNvc3REZXRhaWxMaXN0IHx8IFtdOw0KDQogICAgICAgIC8vIOe7n+S4gOS9v+eUqHR5cGVDb2Rl5Li6J1on55qE5pWw5o2u5L2c5Li65Lqn6YePL+mUgOmHj+S/oeaBrw0KICAgICAgICBjb25zdCBwcm9kdWN0aW9uRGF0YSA9IHF1YWxpdHlDb3N0RGV0YWlsTGlzdC5maW5kKGl0ZW0gPT4gaXRlbS50eXBlQ29kZSA9PT0gJ1onKTsNCiAgICAgICAgdGhpcy5wcm9kdWN0aW9uSW5mbyA9IHByb2R1Y3Rpb25EYXRhIHx8IG51bGw7DQoNCg0KDQogICAgICAgIC8vIOS4uuavj+S4gOihjOa3u+WKoOaIkOacrOexu+WIq+S/oeaBr++8jOeUqOS6juWJjeerr+ihqOagvOaYvuekuuWSjOWQiOW5tg0KICAgICAgICBxdWFsaXR5Q29zdERldGFpbExpc3QuZm9yRWFjaChyb3cgPT4gew0KICAgICAgICAgIHJvdy5jb3N0VHlwZSA9IHRoaXMuZ2V0Q29zdFR5cGUocm93LnR5cGVDb2RlKTsNCiAgICAgICAgfSk7DQoNCiAgICAgICAgdGhpcy5xdWFsaXR5Q29zdExpc3QgPSBxdWFsaXR5Q29zdERldGFpbExpc3Q7DQoNCiAgICAgICAgLy8g6K6h566X5ZCI5bm25Y2V5YWD5qC8DQogICAgICAgIHRoaXMuY2FsY3VsYXRlTWVyZ2VDZWxscygpOw0KDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5p+l6K+i5aSx6LSlJyk7DQogICAgICAgIHRoaXMucXVhbGl0eUNvc3RMaXN0ID0gW107DQogICAgICAgIHRoaXMucHJvZHVjdGlvbkluZm8gPSBudWxsOw0KICAgICAgICB0aGlzLm1lcmdlQ2VsbHMgPSBbXTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICAvLyDph43nva7kuLrpu5jorqTlgLwNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY29zdENlbnRlciA9ICdKWVhDVFpHJzsgLy8g6buY6K6k6YCJ5Lit5YW05r6E54m56ZKi5rGH5oC7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnllYXJNb250aCA9IHRoaXMuZ2V0RGVmYXVsdFllYXJNb250aCgpOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3N0VHlwZSA9IFsnJ107DQogICAgICAvLyDmuIXnqbrmlbDmja4NCiAgICAgIHRoaXMucXVhbGl0eUNvc3RMaXN0ID0gW107DQogICAgICB0aGlzLnByb2R1Y3Rpb25JbmZvID0gbnVsbDsNCiAgICAgIHRoaXMubWVyZ2VDZWxscyA9IFtdOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W6buY6K6k5Lya6K6h5pyf77yI5LiK5Liq5pyI77yJICovDQogICAgZ2V0RGVmYXVsdFllYXJNb250aCgpIHsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCB5ZWFyID0gbm93LmdldEZ1bGxZZWFyKCk7DQogICAgICBjb25zdCBtb250aCA9IG5vdy5nZXRNb250aCgpICsgMTsgLy8gMS0xMg0KICAgICAgY29uc3QgZGF5ID0gbm93LmdldERhdGUoKTsNCiAgICAgIGNvbnN0IGhvdXIgPSBub3cuZ2V0SG91cnMoKTsNCg0KICAgICAgLy8g5aaC5p6c5LuK5aSp5piv5pys5pyIMjXlj7c454K55YmN77yI5ZCrMjXlj7c3OjU577yJ77yM5YiZ55So5LiK5Liq5pyIDQogICAgICBpZiAoZGF5IDwgMjggfHwgKGRheSA9PT0gMjggJiYgaG91ciA8IDEpKSB7DQogICAgICAgIC8vIOWkhOeQhjHmnIjml7bnmoTot6jlubQNCiAgICAgICAgY29uc3QgcHJldk1vbnRoID0gbW9udGggPT09IDEgPyAxMiA6IG1vbnRoIC0gMTsNCiAgICAgICAgY29uc3QgcHJldlllYXIgPSBtb250aCA9PT0gMSA/IHllYXIgLSAxIDogeWVhcjsNCiAgICAgICAgcmV0dXJuIGAke3ByZXZZZWFyfS0ke1N0cmluZyhwcmV2TW9udGgpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBgJHt5ZWFyfS0ke1N0cmluZyhtb250aCkucGFkU3RhcnQoMiwgJzAnKX1gOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5pWw5o2uDQogICAgICBpZiAoIXRoaXMucXVhbGl0eUNvc3RMaXN0IHx8IHRoaXMucXVhbGl0eUNvc3RMaXN0Lmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aaguaXoOaVsOaNruWPr+WvvOWHuu+8jOivt+WFiOafpeivouaVsOaNricpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOaehOW7uuafpeivouWPguaVsO+8jOS4juafpeivouaVsOaNruaXtuS/neaMgeS4gOiHtOeahOagvOW8jw0KICAgICAgbGV0IHF1ZXJ5UGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5xdWVyeVBhcmFtcy55ZWFyTW9udGgucmVwbGFjZSgnLScsICcnKSwgLy8g5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgICB0eXBlQ29kZUxpc3Q6IHRoaXMucXVlcnlQYXJhbXMuY29zdFR5cGUsDQogICAgICAgIGNvc3RUb246IHRoaXMuZm9ybWF0UHJvZHVjdGlvbih0aGlzLnByb2R1Y3Rpb25JbmZvLmNvc3RUb24pDQogICAgICB9Ow0KDQoNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuWFtOa+hOeJuemSoui0qOmHj+aIkOacrOihqOaVsOaNrj8nLCAi6K2m5ZGKIiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICByZXR1cm4gZXhwb3J0UXVhbGl0eUNvc3RQYWdlKHF1ZXJ5UGFyYW1zKTsNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0NCg0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8JA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/qualityCostPage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\" style=\"height: auto !important; min-height: auto !important; max-height: none !important;\">\r\n    <!-- 顶部筛选区域 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"成本中心\" prop=\"costCenter\">\r\n        <el-select v-model=\"queryParams.costCenter\" placeholder=\"请选择成本中心\" clearable size=\"small\" style=\"width: 200px;\">\r\n          <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"会计期\" prop=\"yearMonth\">\r\n        <el-date-picker v-model=\"queryParams.yearMonth\" type=\"month\" placeholder=\"请选择年月\" format=\"yyyy-MM\"\r\n          value-format=\"yyyy-MM\" size=\"small\" style=\"width: 200px;\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"成本类型\" prop=\"costType\">\r\n        <el-select v-model=\"queryParams.costType\" placeholder=\"请选择成本类型\" multiple clearable size=\"small\"\r\n          style=\"width: 300px;\" @change=\"handleCostTypeChange\">\r\n          <el-option v-for=\"item in costTypeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\">导出</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 质量成本表格 -->\r\n    <div v-if=\"qualityCostList.length > 0\"\r\n      style=\"width: 100%; height: 80vh; overflow: visible; margin: 0; padding: 0; display: flex; flex-direction: column; align-items: center;\">\r\n      <!-- 标题和产量信息 -->\r\n      <div style=\"position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;\">\r\n        <h3 style=\"text-align: center; margin: 0;\">兴澄特钢质量成本表</h3>\r\n        <div style=\"position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;\">\r\n          {{ getProductionLabel() }}：{{ productionInfo ? formatProduction(productionInfo.costTon) + '吨' : '无' }}\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"overflow-x: auto; width: 95%; height: calc(100% - 80px); margin: 0; padding: 0; border: none;\">\r\n        <vxe-table\r\n          :loading=\"loading\"\r\n          :data=\"qualityCostList\"\r\n          border\r\n          height=\"100%\"\r\n          style=\"table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;\"\r\n          :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#303133' }\"\r\n          :merge-cells=\"mergeCells\"\r\n          stripe\r\n          :auto-resize=\"false\"\r\n          :scroll-y=\"{enabled: true}\"\r\n          :scroll-x=\"{enabled: true}\">\r\n          <vxe-column title=\"成本类别\" align=\"center\" field=\"costType\" width=\"5%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                {{ row.costType }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"科目\" field=\"typeName\" width=\"15%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{\r\n                  fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal',\r\n                  textAlign: (row.typeName && row.typeName.includes('小计')) ? 'center' : 'left',\r\n                  display: 'block'\r\n                }\">\r\n                {{ row.typeName }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <!-- 质量成本分组 -->\r\n          <vxe-colgroup title=\"质量成本（列入项）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"costEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.costEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"costPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.costPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 不列入项分组 -->\r\n          <vxe-colgroup title=\"质量成本（不列入项）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"nincEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.nincEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"nincPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.nincPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 质量成本（含不列入项）分组 -->\r\n          <vxe-colgroup title=\"质量成本（总）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"allcEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.allcEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"allcPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.allcPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 金额百分比列 -->\r\n          <vxe-column title=\"金额百分比\" align=\"center\" field=\"amountPercent\" width=\"8%\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                {{ formatPercentage(row.amountPercent) }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据提示 -->\r\n    <div v-else-if=\"!loading\" style=\"text-align: center; padding: 50px;\">\r\n      <el-empty description=\"请选择查询条件后点击搜索查看数据\"></el-empty>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listQualityCostPage, costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { exportQualityCostPage } from \"@/api/qualityCost/qualityCostPage\";\r\n\r\nexport default {\r\n  name: \"QualityCost\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 质量成本数据列表\r\n      qualityCostList: [],\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      // 产量信息\r\n      productionInfo: null,\r\n      // 合并单元格配置\r\n      mergeCells: [],\r\n      // 成本类型选项\r\n      costTypeOptions: [\r\n        { value: '', label: '全部' },\r\n        { value: 'A', label: 'A-预防成本' },\r\n        { value: 'B', label: 'B-鉴定成本' },\r\n        { value: 'C', label: 'C-内部损失成本' },\r\n        { value: 'D', label: 'D-外部损失成本' }\r\n      ],\r\n      // 查询参数\r\n      queryParams: {\r\n        costCenter: 'JYXCTZG', // 默认选中兴澄特钢汇总\r\n        yearMonth: null,\r\n        costType: ['']\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n    this.getCostCenterList();\r\n    // 页面加载时自动触发搜索\r\n    this.$nextTick(() => {\r\n      this.handleQuery();\r\n    });\r\n  },\r\n  methods: {\r\n    /** 获取成本中心列表 */\r\n    getCostCenterList() {\r\n      costCenterlist().then(response => {\r\n        const options = response.data || [];\r\n        // 将JYXCTZG选项排在第一位\r\n        const jyxctzgOption = options.find(item => item.key === 'JYXCTZG');\r\n        const otherOptions = options.filter(item => item.key !== 'JYXCTZG');\r\n\r\n        if (jyxctzgOption) {\r\n          this.costCenterOptions = [jyxctzgOption, ...otherOptions];\r\n        } else {\r\n          this.costCenterOptions = options;\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取成本中心列表失败');\r\n      });\r\n    },\r\n\r\n    /** 获取成本类型 */\r\n    getCostType(typeCode) {\r\n      if (!typeCode) return '';\r\n      const firstChar = typeCode.charAt(0).toUpperCase();\r\n      switch (firstChar) {\r\n        case 'A': return '预防成本';\r\n        case 'B': return '鉴定成本';\r\n        case 'C': return '内部损失成本';\r\n        case 'D': return '外部损失成本';\r\n        default: return '汇总';\r\n      }\r\n    },\r\n\r\n    /** 计算合并单元格 */\r\n    calculateMergeCells() {\r\n      const mergeCells = [];\r\n      const costTypeGroups = {};\r\n\r\n      // 按成本类型分组\r\n      this.qualityCostList.forEach((row, index) => {\r\n        const costType = row.costType;\r\n        if (!costTypeGroups[costType]) {\r\n          costTypeGroups[costType] = [];\r\n        }\r\n        costTypeGroups[costType].push(index);\r\n      });\r\n\r\n      // 生成合并配置\r\n      Object.values(costTypeGroups).forEach(group => {\r\n        if (group.length > 1) {\r\n          mergeCells.push({\r\n            row: group[0],\r\n            col: 0,\r\n            rowspan: group.length,\r\n            colspan: 1\r\n          });\r\n        }\r\n      });\r\n\r\n      this.mergeCells = mergeCells;\r\n    },\r\n\r\n    /** 格式化数字 */\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化百分比 */\r\n    formatPercentage(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return (Number(num) * 100).toFixed(3) + '%';\r\n    },\r\n\r\n    /** 格式化货币 */\r\n    formatCurrency(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化产量/销量 */\r\n    formatProduction(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toFixed(3);\r\n    },\r\n\r\n    /** 成本类型变化处理 */\r\n    handleCostTypeChange(value) {\r\n      // 如果选择了\"全部\"，清空其他选项\r\n      if (value.includes('')) {\r\n        if (value.length > 1) {\r\n          // 如果同时选择了\"全部\"和其他选项，只保留\"全部\"\r\n          this.queryParams.costType = [''];\r\n        }\r\n      } else {\r\n        // 如果没有选择\"全部\"，确保\"全部\"不在选择列表中\r\n        this.queryParams.costType = value.filter(item => item !== '');\r\n      }\r\n    },\r\n\r\n    /** 获取产量标签 */\r\n    getProductionLabel() {\r\n      if (this.queryParams.costCenter === 'JYXCTZG') {\r\n        return '销量';\r\n      } else {\r\n        return '产量';\r\n      }\r\n    },\r\n\r\n    /** 查询质量成本列表 */\r\n    getList() {\r\n      // 验证必填参数\r\n      if (!this.queryParams.yearMonth) {\r\n        this.$message.warning('请选择会计期');\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n\r\n      // 处理日期格式：将 2025-06 转换为 202506\r\n      const formatYearMonth = this.queryParams.yearMonth.replace('-', '');\r\n\r\n      // 处理成本类型参数\r\n      let typeCodeList = [];\r\n      if (this.queryParams.costType && this.queryParams.costType.length > 0) {\r\n        // 如果选择了\"全部\"或数组为空，传递空数组\r\n        if (this.queryParams.costType.includes('') || this.queryParams.costType.length === 0) {\r\n          typeCodeList = [];\r\n        } else {\r\n          typeCodeList = this.queryParams.costType;\r\n        }\r\n      }\r\n\r\n      const requestParams = {\r\n        costCenter: this.queryParams.costCenter || '',\r\n        yearMonth: formatYearMonth,\r\n        typeCodeList: typeCodeList\r\n      };\r\n\r\n      listQualityCostPage(requestParams).then(response => {\r\n        const data = response.data || {};\r\n\r\n        // 从返回的QualityCostMonthlyVO对象中获取数据\r\n        let qualityCostDetailList = data.qualityCostDetailList || [];\r\n\r\n        // 统一使用typeCode为'Z'的数据作为产量/销量信息\r\n        const productionData = qualityCostDetailList.find(item => item.typeCode === 'Z');\r\n        this.productionInfo = productionData || null;\r\n\r\n\r\n\r\n        // 为每一行添加成本类别信息，用于前端表格显示和合并\r\n        qualityCostDetailList.forEach(row => {\r\n          row.costType = this.getCostType(row.typeCode);\r\n        });\r\n\r\n        this.qualityCostList = qualityCostDetailList;\r\n\r\n        // 计算合并单元格\r\n        this.calculateMergeCells();\r\n\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n        this.$message.error('查询失败');\r\n        this.qualityCostList = [];\r\n        this.productionInfo = null;\r\n        this.mergeCells = [];\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      // 重置为默认值\r\n      this.queryParams.costCenter = 'JYXCTZG'; // 默认选中兴澄特钢汇总\r\n      this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n      this.queryParams.costType = [''];\r\n      // 清空数据\r\n      this.qualityCostList = [];\r\n      this.productionInfo = null;\r\n      this.mergeCells = [];\r\n    },\r\n\r\n    /** 获取默认会计期（上个月） */\r\n    getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 检查是否有数据\r\n      if (!this.qualityCostList || this.qualityCostList.length === 0) {\r\n        this.$message.warning('暂无数据可导出，请先查询数据');\r\n        return;\r\n      }\r\n\r\n      // 构建查询参数，与查询数据时保持一致的格式\r\n      let queryParams = {\r\n        costCenter: this.queryParams.costCenter,\r\n        yearMonth: this.queryParams.yearMonth.replace('-', ''), // 将 2025-06 转换为 202506\r\n        typeCodeList: this.queryParams.costType,\r\n        costTon: this.formatProduction(this.productionInfo.costTon)\r\n      };\r\n\r\n\r\n      this.$confirm('是否确认导出兴澄特钢质量成本表数据?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.loading = true;\r\n        return exportQualityCostPage(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    }\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 重置所有可能的高度约束 */\r\n*,\r\n*::before,\r\n*::after {\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 容器样式 */\r\n.app-container {\r\n  overflow: visible !important;\r\n  height: auto !important;\r\n  min-height: auto !important;\r\n  max-height: none !important;\r\n  padding: 20px !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.vxe-table ::v-deep .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-footer--column {\r\n  background-color: #fafafa;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 筛选区域样式 */\r\n.el-form--inline .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.vxe-table .vxe-cell {\r\n  padding: 0 5px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 主要表格样式 */\r\n.vxe-table {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 固定列样式 - 确保固定列正常显示 */\r\n.vxe-table ::v-deep .vxe-table--fixed-left-wrapper {\r\n  z-index: 10;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--fixed-right-wrapper {\r\n  z-index: 10;\r\n}\r\n\r\n/* 汇总表格合计行样式 */\r\n.vxe-table ::v-deep .summary-row {\r\n  background-color: #f8f9fa;\r\n  font-weight: bold;\r\n}\r\n\r\n.vxe-table ::v-deep .summary-row .vxe-cell {\r\n  background-color: #f8f9fa !important;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 自定义滚动条样式 - 蓝色加粗 */\r\n/* vxe-table 内部滚动条样式 */\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-track,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f1f1 !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-thumb,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-thumb {\r\n  background: #1890ff !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f1f1f1 !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-thumb:hover,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #096dd9 !important;\r\n}\r\n\r\n/* 表格容器滚动条样式 */\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-track {\r\n  background: #f1f1f1 !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-thumb {\r\n  background: #1890ff !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f1f1f1 !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-thumb:hover {\r\n  background: #096dd9 !important;\r\n}\r\n\r\n/* 通用滚动条样式 - 更高优先级 */\r\n.vxe-table ::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-track {\r\n  background: #f1f1f1 !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #1890ff !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f1f1f1 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #096dd9 !important;\r\n}\r\n</style>\r\n"]}]}