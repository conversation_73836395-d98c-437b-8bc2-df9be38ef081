<template>
  <div class="app-container" style="height: auto !important; min-height: auto !important; max-height: none !important;">
    <!-- 顶部筛选区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="成本中心" prop="costCenter">
        <el-select v-model="queryParams.costCenter" placeholder="请选择成本中心" clearable size="small" style="width: 200px;">
          <el-option v-for="item in costCenterOptions" :key="item.key" :label="item.label" :value="item.key">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="会计期" prop="yearMonth">
        <el-date-picker v-model="queryParams.yearMonth" type="month" placeholder="请选择年月" format="yyyy-MM"
          value-format="yyyy-MM" size="small" style="width: 200px;">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="成本类型" prop="costType">
        <el-select v-model="queryParams.costType" placeholder="请选择成本类型" multiple clearable size="small"
          style="width: 300px;" @change="handleCostTypeChange">
          <el-option v-for="item in costTypeOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!-- 质量成本表格 -->
    <div v-if="qualityCostList.length > 0"
      style="width: 100%; height: 80vh; overflow: visible; margin: 0; padding: 0; display: flex; flex-direction: column; align-items: center;">
      <!-- 标题和产量信息 -->
      <div style="position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;">
        <h3 style="text-align: center; margin: 0;">兴澄特钢质量成本表</h3>
        <div style="position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;">
          <span style="margin-right: 20px;">单位：元</span>
          {{ getProductionLabel() }}：{{ productionInfo ? formatProduction(productionInfo.costTon) + '吨' : '无' }}
        </div>
      </div>

      <div style="overflow-x: auto; width: 95%; max-height: calc(100% - 80px); margin: 0; padding: 0; border: none;">
        <vxe-table
          :loading="loading"
          :data="qualityCostList"
          border
          :height="tableHeight"
          :class="{ 'no-scroll': !needScrollbar }"
          style="table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;"
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#303133' }"
          :merge-cells="mergeCells"
          stripe
          :auto-resize="false"
          :scroll-y="{enabled: needScrollbar}"
          :scroll-x="{enabled: true}">
          <vxe-column title="成本类别" align="center" field="costType" width="5%" fixed="left">
            <template #default="{ row }">
              <span
                :style="{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }">
                {{ row.costType }}
              </span>
            </template>
          </vxe-column>
          <vxe-column title="科目" field="typeName" width="15%" fixed="left">
            <template #default="{ row }">
              <span
                :style="{
                  fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal',
                  textAlign: (row.typeName && row.typeName.includes('小计')) ? 'center' : 'left',
                  display: 'block'
                }">
                {{ row.typeName }}
              </span>
            </template>
          </vxe-column>

          <!-- 质量成本分组 -->
          <vxe-colgroup title="质量成本（列入项）" align="center">
            <vxe-column title="金额（元）" align="center" field="costEx" width="12%">
              <template #default="{ row }">
                <span
                  :style="{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }">
                  {{ formatCurrency(row.costEx) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨钢金额（元/吨）" align="center" field="costPerEx" width="12%">
              <template #default="{ row }">
                <span
                  :style="{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }">
                  {{ formatCurrency(row.costPerEx) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 不列入项分组 -->
          <vxe-colgroup title="质量成本（不列入项）" align="center">
            <vxe-column title="金额（元）" align="center" field="nincEx" width="12%">
              <template #default="{ row }">
                <span
                  :style="{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }">
                  {{ formatCurrency(row.nincEx) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨钢金额（元/吨）" align="center" field="nincPerEx" width="12%">
              <template #default="{ row }">
                <span
                  :style="{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }">
                  {{ formatCurrency(row.nincPerEx) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 质量成本（含不列入项）分组 -->
          <vxe-colgroup title="质量成本（总）" align="center">
            <vxe-column title="金额（元）" align="center" field="allcEx" width="12%">
              <template #default="{ row }">
                <span
                  :style="{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }">
                  {{ formatCurrency(row.allcEx) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨钢金额（元/吨）" align="center" field="allcPerEx" width="12%">
              <template #default="{ row }">
                <span
                  :style="{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }">
                  {{ formatCurrency(row.allcPerEx) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 金额百分比列 -->
          <vxe-column title="金额百分比" align="center" field="amountPercent" width="8%">
            <template #default="{ row }">
              <span
                :style="{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }">
                {{ formatPercentage(row.amountPercent) }}
              </span>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-else-if="!loading" style="text-align: center; padding: 50px;">
      <el-empty description="请选择查询条件后点击搜索查看数据"></el-empty>
    </div>
  </div>
</template>

<script>
import { listQualityCostPage, costCenterlist } from "@/api/qualityCost/qualityCostDetail";
import { exportQualityCostPage } from "@/api/qualityCost/qualityCostPage";

export default {
  name: "QualityCost",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 质量成本数据列表
      qualityCostList: [],
      // 成本中心选项
      costCenterOptions: [],
      // 产量信息
      productionInfo: null,
      // 合并单元格配置
      mergeCells: [],
      // 成本类型选项
      costTypeOptions: [
        { value: '', label: '全部' },
        { value: 'A', label: 'A-预防成本' },
        { value: 'B', label: 'B-鉴定成本' },
        { value: 'C', label: 'C-内部损失成本' },
        { value: 'D', label: 'D-外部损失成本' }
      ],
      // 查询参数
      queryParams: {
        costCenter: 'JYXCTZG', // 默认选中兴澄特钢汇总
        yearMonth: null,
        costType: ['']
      }
    };
  },
  computed: {
    /** 判断是否显示汇总行 */
    shouldShowSummaryRows() {
      // 当成本类型选择为"全部"时显示汇总行
      return this.queryParams.costType.includes('') || this.queryParams.costType.length === 0;
    },
    /** 计算表格高度 */
    tableHeight() {
      if (!this.qualityCostList || this.qualityCostList.length === 0) {
        return 'auto';
      }

      // 当成本类型选择非全部时，使用auto高度，不限制最大高度
      if (!this.shouldShowSummaryRows) {
        return 'auto';
      }

      // 计算所需高度：表头高度 + 数据行高度
      const headerHeight = 40; // 表头高度
      const rowHeight = 32; // 每行数据高度
      const padding = 10; // 额外间距

      const dataRowsCount = this.qualityCostList.length;
      const calculatedHeight = headerHeight + (dataRowsCount * rowHeight) + padding;

      // 最大高度限制（80vh - 80px）
      const maxHeight = window.innerHeight * 0.8 - 80;

      // 返回计算高度和最大高度中的较小值
      return Math.min(calculatedHeight, maxHeight) + 'px';
    },
    /** 判断是否需要显示滚动条 */
    needScrollbar() {
      // 当成本类型选择非全部时，不显示滚动条
      if (!this.shouldShowSummaryRows) {
        return false;
      }

      if (!this.qualityCostList || this.qualityCostList.length === 0) {
        return false;
      }

      const headerHeight = 40;
      const rowHeight = 32;
      const padding = 10;
      const dataRowsCount = this.qualityCostList.length;
      const calculatedHeight = headerHeight + (dataRowsCount * rowHeight) + padding;
      const maxHeight = window.innerHeight * 0.8 - 80;

      return calculatedHeight > maxHeight;
    }
  },
  created() {
    this.queryParams.yearMonth = this.getDefaultYearMonth();
    this.getCostCenterList();
    // 页面加载时自动触发搜索
    this.$nextTick(() => {
      this.handleQuery();
    });
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    /** 获取成本中心列表 */
    getCostCenterList() {
      costCenterlist().then(response => {
        const options = response.data || [];
        // 将JYXCTZG选项排在第一位
        const jyxctzgOption = options.find(item => item.key === 'JYXCTZG');
        const otherOptions = options.filter(item => item.key !== 'JYXCTZG');

        if (jyxctzgOption) {
          this.costCenterOptions = [jyxctzgOption, ...otherOptions];
        } else {
          this.costCenterOptions = options;
        }
      }).catch(() => {
        this.$message.error('获取成本中心列表失败');
      });
    },

    /** 获取成本类型 */
    getCostType(typeCode) {
      if (!typeCode) return '';
      const firstChar = typeCode.charAt(0).toUpperCase();
      switch (firstChar) {
        case 'A': return '预防成本';
        case 'B': return '鉴定成本';
        case 'C': return '内部损失成本';
        case 'D': return '外部损失成本';
        default: return '汇总';
      }
    },

    /** 计算合并单元格 */
    calculateMergeCells() {
      const mergeCells = [];
      const costTypeGroups = {};

      // 按成本类型分组
      this.qualityCostList.forEach((row, index) => {
        const costType = row.costType;
        if (!costTypeGroups[costType]) {
          costTypeGroups[costType] = [];
        }
        costTypeGroups[costType].push(index);
      });

      // 生成合并配置
      Object.values(costTypeGroups).forEach(group => {
        if (group.length > 1) {
          mergeCells.push({
            row: group[0],
            col: 0,
            rowspan: group.length,
            colspan: 1
          });
        }
      });

      this.mergeCells = mergeCells;
    },

    /** 格式化数字 */
    formatNumber(num) {
      if (num === null || num === undefined || num === '' || num === 0) {
        return '-';
      }
      return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    /** 格式化百分比 */
    formatPercentage(num) {
      if (num === null || num === undefined || num === '' || num === 0) {
        return '-';
      }
      return (Number(num) * 100).toFixed(3) + '%';
    },

    /** 格式化货币 */
    formatCurrency(num) {
      if (num === null || num === undefined || num === '' || num === 0) {
        return '-';
      }
      return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    /** 格式化产量/销量 */
    formatProduction(num) {
      if (num === null || num === undefined || num === '' || num === 0) {
        return '-';
      }
      return Number(num).toFixed(3);
    },

    /** 成本类型变化处理 */
    handleCostTypeChange(value) {
      // 如果选择了"全部"，清空其他选项
      if (value.includes('')) {
        if (value.length > 1) {
          // 如果同时选择了"全部"和其他选项，只保留"全部"
          this.queryParams.costType = [''];
        }
      } else {
        // 如果没有选择"全部"，确保"全部"不在选择列表中
        this.queryParams.costType = value.filter(item => item !== '');
      }
    },

    /** 获取产量标签 */
    getProductionLabel() {
      if (this.queryParams.costCenter === 'JYXCTZG') {
        return '销量';
      } else {
        return '产量';
      }
    },

    /** 查询质量成本列表 */
    getList() {
      // 验证必填参数
      if (!this.queryParams.yearMonth) {
        this.$message.warning('请选择会计期');
        return;
      }

      this.loading = true;

      // 处理日期格式：将 2025-06 转换为 202506
      const formatYearMonth = this.queryParams.yearMonth.replace('-', '');

      // 处理成本类型参数
      let typeCodeList = [];
      if (this.queryParams.costType && this.queryParams.costType.length > 0) {
        // 如果选择了"全部"或数组为空，传递空数组
        if (this.queryParams.costType.includes('') || this.queryParams.costType.length === 0) {
          typeCodeList = [];
        } else {
          typeCodeList = this.queryParams.costType;
        }
      }

      const requestParams = {
        costCenter: this.queryParams.costCenter || '',
        yearMonth: formatYearMonth,
        typeCodeList: typeCodeList
      };

      listQualityCostPage(requestParams).then(response => {
        const data = response.data || {};

        // 从返回的QualityCostMonthlyVO对象中获取数据
        let qualityCostDetailList = data.qualityCostDetailList || [];

        // 统一使用typeCode为'Z'的数据作为产量/销量信息
        const productionData = qualityCostDetailList.find(item => item.typeCode === 'Z');
        this.productionInfo = productionData || null;



        // 为每一行添加成本类别信息，用于前端表格显示和合并
        qualityCostDetailList.forEach(row => {
          row.costType = this.getCostType(row.typeCode);
        });

        // 根据成本类型选择过滤汇总行
        if (!this.shouldShowSummaryRows) {
          // 过滤掉包含"合计"、"小计"、"汇总"的行
          qualityCostDetailList = qualityCostDetailList.filter(row => {
            return !(row.typeName && (
              row.typeName.includes('合计') ||
              row.typeName.includes('小计') ||
              row.typeName.includes('汇总')
            ));
          });
        }

        this.qualityCostList = qualityCostDetailList;

        // 计算合并单元格
        this.calculateMergeCells();

        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.$message.error('查询失败');
        this.qualityCostList = [];
        this.productionInfo = null;
        this.mergeCells = [];
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 重置为默认值
      this.queryParams.costCenter = 'JYXCTZG'; // 默认选中兴澄特钢汇总
      this.queryParams.yearMonth = this.getDefaultYearMonth();
      this.queryParams.costType = [''];
      // 清空数据
      this.qualityCostList = [];
      this.productionInfo = null;
      this.mergeCells = [];
    },

    /** 获取默认会计期（上个月） */
    getDefaultYearMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1; // 1-12
      const day = now.getDate();
      const hour = now.getHours();

      // 如果今天是本月25号8点前（含25号7:59），则用上个月
      if (day < 28 || (day === 28 && hour < 1)) {
        // 处理1月时的跨年
        const prevMonth = month === 1 ? 12 : month - 1;
        const prevYear = month === 1 ? year - 1 : year;
        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;
      } else {
        return `${year}-${String(month).padStart(2, '0')}`;
      }
    },

    /** 处理窗口大小变化 */
    handleResize() {
      // 强制重新计算表格高度
      this.$forceUpdate();
    },

    /** 导出按钮操作 */
    handleExport() {
      // 检查是否有数据
      if (!this.qualityCostList || this.qualityCostList.length === 0) {
        this.$message.warning('暂无数据可导出，请先查询数据');
        return;
      }

      // 构建查询参数，与查询数据时保持一致的格式
      let queryParams = {
        costCenter: this.queryParams.costCenter,
        yearMonth: this.queryParams.yearMonth.replace('-', ''), // 将 2025-06 转换为 202506
        typeCodeList: this.queryParams.costType,
        costTon: this.formatProduction(this.productionInfo.costTon)
      };


      this.$confirm('是否确认导出兴澄特钢质量成本表数据?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        return exportQualityCostPage(queryParams);
      }).then(response => {
        this.download(response.msg);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }

  }
};
</script>

<style scoped>
/* 重置所有可能的高度约束 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 容器样式 */
.app-container {
  overflow: visible !important;
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  padding: 20px !important;
  margin: 0 !important;
}

/* 表格样式优化 */
.vxe-table ::v-deep .vxe-header--column {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: bold;
}

.vxe-table ::v-deep .vxe-footer--column {
  background-color: #fafafa;
  font-weight: bold;
}

/* 筛选区域样式 */
.el-form--inline .el-form-item {
  margin-bottom: 10px;
}

.vxe-table .vxe-cell {
  padding: 0 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 主要表格样式 */
.vxe-table {
  font-size: 12px;
}

/* 固定列样式 - 确保固定列正常显示 */
.vxe-table ::v-deep .vxe-table--fixed-left-wrapper {
  z-index: 10;
}

.vxe-table ::v-deep .vxe-table--fixed-right-wrapper {
  z-index: 10;
}

/* 汇总表格合计行样式 */
.vxe-table ::v-deep .summary-row {
  background-color: #f8f9fa;
  font-weight: bold;
}

.vxe-table ::v-deep .summary-row .vxe-cell {
  background-color: #f8f9fa !important;
  font-weight: bold;
}

/* 自定义滚动条样式 - 浅蓝色加粗 */
/* vxe-table 内部滚动条样式 */
.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,
.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar,
.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar {
  width: 24px !important;
  height: 24px !important;
  z-index: 999 !important;
}

/* 当不需要滚动时隐藏所有滚动条 */
.vxe-table.no-scroll ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,
.vxe-table.no-scroll ::v-deep .vxe-body--wrapper::-webkit-scrollbar,
.vxe-table.no-scroll ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

/* 当不需要滚动时，确保表格内容完全显示 */
.vxe-table.no-scroll ::v-deep .vxe-table--body-wrapper,
.vxe-table.no-scroll ::v-deep .vxe-body--wrapper,
.vxe-table.no-scroll ::v-deep .vxe-table--main-wrapper {
  overflow: visible !important;
}

.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-track,
.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-track,
.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-track {
  background: #f0f8ff !important;
  border-radius: 12px !important;
}

.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb,
.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-thumb,
.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-thumb {
  background: #87ceeb !important;
  border-radius: 12px !important;
  border: 2px solid #f0f8ff !important;
}

.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,
.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-thumb:hover,
.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-thumb:hover {
  background: #4682b4 !important;
}

/* 表格容器滚动条样式 */
div[style*="overflow-x: auto"]::-webkit-scrollbar {
  width: 24px !important;
  height: 24px !important;
  z-index: 999 !important;
}

div[style*="overflow-x: auto"]::-webkit-scrollbar-track {
  background: #f0f8ff !important;
  border-radius: 12px !important;
}

div[style*="overflow-x: auto"]::-webkit-scrollbar-thumb {
  background: #87ceeb !important;
  border-radius: 12px !important;
  border: 2px solid #f0f8ff !important;
}

div[style*="overflow-x: auto"]::-webkit-scrollbar-thumb:hover {
  background: #4682b4 !important;
}

/* 通用滚动条样式 - 更高优先级 */
.vxe-table ::-webkit-scrollbar {
  width: 24px !important;
  height: 24px !important;
  z-index: 1000 !important;
}

.vxe-table ::-webkit-scrollbar-track {
  background: #f0f8ff !important;
  border-radius: 12px !important;
}

.vxe-table ::-webkit-scrollbar-thumb {
  background: #87ceeb !important;
  border-radius: 12px !important;
  border: 2px solid #f0f8ff !important;
}

.vxe-table ::-webkit-scrollbar-thumb:hover {
  background: #4682b4 !important;
}

/* 滚动条容器层级调整 */
.vxe-table ::v-deep .vxe-table--body-wrapper,
.vxe-table ::v-deep .vxe-body--wrapper,
.vxe-table ::v-deep .vxe-table--main-wrapper {
  position: relative !important;
  z-index: 1 !important;
}
</style>
