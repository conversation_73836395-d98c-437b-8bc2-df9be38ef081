{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\qualityCostPage\\index.vue?vue&type=template&id=34153b51&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\qualityCostPage\\index.vue", "mtime": 1754382562899}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}