{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue", "mtime": 1754372909829}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_qualityCostDetail", "_dashboard", "name", "data", "getDefaultYearMonth", "now", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "hour", "getHours", "prevMonth", "prevYear", "concat", "String", "padStart", "updateTime", "charts", "costCenter", "accountingPeriod", "containType", "costCenterOptions", "costCenterLoading", "qualityCostDetail", "qualityCostData", "watch", "handler", "console", "log", "refreshChartData", "mounted", "_this", "getCostCenterList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resizeObserver", "ResizeObserver", "resi<PERSON><PERSON><PERSON><PERSON>", "observe", "$el", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "Object", "values", "for<PERSON>ach", "chart", "dispose", "disconnect", "removeEventListener", "methods", "isNegativePercentage", "percentage", "toString", "startsWith", "getPercentageClass", "formatNumber", "num", "undefined", "number", "Number", "isNaN", "parseFloat", "toFixed", "addThousandSeparator", "replace", "formatTonnage", "result", "formatAmount", "formatUnitCost", "getWaterfallChartDetail", "_this2", "params", "yearMonth", "then", "response", "updateWaterfallChart", "catch", "error", "$message", "<PERSON><PERSON>hart", "xAxisData", "seriesData", "colors", "dataItems", "rescueProject", "entries", "map", "_ref", "_ref2", "_slicedToArray2", "default", "key", "value", "length", "sort", "a", "b", "topTenItems", "slice", "item", "index", "push", "itemStyle", "color", "warn", "option", "grid", "left", "right", "bottom", "containLabel", "tooltip", "trigger", "axisPointer", "type", "backgroundColor", "borderColor", "textStyle", "formatter", "formattedValue", "marker", "seriesName", "xAxis", "axisLabel", "interval", "rotate", "align", "axisLine", "lineStyle", "yAxis", "nameTextStyle", "splitLine", "series", "setOption", "updateScrapLossChart", "scrapLossChart", "_typeof2", "keys", "scrapLossMap", "_ref3", "_ref4", "_ref5", "_ref6", "scrapLoss", "_ref7", "_ref8", "scrapLossProject", "_ref9", "_ref0", "_ref1", "_ref10", "updateQualityObjectionChart", "qualityObjectionChart", "qualityObjectionLossMap", "_ref11", "_ref12", "_ref13", "_ref14", "_ref15", "_ref16", "getQualityObjectionLossDetail", "_this3", "getScrapLossChartDetailsDetail", "_this4", "getExternalCostDetail", "_this5", "updateExternalCostDetailChart", "getInternalCostDetail", "_this6", "updateInternalCostDetailChart", "internalCostDetailChart", "allDataItems", "contraction<PERSON>oss", "_ref17", "_ref18", "numValue", "rescueCost", "_ref19", "_ref20", "revisionLoss", "_ref21", "_ref22", "_ref23", "_ref24", "yAxisData", "reverse", "externalCostDetailChart", "customerClaimCost", "_ref25", "_ref26", "qualityObjectionFeeCost", "_ref27", "_ref28", "qualityObjectionTravelCost", "_ref29", "_ref30", "returnLoss", "_ref31", "_ref32", "getQualityCostDetail", "_this7", "getMultiLineChartData", "_this8", "updateMultiLineChart", "getComboChartDetail", "_this9", "updateComboChart", "comboChart", "months", "generateComboChartMonthsByAccountingPeriod", "yearMonths", "generateYearMonthsByAccountingPeriod", "failureCostData", "controllingCostData", "failureValue", "failureCostMap", "controllingValue", "controllingCostMap", "legend", "boundaryGap", "smooth", "width", "symbol", "symbolSize", "generateComboChartMonths", "currentDate", "i", "date", "generateYearMonths", "_this$accountingPerio", "split", "_this$accountingPerio2", "accountingDate", "monthNum", "_this$accountingPerio3", "_this$accountingPerio4", "yearNum", "getPieChartData", "_this0", "update<PERSON>ie<PERSON>hart", "_this1", "costCenterlist", "$nextTick", "finally", "<PERSON><PERSON><PERSON>", "getOption", "preventionCost", "appraisalCost", "internalCost", "externalCost", "multiLineChart", "preventionData", "processMapData", "preventionCostMap", "appraisalData", "appraisalCostMap", "internalData", "internalCostMap", "externalData", "externalCostMap", "costMap", "convertToWanYuan", "arguments", "Array", "fill", "generateMonthLabels", "parseInt", "handleQuery", "reset<PERSON><PERSON>y", "success", "THEME", "businessColors", "light", "gradient", "offset", "init", "$refs", "setPieChartOption", "setMultiLineChartOption", "setExternalCostDetailChartOption", "setInternalCostDetailChartOption", "setWaterfallChartOption", "setComboChartOption", "setScrapLossChartOption", "setQualityObjectionChartOption", "percent", "top", "fontSize", "radius", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "labelLine", "label", "setParetoChartOption", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "yAxisIndex", "stack", "borderWidth", "setDualYChartOption", "dualYChart", "resize"], "sources": ["src/views/qualityCost/dashboard/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"quality-cost-dashboard\">\r\n    <header class=\"header\">\r\n      <div class=\"header-wrapper\">\r\n        <h1>兴澄特钢质量成本看板</h1>\r\n        <!-- 标题右下角筛选区域 -->\r\n        <div class=\"header-filters\">\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">成本中心：</span>\r\n            <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\"\r\n              size=\"small\">\r\n              <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">会计期：</span>\r\n            <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"请选择年月\" format=\"yyyy-MM\"\r\n              value-format=\"yyyy-MM\" style=\"width: 130px;\" size=\"small\">\r\n            </el-date-picker>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">质量成本类型：</span>\r\n            <el-select v-model=\"containType\" placeholder=\"请选择质量成本类型\" style=\"width: 130px;\" size=\"small\">\r\n              <el-option label=\"含不列入项\" :value=\"2\"></el-option>\r\n              <el-option label=\"不含列入项\" :value=\"1\"></el-option>\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <p>数据更新时间: {{ updateTime }}</p> -->\r\n    </header>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第四类：核心绩效指标（KPI）看板 -->\r\n      <div class=\"kpi-grid\">\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">{{ costCenter === 'JYXCTZG' ? '销量' : '产量' }}</div>\r\n          <div class=\"value\">{{ formatTonnage(containType === 2 ? qualityCostData.allcTon : qualityCostData.costTon) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costTonUpPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costTonUpPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costTonUpPercent)\">{{ qualityCostDetail.costTonUpPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">总金额</div>\r\n          <div class=\"value\">{{ formatAmount(containType === 2 ? qualityCostData.allcEx : qualityCostData.costEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costExPercent)\">{{ qualityCostDetail.costExPercent }} vs\r\n              上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">吨钢成本</div>\r\n          <div class=\"value\">{{ formatUnitCost(containType === 2 ? qualityCostData.allcPerEx : qualityCostData.costPerEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costPerExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costPerExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costPerExPercent)\">{{ qualityCostDetail.costPerExPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 第2行：质量成本四大类别占比（占1/2宽度）+ 四大质量成本趋势（占1/2宽度） -->\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>1. 质量成本四大类别占比</h3>\r\n        <div ref=\"pieChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>2. 四大质量成本趋势</h3>\r\n        <div ref=\"multiLineChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：外部损失成本构成图表 + 内部损失成本构成图表 - 每个占据行宽的50% -->\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>3. 外部损失成本构成</h3>\r\n        <div ref=\"externalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>4. 内部损失成本构成</h3>\r\n        <div ref=\"internalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：产品挽救处理成本分析图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>5. 产品挽救处理成本分析</h3>\r\n        <div ref=\"waterfallChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第4行：产品报废损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>6. 产品报废损失明细</h3>\r\n        <div ref=\"scrapLossChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第5行：产品质量异议损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>7. 产品质量异议损失明细</h3>\r\n        <div ref=\"qualityObjectionChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第7行：控制成本 vs 失败成本对比（占满整行） -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>8. \"控制成本\" vs \"失败成本\" 对比</h3>\r\n        <div ref=\"comboChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { getPieChartData, getMultiLineChartData, getQualityCostDetail, getExternalCostDetail, getInternalCostDetail, getComboChartDetail,getWaterfallChartDetail,getScrapLossChartDetailsDetail,getQualityObjectionLossDetail } from \"@/api/qualityCost/dashboard\";\r\n\r\nexport default {\r\n  name: 'QualityCostDashboard',\r\n  data() {\r\n    // 获取默认会计期（上个月）\r\n    const getDefaultYearMonth = () => {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    };\r\n\r\n    return {\r\n      updateTime: '2023-10-27 10:00',\r\n      charts: {},\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: getDefaultYearMonth(),\r\n      // 质量成本类型，默认值为1（不含列入项）\r\n      containType: 1,\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      qualityCostDetail: {},\r\n      qualityCostData: {}\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        console.log('成本中心变化:', this.costCenter);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        console.log('会计期变化:', this.accountingPeriod);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听质量成本类型变化\r\n    containType: {\r\n      handler() {\r\n        console.log('质量成本类型变化:', this.containType);\r\n        this.refreshChartData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n    //质量成本四大类别占比\r\n\r\n    this.initCharts();\r\n    this.resizeObserver = new ResizeObserver(() => {\r\n      this.resizeCharts()\r\n    })\r\n    this.resizeObserver.observe(this.$el)\r\n    window.addEventListener('resize', this.resizeCharts)\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁所有图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      if (chart) {\r\n        chart.dispose()\r\n      }\r\n    })\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect()\r\n    }\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n  },\r\n  methods: {\r\n    // 判断百分比是否为负数\r\n    isNegativePercentage(percentage) {\r\n      if (!percentage) return false;\r\n      return percentage.toString().startsWith('-');\r\n    },\r\n\r\n    // 根据百分比正负值返回对应的CSS类\r\n    getPercentageClass(percentage) {\r\n      if (!percentage) return 'neutral';\r\n      return this.isNegativePercentage(percentage) ? 'negative' : 'positive';\r\n    },\r\n\r\n    // 格式化数字，最多保留两位小数\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0';\r\n      }\r\n      // 使用toFixed(2)保留两位小数，然后用parseFloat去掉末尾的0\r\n      return parseFloat(number.toFixed(2)).toString();\r\n    },\r\n\r\n    // 添加千分位分隔符\r\n    addThousandSeparator(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    },\r\n\r\n    // 格式化产量/销量为万吨单位\r\n    formatTonnage(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万吨';\r\n      }\r\n      // 转换为万吨并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万吨`;\r\n    },\r\n\r\n    // 格式化总金额为万元单位\r\n    formatAmount(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万元';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万元';\r\n      }\r\n      // 转换为万元并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万元`;\r\n    },\r\n\r\n    // 格式化吨钢成本为元/吨单位\r\n    formatUnitCost(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0元/吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0元/吨';\r\n      }\r\n      // 保留两位小数并添加单位，添加千分位分隔符\r\n      const result = number.toFixed(2);\r\n      return `${this.addThousandSeparator(result)}元/吨`;\r\n    },\r\n\r\n\r\n    getWaterfallChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getWaterfallChartDetail(params).then(response => {\r\n        console.log('getWaterfallChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新WaterfallChart柱状图\r\n          this.updateWaterfallChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取WaterfallChart数据失败:', error);\r\n        this.$message.error('获取WaterfallChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新WaterfallChart柱状图\r\n    updateWaterfallChart(data) {\r\n      if (this.charts.waterfallChart && data) {\r\n        console.log('接收到的WaterfallChart数据:', data);\r\n\r\n        // 处理rescueProject数据\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n\r\n        let dataItems = [];\r\n\r\n        if (data.rescueProject) {\r\n          // 将rescueProject对象转换为数组，转换为万元\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,    // 第一项为维度名称\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)  // 第二项为对应维度的值，转换为万元\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '挽救处理成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.waterfallChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('WaterfallChart柱状图数据已更新');\r\n      } else {\r\n        console.error('WaterfallChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新ScrapLossChart柱状图\r\n    updateScrapLossChart(data) {\r\n      if (this.charts.scrapLossChart && data) {\r\n        console.log('接收到的ScrapLossChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理报废损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.scrapLossMap) {\r\n          // 情况1: 使用scrapLossMap数据（根据实际API返回的数据结构）\r\n          console.log('使用scrapLossMap数据');\r\n          dataItems = Object.entries(data.scrapLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLoss) {\r\n          // 情况3: 使用scrapLoss数据\r\n          console.log('使用scrapLoss数据');\r\n          dataItems = Object.entries(data.scrapLoss).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLossProject) {\r\n          // 情况4: 使用scrapLossProject数据\r\n          console.log('使用scrapLossProject数据');\r\n          dataItems = Object.entries(data.scrapLossProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况5: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '报废损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.scrapLossChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('ScrapLossChart柱状图数据已更新');\r\n      } else {\r\n        console.error('ScrapLossChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新QualityObjectionChart柱状图\r\n    updateQualityObjectionChart(data) {\r\n      if (this.charts.qualityObjectionChart && data) {\r\n        console.log('接收到的QualityObjectionChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理质量异议损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.qualityObjectionLossMap) {\r\n          // 情况1: 使用qualityObjectionLossMap数据\r\n          console.log('使用qualityObjectionLossMap数据');\r\n          dataItems = Object.entries(data.qualityObjectionLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况3: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '质量异议损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.qualityObjectionChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('QualityObjectionChart柱状图数据已更新');\r\n      } else {\r\n        console.error('QualityObjectionChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    getQualityObjectionLossDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityObjectionLossDetail(params).then(response => {\r\n        console.log('getQualityObjectionLossDetail:', response);\r\n        if (response.data) {\r\n          // 更新QualityObjectionChart柱状图\r\n          this.updateQualityObjectionChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取QualityObjectionChart数据失败:', error);\r\n        this.$message.error('获取产品质量异议损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getScrapLossChartDetailsDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getScrapLossChartDetailsDetail(params).then(response => {\r\n        console.log('getScrapLossChartDetailsDetail:', response);\r\n        if (response.data) {\r\n          // 更新ScrapLossChart柱状图\r\n          this.updateScrapLossChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取ScrapLossChart数据失败:', error);\r\n        this.$message.error('获取产品报废损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getExternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getExternalCostDetail(params).then(response => {\r\n        console.log('getExternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新外部损失成本构成图表\r\n          this.updateExternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取外部损失成本数据失败:', error);\r\n        this.$message.error('获取外部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    getInternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getInternalCostDetail(params).then(response => {\r\n        console.log('getInternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新内部损失成本构成图表\r\n          this.updateInternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取内部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新内部损失成本构成图表\r\n    updateInternalCostDetailChart(data) {\r\n      if (this.charts.internalCostDetailChart && data) {\r\n        console.log('接收到的内部损失成本数据:', data);\r\n\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.contractionLoss) {\r\n          Object.entries(data.contractionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.rescueCost) {\r\n          Object.entries(data.rescueCost).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.revisionLoss) {\r\n          Object.entries(data.revisionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.scrapLoss) {\r\n          Object.entries(data.scrapLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        console.log('收集到的所有数据项（包含0值）:', allDataItems);\r\n\r\n        // 按数值从高到低排序（0值会排在负值之前，正值之后）\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        console.log('排序后的数据（包含0值）:', allDataItems);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        console.log('y轴数据:', yAxisData);\r\n        console.log('系列数据（包含0值）:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.internalCostDetailChart.setOption(option);\r\n        console.log('内部损失成本构成图表数据已更新（包含0值，按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    // 更新外部损失成本构成图表\r\n    updateExternalCostDetailChart(data) {\r\n      if (this.charts.externalCostDetailChart && data) {\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#FCA5A5', '#FDE68A', '#86EFAC', '#93C5FD', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.customerClaimCost) {\r\n          Object.entries(data.customerClaimCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionFeeCost) {\r\n          Object.entries(data.qualityObjectionFeeCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionTravelCost) {\r\n          Object.entries(data.qualityObjectionTravelCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.returnLoss) {\r\n          Object.entries(data.returnLoss).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        // 按数值从高到低排序\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.externalCostDetailChart.setOption(option);\r\n        console.log('外部损失成本构成图表数据已更新（已按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    getQualityCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityCostDetail(params).then(response => {\r\n        console.log('getQualityCostDetail:', response);\r\n        if (response.data) {\r\n          this.qualityCostData = response.data.qualityCostData;\r\n          this.qualityCostDetail = response.data;\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getMultiLineChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getMultiLineChartData(params).then(response => {\r\n        console.log('getMultiLineChartData:', response);\r\n        if (response.data) {\r\n          this.updateMultiLineChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    getComboChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getComboChartDetail(params).then(response => {\r\n        console.log('getComboChartDetail:', response);\r\n        if (response.data) {\r\n          this.updateComboChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取ComboChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新ComboChart图表\r\n    updateComboChart(data) {\r\n      if (this.charts.comboChart && data) {\r\n        console.log('接收到的ComboChart数据:', data);\r\n\r\n        // 基于会计期生成近6个月的月份标签作为x轴数据\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        console.log('生成的月份标签:', months);\r\n\r\n        // 生成对应的年月格式用于数据匹配\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n        console.log('生成的年月格式:', yearMonths);\r\n\r\n        const failureCostData = [];     // 失败成本数据\r\n        const controllingCostData = []; // 控制成本数据\r\n\r\n        // 为每个月份提取对应的数值，转换为万元\r\n        yearMonths.forEach(yearMonth => {\r\n          // 获取失败成本数据，转换为万元\r\n          const failureValue = data.failureCostMap && data.failureCostMap[yearMonth]\r\n            ? ((Number(data.failureCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          failureCostData.push(failureValue);\r\n\r\n          // 获取控制成本数据，转换为万元\r\n          const controllingValue = data.controllingCostMap && data.controllingCostMap[yearMonth]\r\n            ? ((Number(data.controllingCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          controllingCostData.push(controllingValue);\r\n        });\r\n\r\n        console.log('x轴月份数据:', months.map(month => `${month}月`));\r\n        console.log('失败成本数据:', failureCostData);\r\n        console.log('控制成本数据:', controllingCostData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          // 图例配置 - 标注颜色对应的维度\r\n          legend: {\r\n            data: ['失败成本', '控制成本'], // 失败成本(红色#FCA5A5)，控制成本(绿色#86EFAC)\r\n            textStyle: { color: '#E5E7EB' }\r\n          },\r\n          grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 近6个月的月份\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '成本 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n\r\n          series: [\r\n            {\r\n              name: '失败成本', // 红色曲线 #FCA5A5\r\n              type: 'line',\r\n              data: failureCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '控制成本', // 绿色曲线 #86EFAC\r\n              type: 'line',\r\n              data: controllingCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.comboChart.setOption(option);\r\n        console.log('ComboChart图表数据已更新');\r\n      }\r\n    },\r\n\r\n    // 生成ComboChart的月份标签（当前月份和之前的5个月）\r\n    generateComboChartMonths() {\r\n      const months = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const month = date.getMonth() + 1;\r\n        months.push(month);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 生成对应的年月格式（当前月份和之前的5个月，如202501, 202502等）\r\n    generateYearMonths() {\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n        const yearMonth = `${year}${String(month).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    // 基于会计期生成ComboChart的月份标签（会计期当前月份和之前的5个月）\r\n    generateComboChartMonthsByAccountingPeriod() {\r\n      const months = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateComboChartMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const monthNum = date.getMonth() + 1;\r\n        months.push(monthNum);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 基于会计期生成对应的年月格式（会计期当前月份和之前的5个月）\r\n    generateYearMonthsByAccountingPeriod() {\r\n      const yearMonths = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateYearMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const yearNum = date.getFullYear();\r\n        const monthNum = date.getMonth() + 1;\r\n        const yearMonth = `${yearNum}${String(monthNum).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getPieChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n\r\n      getPieChartData(params).then(response => {\r\n        console.log('getPieChartData:', response);\r\n        if (response.data) {\r\n          this.updatePieChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          console.log('获取成本中心列表:', this.costCenterOptions);\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据刷新\r\n          this.$nextTick(() => {\r\n            this.refreshChartData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n\r\n    // 更新饼图数据\r\n    updatePieChart(data) {\r\n      if (this.charts.pieChart && data) {\r\n        // 更新饼图的数据，转换为万元\r\n        const option = this.charts.pieChart.getOption();\r\n        if (option && option.series && option.series[0]) {\r\n          option.series[0].data = [\r\n            { value: (data.preventionCost / 10000).toFixed(2), name: '预防成本', itemStyle: { color: '#93C5FD' } },\r\n            { value: (data.appraisalCost / 10000).toFixed(2), name: '鉴定成本', itemStyle: { color: '#86EFAC' } },\r\n            { value: (data.internalCost / 10000).toFixed(2), name: '内部损失成本', itemStyle: { color: '#FDE68A' } },\r\n            { value: (data.externalCost / 10000).toFixed(2), name: '外部损失成本', itemStyle: { color: '#FCA5A5' } },\r\n          ],\r\n            this.charts.pieChart.setOption(option);\r\n          // console.log('饼图数据已更新');\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新多线图数据\r\n    updateMultiLineChart(data) {\r\n      if (this.charts.multiLineChart && data) {\r\n        // 基于会计期生成月份标签和对应的年月数字\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n\r\n        // 处理各种成本数据，转换为万元\r\n        const preventionData = this.processMapData(data.preventionCostMap, yearMonths, true);\r\n        const appraisalData = this.processMapData(data.appraisalCostMap, yearMonths, true);\r\n        const internalData = this.processMapData(data.internalCostMap, yearMonths, true);\r\n        const externalData = this.processMapData(data.externalCostMap, yearMonths, true);\r\n\r\n        // 更新多线图的配置\r\n        const option = {\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 格式化为\"X月\"\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [\r\n            {\r\n              name: '预防成本',\r\n              type: 'line',\r\n              data: preventionData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#93C5FD', width: 3 },\r\n              itemStyle: { color: '#93C5FD' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '鉴定成本',\r\n              type: 'line',\r\n              data: appraisalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '内部损失成本',\r\n              type: 'line',\r\n              data: internalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FDE68A', width: 3 },\r\n              itemStyle: { color: '#FDE68A' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '外部损失成本',\r\n              type: 'line',\r\n              data: externalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.multiLineChart.setOption(option);\r\n        console.log('多线图数据已更新');\r\n      }\r\n    },\r\n\r\n    // 处理Map数据，根据年月匹配对应的值\r\n    processMapData(costMap, yearMonths, convertToWanYuan = false) {\r\n      if (!costMap) return new Array(yearMonths.length).fill(0);\r\n\r\n      return yearMonths.map(yearMonth => {\r\n        const value = costMap[yearMonth] || 0;\r\n        return convertToWanYuan ? (value / 10000).toFixed(2) : value;\r\n      });\r\n    },\r\n\r\n    // 生成月份标签（当前月份及前五个月份）\r\n    generateMonthLabels() {\r\n      const months = [];\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n\r\n        months.push(`${month}月`);\r\n        yearMonths.push(parseInt(`${year}${String(month).padStart(2, '0')}`));\r\n      }\r\n\r\n      return { months, yearMonths };\r\n    },\r\n\r\n    // 刷新图表数据\r\n    refreshChartData() {\r\n      // 只有当成本中心和会计期都有值时才刷新\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        return;\r\n      }\r\n\r\n      this.getQualityCostDetail();\r\n      this.getPieChartData();\r\n      this.getMultiLineChartData();\r\n      this.getExternalCostDetail();\r\n      this.getInternalCostDetail();\r\n      this.getComboChartDetail();\r\n      this.getWaterfallChartDetail();\r\n      this.getScrapLossChartDetailsDetail();\r\n      this.getQualityObjectionLossDetail();\r\n\r\n      // 这里可以添加其他图表的数据刷新\r\n      // this.$message.success(`已切换到成本中心: ${this.costCenter}, 会计期: ${this.accountingPeriod}`);\r\n    },\r\n\r\n    /** 查询按钮操作 */\r\n    handleQuery() {\r\n      this.refreshChartData();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      // 重置为默认值\r\n      if (this.costCenterOptions.length > 0) {\r\n        this.costCenter = this.costCenterOptions[0].key;\r\n      }\r\n\r\n      // 获取默认会计期\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth();\r\n      const prevMonth = month === 0 ? 12 : month;\r\n      const prevYear = month === 0 ? year - 1 : year;\r\n      this.accountingPeriod = `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n\r\n      this.$message.success('查询条件已重置');\r\n    },\r\n\r\n    initCharts() {\r\n      const THEME = 'dark'\r\n\r\n      // 定义商务风淡色系色彩方案\r\n      this.businessColors = {\r\n        light: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#7DD3FC', '#F9A8D4', '#BEF264'],\r\n        gradient: [\r\n          { offset: 0, color: '#3B82F6' },\r\n          { offset: 1, color: '#1E40AF' }\r\n        ]\r\n      }\r\n\r\n      // 初始化所有图表\r\n      this.charts.pieChart = echarts.init(this.$refs.pieChart, THEME)\r\n      this.charts.multiLineChart = echarts.init(this.$refs.multiLineChart, THEME)\r\n      this.charts.externalCostDetailChart = echarts.init(this.$refs.externalCostDetailChart, THEME)\r\n      this.charts.internalCostDetailChart = echarts.init(this.$refs.internalCostDetailChart, THEME)\r\n      this.charts.waterfallChart = echarts.init(this.$refs.waterfallChart, THEME)\r\n      this.charts.comboChart = echarts.init(this.$refs.comboChart, THEME)\r\n      this.charts.scrapLossChart = echarts.init(this.$refs.scrapLossChart, THEME)\r\n      this.charts.qualityObjectionChart = echarts.init(this.$refs.qualityObjectionChart, THEME)\r\n\r\n      // 配置所有图表\r\n      this.setPieChartOption()\r\n      this.setMultiLineChartOption()\r\n      this.setExternalCostDetailChartOption()\r\n      this.setInternalCostDetailChartOption()\r\n      this.setWaterfallChartOption()\r\n      this.setComboChartOption()\r\n      this.setScrapLossChartOption()\r\n      this.setQualityObjectionChartOption()\r\n    },\r\n\r\n    setPieChartOption() {\r\n      this.charts.pieChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: (params) => {\r\n            const value = parseFloat(params.value).toFixed(2);\r\n            const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n            return `${params.seriesName} <br/>${params.name}: ${formattedValue}万元 (${params.percent}%)`;\r\n          },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          top: 'bottom',\r\n          left: 'center',\r\n          textStyle: { color: '#E5E7EB', fontSize: 12 }\r\n        },\r\n        series: [{\r\n          name: '成本类别',\r\n          type: 'pie',\r\n          radius: '65%',\r\n          data: [],\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 15,\r\n              shadowOffsetX: 0,\r\n              shadowColor: 'rgba(147, 197, 253, 0.6)'\r\n            }\r\n          },\r\n          labelLine: { lineStyle: { color: '#9CA3AF' } },\r\n          label: {\r\n            color: '#E5E7EB',\r\n            formatter: (params) => {\r\n              const value = parseFloat(params.value).toFixed(2);\r\n              const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              return `${params.name}(${formattedValue}万元)`;\r\n            }\r\n          }\r\n        }]\r\n      })\r\n    },\r\n\r\n\r\n\r\n    setComboChartOption() {\r\n      this.charts.comboChart.setOption({\r\n        color: ['#FCA5A5', '#86EFAC'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['失败成本', '控制成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '失败成本', // 红色曲线 #FCA5A5\r\n            type: 'line',\r\n            data: [280, 260, 240, 220, 200, 180],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '控制成本', // 绿色曲线 #86EFAC\r\n            type: 'line',\r\n            data: [120, 125, 130, 135, 140, 145],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setMultiLineChartOption() {\r\n      this.charts.multiLineChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['预防成本', '鉴定成本', '内部损失成本', '外部损失成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '预防成本',\r\n            type: 'line',\r\n            data: [80, 82, 85, 88, 90, 95],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#93C5FD', width: 3 },\r\n            itemStyle: { color: '#93C5FD' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '鉴定成本',\r\n            type: 'line',\r\n            data: [120, 122, 125, 128, 130, 135],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '内部损失成本',\r\n            type: 'line',\r\n            data: [450, 430, 410, 380, 350, 320],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '外部损失成本',\r\n            type: 'line',\r\n            data: [350, 340, 310, 290, 260, 230],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    setParetoChartOption() {\r\n      this.charts.paretoChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        grid: { right: '20%' },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['产品报废', '产品改判', '设备故障', '工艺废料', '其他'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '损失金额(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '累计占比',\r\n            min: 0,\r\n            max: 100,\r\n            axisLabel: {\r\n              formatter: '{value} %',\r\n              color: '#9CA3AF'\r\n            },\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '损失金额',\r\n            type: 'bar',\r\n            data: [280, 110, 35, 20, 5],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '累计占比',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [62.2, 86.7, 94.4, 98.9, 100],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setExternalCostDetailChartOption() {\r\n      this.charts.externalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setInternalCostDetailChartOption() {\r\n      this.charts.internalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setWaterfallChartOption() {\r\n      this.charts.waterfallChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['初始成本', '修磨', '矫直', '探伤', '热处理', '总成本'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '辅助',\r\n            type: 'bar',\r\n            stack: '总量',\r\n            itemStyle: {\r\n              color: 'rgba(0,0,0,0)',\r\n              borderColor: 'rgba(0,0,0,0)',\r\n              borderWidth: 0\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: 'rgba(0,0,0,0)'\r\n              }\r\n            },\r\n            data: [0, 0, 50, 80, 105, 0]\r\n          },\r\n        ]\r\n      })\r\n    },\r\n\r\n    setScrapLossChartOption() {\r\n      this.charts.scrapLossChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '报废损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setQualityObjectionChartOption() {\r\n      this.charts.qualityObjectionChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '质量异议损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setDualYChartOption() {\r\n      this.charts.dualYChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['产量(吨)', '吨钢成本(元)'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '产量(吨)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '吨钢成本(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '产量(吨)',\r\n            type: 'bar',\r\n            data: [80000, 82000, 85000, 83000, 88000, 90000],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '吨钢成本(元)',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [13.1, 12.8, 12.5, 12.6, 12.2, 12.0],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    resizeCharts() {\r\n      Object.values(this.charts).forEach(chart => {\r\n        if (chart) {\r\n          chart.resize()\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.quality-cost-dashboard {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\r\n  background-color: #111827;\r\n  /* 深色背景 */\r\n  color: #d1d5db;\r\n  /* 浅色文字 */\r\n  margin: 0;\r\n  padding: 24px;\r\n  min-height: 100vh;\r\n}\r\n\r\n.header {\r\n  margin-bottom: 24px;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.header-wrapper {\r\n  display: inline-block;\r\n  position: relative;\r\n}\r\n\r\n.header h1 {\r\n  font-size: 28px;\r\n  color: #f9fafb;\r\n  /* 白色标题 */\r\n  font-weight: 600;\r\n  margin: 0;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.header p {\r\n  font-size: 16px;\r\n  color: #9ca3af;\r\n  /* 中灰色文字 */\r\n  margin: 8px 0 0 0;\r\n}\r\n\r\n.header-filters {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex-wrap: nowrap;\r\n  justify-content: flex-start;\r\n  margin-top: 12px;\r\n  margin-left: 950px;\r\n  /* 向左对齐 */\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.filter-item .label {\r\n  color: #d1d5db;\r\n  /* 浅色标签文字 */\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右上角筛选区域的样式 */\r\n.header-filters .el-input__inner,\r\n.header-filters .el-select .el-input__inner {\r\n  background-color: #111827; /* 与页面背景一致的深色 */\r\n  border-color: #374151;\r\n  color: #ffffff; /* 白色字体 */\r\n}\r\n\r\n.header-filters .el-input__inner:focus,\r\n.header-filters .el-select .el-input__inner:focus {\r\n  border-color: #93c5fd;\r\n  background-color: #111827; /* 聚焦时保持背景色 */\r\n}\r\n\r\n.header-filters .el-select-dropdown {\r\n  background-color: #111827; /* 下拉菜单背景与页面一致 */\r\n  border-color: #374151;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item {\r\n  color: #ffffff; /* 下拉选项白色字体 */\r\n  background-color: #111827;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item:hover {\r\n  background-color: #1f2937; /* 悬浮时稍微亮一点 */\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item.selected {\r\n  background-color: #374151; /* 选中项背景 */\r\n  color: #ffffff;\r\n}\r\n\r\n/* 下拉框箭头颜色 */\r\n.header-filters .el-select .el-input__suffix {\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select .el-select__caret {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 占位符文字颜色 */\r\n.header-filters .el-input__inner::placeholder {\r\n  color: #9ca3af;\r\n}\r\n\r\n/* 清除按钮颜色 */\r\n.header-filters .el-select .el-select__clear {\r\n  color: #9ca3af;\r\n}\r\n\r\n.header-filters .el-select .el-select__clear:hover {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n  .header-filters {\r\n    flex-wrap: wrap;\r\n    gap: 8px;\r\n    margin-left: 0;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header {\r\n    text-align: center;\r\n  }\r\n\r\n  .header-filters {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-left: 0;\r\n    /* 在小屏幕上取消左边距 */\r\n  }\r\n\r\n  .filter-item {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr); /* 改为四列布局，支持1/4和3/4分配 */\r\n  gap: 24px;\r\n}\r\n\r\n/* 响应式设计：在小屏幕上改为单列 */\r\n@media (max-width: 1200px) {\r\n  .dashboard-grid {\r\n    grid-template-columns: 1fr; /* 小屏幕时改为单列 */\r\n  }\r\n\r\n  /* 小屏幕时重置所有grid-column样式 */\r\n  .dashboard-grid .chart-container {\r\n    grid-column: 1 !important;\r\n  }\r\n}\r\n\r\n.chart-container,\r\n.kpi-card {\r\n  background-color: #1f2937;\r\n  /* 深色卡片背景 */\r\n  border-radius: 8px;\r\n  border: 1px solid #374151;\r\n  /* 边框 */\r\n  box-shadow: none;\r\n  padding: 24px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-container {\r\n  height: 400px;\r\n}\r\n\r\n/* 大图表样式 - 用于两两排列的图表 */\r\n.chart-container.large-chart {\r\n  height: 500px; /* 增加高度 */\r\n  min-height: 500px;\r\n}\r\n\r\n.chart-container h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #f9fafb;\r\n  /* 白色卡片标题 */\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  flex-grow: 1;\r\n}\r\n\r\n.kpi-grid {\r\n  grid-column: 1 / -1;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 24px;\r\n}\r\n\r\n.kpi-card {\r\n  justify-content: space-between;\r\n}\r\n\r\n.kpi-card .title {\r\n  font-size: 14px;\r\n  color: #9ca3af;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.kpi-card .value {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #f9fafb;\r\n}\r\n\r\n.kpi-card .comparison {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.kpi-card .comparison .arrow {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin-right: 4px;\r\n  stroke-width: 2.5px;\r\n}\r\n\r\n.kpi-card .comparison .positive {\r\n  color: #34d399;\r\n  /* 亮绿色 */\r\n}\r\n\r\n.kpi-card .comparison .negative {\r\n  color: #f87171;\r\n  /* 亮红色 */\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;IACA,IAAAC,mBAAA,YAAAA,oBAAA;MACA,IAAAC,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAJ,GAAA,CAAAK,QAAA;MACA,IAAAC,GAAA,GAAAN,GAAA,CAAAO,OAAA;MACA,IAAAC,IAAA,GAAAR,GAAA,CAAAS,QAAA;;MAEA;MACA,IAAAH,GAAA,SAAAA,GAAA,WAAAE,IAAA;QACA;QACA,IAAAE,SAAA,GAAAN,KAAA,cAAAA,KAAA;QACA,IAAAO,QAAA,GAAAP,KAAA,SAAAF,IAAA,OAAAA,IAAA;QACA,UAAAU,MAAA,CAAAD,QAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAH,SAAA,EAAAI,QAAA;MACA;QACA,UAAAF,MAAA,CAAAV,IAAA,OAAAU,MAAA,CAAAC,MAAA,CAAAT,KAAA,EAAAU,QAAA;MACA;IACA;IAEA;MACAC,UAAA;MACAC,MAAA;MACA;MACAC,UAAA;MACAC,gBAAA,EAAAnB,mBAAA;MACA;MACAoB,WAAA;MACA;MACAC,iBAAA;MACAC,iBAAA;MACAC,iBAAA;MACAC,eAAA;IACA;EACA;EACAC,KAAA;IACA;IACAP,UAAA;MACAQ,OAAA,WAAAA,QAAA;QACAC,OAAA,CAAAC,GAAA,iBAAAV,UAAA;QACA,KAAAW,gBAAA;MACA;IACA;IACA;IACAV,gBAAA;MACAO,OAAA,WAAAA,QAAA;QACAC,OAAA,CAAAC,GAAA,gBAAAT,gBAAA;QACA,KAAAU,gBAAA;MACA;IACA;IACA;IACAT,WAAA;MACAM,OAAA,WAAAA,QAAA;QACAC,OAAA,CAAAC,GAAA,mBAAAR,WAAA;QACA,KAAAS,gBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,iBAAA;IACA;;IAEA,KAAAC,UAAA;IACA,KAAAC,cAAA,OAAAC,cAAA;MACAJ,KAAA,CAAAK,YAAA;IACA;IACA,KAAAF,cAAA,CAAAG,OAAA,MAAAC,GAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAJ,YAAA;EACA;EACAK,aAAA,WAAAA,cAAA;IACA;IACAC,MAAA,CAAAC,MAAA,MAAA1B,MAAA,EAAA2B,OAAA,WAAAC,KAAA;MACA,IAAAA,KAAA;QACAA,KAAA,CAAAC,OAAA;MACA;IACA;IACA,SAAAZ,cAAA;MACA,KAAAA,cAAA,CAAAa,UAAA;IACA;IACAR,MAAA,CAAAS,mBAAA,gBAAAZ,YAAA;EACA;EACAa,OAAA;IACA;IACAC,oBAAA,WAAAA,qBAAAC,UAAA;MACA,KAAAA,UAAA;MACA,OAAAA,UAAA,CAAAC,QAAA,GAAAC,UAAA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAAH,UAAA;MACA,KAAAA,UAAA;MACA,YAAAD,oBAAA,CAAAC,UAAA;IACA;IAEA;IACAI,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAC,SAAA,IAAAD,GAAA;QACA;MACA;MACA,IAAAE,MAAA,GAAAC,MAAA,CAAAH,GAAA;MACA,IAAAI,KAAA,CAAAF,MAAA;QACA;MACA;MACA;MACA,OAAAG,UAAA,CAAAH,MAAA,CAAAI,OAAA,KAAAV,QAAA;IACA;IAEA;IACAW,oBAAA,WAAAA,qBAAAP,GAAA;MACA,OAAAA,GAAA,CAAAJ,QAAA,GAAAY,OAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAT,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAC,SAAA,IAAAD,GAAA;QACA;MACA;MACA,IAAAE,MAAA,GAAAC,MAAA,CAAAH,GAAA;MACA,IAAAI,KAAA,CAAAF,MAAA;QACA;MACA;MACA;MACA,IAAAQ,MAAA,IAAAR,MAAA,UAAAI,OAAA;MACA,UAAAjD,MAAA,MAAAkD,oBAAA,CAAAG,MAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAX,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAC,SAAA,IAAAD,GAAA;QACA;MACA;MACA,IAAAE,MAAA,GAAAC,MAAA,CAAAH,GAAA;MACA,IAAAI,KAAA,CAAAF,MAAA;QACA;MACA;MACA;MACA,IAAAQ,MAAA,IAAAR,MAAA,UAAAI,OAAA;MACA,UAAAjD,MAAA,MAAAkD,oBAAA,CAAAG,MAAA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAAZ,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAC,SAAA,IAAAD,GAAA;QACA;MACA;MACA,IAAAE,MAAA,GAAAC,MAAA,CAAAH,GAAA;MACA,IAAAI,KAAA,CAAAF,MAAA;QACA;MACA;MACA;MACA,IAAAQ,MAAA,GAAAR,MAAA,CAAAI,OAAA;MACA,UAAAjD,MAAA,MAAAkD,oBAAA,CAAAG,MAAA;IACA;IAGAG,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAApD,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAiD,kCAAA,EAAAE,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,6BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAuE,MAAA,CAAAK,oBAAA,CAAAD,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,0BAAAA,KAAA;QACAP,MAAA,CAAAQ,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACAF,oBAAA,WAAAA,qBAAA5E,IAAA;MACA,SAAAkB,MAAA,CAAA8D,cAAA,IAAAhF,IAAA;QACA4B,OAAA,CAAAC,GAAA,0BAAA7B,IAAA;;QAEA;QACA,IAAAiF,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QAEA,IAAAC,SAAA;QAEA,IAAApF,IAAA,CAAAqF,aAAA;UACA;UACAD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAqF,aAAA,EAAAE,GAAA,WAAAC,IAAA;YAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAH,IAAA;cAAAI,GAAA,GAAAH,KAAA;cAAAI,KAAA,GAAAJ,KAAA;YAAA;cACA1F,IAAA,EAAA6F,GAAA;cAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA;QAEAnC,OAAA,CAAAC,GAAA,aAAAuD,SAAA;QAEA,IAAAA,SAAA,CAAAU,MAAA;UACA;UACAV,SAAA,CAAAW,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;UAAA;;UAEA;UACA,IAAAK,WAAA,GAAAd,SAAA,CAAAe,KAAA;UACAvE,OAAA,CAAAC,GAAA,cAAAqE,WAAA;;UAEA;UACAA,WAAA,CAAArD,OAAA,WAAAuD,IAAA,EAAAC,KAAA;YACApB,SAAA,CAAAqB,IAAA,CAAAF,IAAA,CAAArG,IAAA;YACAmF,UAAA,CAAAoB,IAAA;cACAT,KAAA,EAAAO,IAAA,CAAAP,KAAA;cACAU,SAAA;gBAAAC,KAAA,EAAArB,MAAA,CAAAkB,KAAA,GAAAlB,MAAA,CAAAW,MAAA;cAAA;YACA;UACA;QACA;UACAlE,OAAA,CAAA6E,IAAA;UACA;UACAxB,SAAA,CAAAqB,IAAA;UACApB,UAAA,CAAAoB,IAAA;YACAT,KAAA;YACAU,SAAA;cAAAC,KAAA,EAAArB,MAAA;YAAA;UACA;QACA;QAEAvD,OAAA,CAAAC,GAAA,YAAAoD,SAAA;QACArD,OAAA,CAAAC,GAAA,WAAAqD,UAAA;;QAEA;QACA,IAAAwB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAA/C,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAAuD,IAAA;gBACA,IAAAoB,cAAA,GAAA1D,UAAA,CAAAsC,IAAA,CAAAP,KAAA,EAAA9B,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAiC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAArD,MAAA;YACA;UACA;UACAwD,KAAA;YACAR,IAAA;YACAnH,IAAA,EAAAiF,SAAA;YACA2C,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACApH,IAAA;YACAoI,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACAtI,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAkF;UACA;QACA;QAEA,KAAAhE,MAAA,CAAA8D,cAAA,CAAAsD,SAAA,CAAA5B,MAAA;QACA9E,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEA;IACAyD,oBAAA,WAAAA,qBAAAvI,IAAA;MACA,SAAAkB,MAAA,CAAAsH,cAAA,IAAAxI,IAAA;QACA4B,OAAA,CAAAC,GAAA,0BAAA7B,IAAA;QACA4B,OAAA,CAAAC,GAAA,cAAA4G,QAAA,CAAA9C,OAAA,EAAA3F,IAAA;QACA4B,OAAA,CAAAC,GAAA,SAAAc,MAAA,CAAA+F,IAAA,CAAA1I,IAAA;;QAEA;QACA,IAAAiF,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QACA,IAAAC,SAAA;;QAEA;QACA,IAAApF,IAAA,CAAA2I,YAAA;UACA;UACA/G,OAAA,CAAAC,GAAA;UACAuD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAA2I,YAAA,EAAApD,GAAA,WAAAqD,KAAA;YAAA,IAAAC,KAAA,OAAAnD,eAAA,CAAAC,OAAA,EAAAiD,KAAA;cAAAhD,GAAA,GAAAiD,KAAA;cAAAhD,KAAA,GAAAgD,KAAA;YAAA;cACA9I,IAAA,EAAA6F,GAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA,WAAA/D,IAAA,CAAAqF,aAAA;UACA;UACAzD,OAAA,CAAAC,GAAA;UACAuD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAqF,aAAA,EAAAE,GAAA,WAAAuD,KAAA;YAAA,IAAAC,KAAA,OAAArD,eAAA,CAAAC,OAAA,EAAAmD,KAAA;cAAAlD,GAAA,GAAAmD,KAAA;cAAAlD,KAAA,GAAAkD,KAAA;YAAA;cACAhJ,IAAA,EAAA6F,GAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA,WAAA/D,IAAA,CAAAgJ,SAAA;UACA;UACApH,OAAA,CAAAC,GAAA;UACAuD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAgJ,SAAA,EAAAzD,GAAA,WAAA0D,KAAA;YAAA,IAAAC,KAAA,OAAAxD,eAAA,CAAAC,OAAA,EAAAsD,KAAA;cAAArD,GAAA,GAAAsD,KAAA;cAAArD,KAAA,GAAAqD,KAAA;YAAA;cACAnJ,IAAA,EAAA6F,GAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA,WAAA/D,IAAA,CAAAmJ,gBAAA;UACA;UACAvH,OAAA,CAAAC,GAAA;UACAuD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAmJ,gBAAA,EAAA5D,GAAA,WAAA6D,KAAA;YAAA,IAAAC,KAAA,OAAA3D,eAAA,CAAAC,OAAA,EAAAyD,KAAA;cAAAxD,GAAA,GAAAyD,KAAA;cAAAxD,KAAA,GAAAwD,KAAA;YAAA;cACAtJ,IAAA,EAAA6F,GAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA;UACA;UACAnC,OAAA,CAAAC,GAAA;UACAuD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,EAAAuF,GAAA,WAAA+D,KAAA;YAAA,IAAAC,MAAA,OAAA7D,eAAA,CAAAC,OAAA,EAAA2D,KAAA;cAAA1D,GAAA,GAAA2D,MAAA;cAAA1D,KAAA,GAAA0D,MAAA;YAAA;cACAxJ,IAAA,EAAA6F,GAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA;QAEAnC,OAAA,CAAAC,GAAA,aAAAuD,SAAA;QAEA,IAAAA,SAAA,CAAAU,MAAA;UACA;UACAV,SAAA,CAAAW,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;UAAA;;UAEA;UACA,IAAAK,WAAA,GAAAd,SAAA,CAAAe,KAAA;UACAvE,OAAA,CAAAC,GAAA,cAAAqE,WAAA;;UAEA;UACAA,WAAA,CAAArD,OAAA,WAAAuD,IAAA,EAAAC,KAAA;YACApB,SAAA,CAAAqB,IAAA,CAAAF,IAAA,CAAArG,IAAA;YACAmF,UAAA,CAAAoB,IAAA;cACAT,KAAA,EAAAO,IAAA,CAAAP,KAAA;cACAU,SAAA;gBAAAC,KAAA,EAAArB,MAAA,CAAAkB,KAAA,GAAAlB,MAAA,CAAAW,MAAA;cAAA;YACA;UACA;QACA;UACAlE,OAAA,CAAA6E,IAAA;UACA;UACAxB,SAAA,CAAAqB,IAAA;UACApB,UAAA,CAAAoB,IAAA;YACAT,KAAA;YACAU,SAAA;cAAAC,KAAA,EAAArB,MAAA;YAAA;UACA;QACA;QAEAvD,OAAA,CAAAC,GAAA,YAAAoD,SAAA;QACArD,OAAA,CAAAC,GAAA,WAAAqD,UAAA;;QAEA;QACA,IAAAwB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAA/C,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAAuD,IAAA;gBACA,IAAAoB,cAAA,GAAA1D,UAAA,CAAAsC,IAAA,CAAAP,KAAA,EAAA9B,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAiC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAArD,MAAA;YACA;UACA;UACAwD,KAAA;YACAR,IAAA;YACAnH,IAAA,EAAAiF,SAAA;YACA2C,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACApH,IAAA;YACAoI,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACAtI,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAkF;UACA;QACA;QAEA,KAAAhE,MAAA,CAAAsH,cAAA,CAAAF,SAAA,CAAA5B,MAAA;QACA9E,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEA;IACA0E,2BAAA,WAAAA,4BAAAxJ,IAAA;MACA,SAAAkB,MAAA,CAAAuI,qBAAA,IAAAzJ,IAAA;QACA4B,OAAA,CAAAC,GAAA,iCAAA7B,IAAA;QACA4B,OAAA,CAAAC,GAAA,cAAA4G,QAAA,CAAA9C,OAAA,EAAA3F,IAAA;QACA4B,OAAA,CAAAC,GAAA,SAAAc,MAAA,CAAA+F,IAAA,CAAA1I,IAAA;;QAEA;QACA,IAAAiF,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QACA,IAAAC,SAAA;;QAEA;QACA,IAAApF,IAAA,CAAA0J,uBAAA;UACA;UACA9H,OAAA,CAAAC,GAAA;UACAuD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAA0J,uBAAA,EAAAnE,GAAA,WAAAoE,MAAA;YAAA,IAAAC,MAAA,OAAAlE,eAAA,CAAAC,OAAA,EAAAgE,MAAA;cAAA/D,GAAA,GAAAgE,MAAA;cAAA/D,KAAA,GAAA+D,MAAA;YAAA;cACA7J,IAAA,EAAA6F,GAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA,WAAA/D,IAAA,CAAAqF,aAAA;UACA;UACAzD,OAAA,CAAAC,GAAA;UACAuD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAqF,aAAA,EAAAE,GAAA,WAAAsE,MAAA;YAAA,IAAAC,MAAA,OAAApE,eAAA,CAAAC,OAAA,EAAAkE,MAAA;cAAAjE,GAAA,GAAAkE,MAAA;cAAAjE,KAAA,GAAAiE,MAAA;YAAA;cACA/J,IAAA,EAAA6F,GAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA;UACA;UACAnC,OAAA,CAAAC,GAAA;UACAuD,SAAA,GAAAzC,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,EAAAuF,GAAA,WAAAwE,MAAA;YAAA,IAAAC,MAAA,OAAAtE,eAAA,CAAAC,OAAA,EAAAoE,MAAA;cAAAnE,GAAA,GAAAoE,MAAA;cAAAnE,KAAA,GAAAmE,MAAA;YAAA;cACAjK,IAAA,EAAA6F,GAAA;cACAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA;UAAA;QACA;QAEAnC,OAAA,CAAAC,GAAA,aAAAuD,SAAA;QAEA,IAAAA,SAAA,CAAAU,MAAA;UACA;UACAV,SAAA,CAAAW,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;UAAA;;UAEA;UACA,IAAAK,WAAA,GAAAd,SAAA,CAAAe,KAAA;UACAvE,OAAA,CAAAC,GAAA,cAAAqE,WAAA;;UAEA;UACAA,WAAA,CAAArD,OAAA,WAAAuD,IAAA,EAAAC,KAAA;YACApB,SAAA,CAAAqB,IAAA,CAAAF,IAAA,CAAArG,IAAA;YACAmF,UAAA,CAAAoB,IAAA;cACAT,KAAA,EAAAO,IAAA,CAAAP,KAAA;cACAU,SAAA;gBAAAC,KAAA,EAAArB,MAAA,CAAAkB,KAAA,GAAAlB,MAAA,CAAAW,MAAA;cAAA;YACA;UACA;QACA;UACAlE,OAAA,CAAA6E,IAAA;UACA;UACAxB,SAAA,CAAAqB,IAAA;UACApB,UAAA,CAAAoB,IAAA;YACAT,KAAA;YACAU,SAAA;cAAAC,KAAA,EAAArB,MAAA;YAAA;UACA;QACA;QAEAvD,OAAA,CAAAC,GAAA,YAAAoD,SAAA;QACArD,OAAA,CAAAC,GAAA,WAAAqD,UAAA;;QAEA;QACA,IAAAwB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAA/C,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAAuD,IAAA;gBACA,IAAAoB,cAAA,GAAA1D,UAAA,CAAAsC,IAAA,CAAAP,KAAA,EAAA9B,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAiC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAArD,MAAA;YACA;UACA;UACAwD,KAAA;YACAR,IAAA;YACAnH,IAAA,EAAAiF,SAAA;YACA2C,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACApH,IAAA;YACAoI,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACAtI,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAkF;UACA;QACA;QAEA,KAAAhE,MAAA,CAAAuI,qBAAA,CAAAnB,SAAA,CAAA5B,MAAA;QACA9E,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEAmF,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA/I,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAA4I,wCAAA,EAAAzF,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,mCAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAkK,MAAA,CAAAV,2BAAA,CAAA7E,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,iCAAAA,KAAA;QACAoF,MAAA,CAAAnF,QAAA,CAAAD,KAAA;MACA;IACA;IAEAqF,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAjJ,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAA8I,yCAAA,EAAA3F,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,oCAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAoK,MAAA,CAAA7B,oBAAA,CAAA5D,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,0BAAAA,KAAA;QACAsF,MAAA,CAAArF,QAAA,CAAAD,KAAA;MACA;IACA;IAEAuF,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAnJ,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAgJ,gCAAA,EAAA7F,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,2BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAsK,MAAA,CAAAC,6BAAA,CAAA5F,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACA;QACAwF,MAAA,CAAAvF,QAAA,CAAAD,KAAA;MACA;IACA;IAEA0F,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAtJ,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAmJ,gCAAA,EAAAhG,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,2BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAyK,MAAA,CAAAC,6BAAA,CAAA/F,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACA2F,MAAA,CAAA1F,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACA4F,6BAAA,WAAAA,8BAAA1K,IAAA;MACA,SAAAkB,MAAA,CAAAyJ,uBAAA,IAAA3K,IAAA;QACA4B,OAAA,CAAAC,GAAA,kBAAA7B,IAAA;;QAEA;QACA,IAAA4K,YAAA;QACA,IAAAzF,MAAA;;QAEA;QACA,IAAAnF,IAAA,CAAA6K,eAAA;UACAlI,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAA6K,eAAA,EAAAhI,OAAA,WAAAiI,MAAA;YAAA,IAAAC,MAAA,OAAArF,eAAA,CAAAC,OAAA,EAAAmF,MAAA;cAAAlF,GAAA,GAAAmF,MAAA;cAAAlF,KAAA,GAAAkF,MAAA;YACA;YACA,IAAAC,QAAA,KAAApH,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA6G,YAAA,CAAAtE,IAAA;cAAAvG,IAAA,EAAA6F,GAAA;cAAAC,KAAA,EAAAmF;YAAA;UACA;QACA;QAEA,IAAAhL,IAAA,CAAAiL,UAAA;UACAtI,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAiL,UAAA,EAAApI,OAAA,WAAAqI,MAAA;YAAA,IAAAC,MAAA,OAAAzF,eAAA,CAAAC,OAAA,EAAAuF,MAAA;cAAAtF,GAAA,GAAAuF,MAAA;cAAAtF,KAAA,GAAAsF,MAAA;YACA;YACA,IAAAH,QAAA,KAAApH,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA6G,YAAA,CAAAtE,IAAA;cAAAvG,IAAA,EAAA6F,GAAA;cAAAC,KAAA,EAAAmF;YAAA;UACA;QACA;QAEA,IAAAhL,IAAA,CAAAoL,YAAA;UACAzI,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAoL,YAAA,EAAAvI,OAAA,WAAAwI,MAAA;YAAA,IAAAC,MAAA,OAAA5F,eAAA,CAAAC,OAAA,EAAA0F,MAAA;cAAAzF,GAAA,GAAA0F,MAAA;cAAAzF,KAAA,GAAAyF,MAAA;YACA;YACA,IAAAN,QAAA,KAAApH,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA6G,YAAA,CAAAtE,IAAA;cAAAvG,IAAA,EAAA6F,GAAA;cAAAC,KAAA,EAAAmF;YAAA;UACA;QACA;QAEA,IAAAhL,IAAA,CAAAgJ,SAAA;UACArG,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAgJ,SAAA,EAAAnG,OAAA,WAAA0I,MAAA;YAAA,IAAAC,MAAA,OAAA9F,eAAA,CAAAC,OAAA,EAAA4F,MAAA;cAAA3F,GAAA,GAAA4F,MAAA;cAAA3F,KAAA,GAAA2F,MAAA;YACA;YACA,IAAAR,QAAA,KAAApH,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YACA6G,YAAA,CAAAtE,IAAA;cAAAvG,IAAA,EAAA6F,GAAA;cAAAC,KAAA,EAAAmF;YAAA;UACA;QACA;QAEApJ,OAAA,CAAAC,GAAA,qBAAA+I,YAAA;;QAEA;QACAA,YAAA,CAAA7E,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;QAAA;QAEAjE,OAAA,CAAAC,GAAA,kBAAA+I,YAAA;;QAEA;QACA,IAAAa,SAAA;QACA,IAAAvG,UAAA;;QAEA;QACA0F,YAAA,CAAAc,OAAA,GAAA7I,OAAA,WAAAuD,IAAA,EAAAC,KAAA;UACAoF,SAAA,CAAAnF,IAAA,CAAAF,IAAA,CAAArG,IAAA;UACAmF,UAAA,CAAAoB,IAAA;YACAT,KAAA,EAAAO,IAAA,CAAAP,KAAA;YACAU,SAAA;cAAAC,KAAA,EAAArB,MAAA,CAAAkB,KAAA,GAAAlB,MAAA,CAAAW,MAAA;YAAA;UACA;QACA;QAEAlE,OAAA,CAAAC,GAAA,UAAA4J,SAAA;QACA7J,OAAA,CAAAC,GAAA,gBAAAqD,UAAA;;QAEA;QACA,IAAAwB,MAAA;UACAwB,KAAA;YACAf,IAAA;YACAnH,IAAA,EAAAyL,SAAA;YACA7D,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACAtI,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAkF;UACA;QACA;QAEA,KAAAhE,MAAA,CAAAyJ,uBAAA,CAAArC,SAAA,CAAA5B,MAAA;QACA9E,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACA0I,6BAAA,WAAAA,8BAAAvK,IAAA;MACA,SAAAkB,MAAA,CAAAyK,uBAAA,IAAA3L,IAAA;QACA;QACA,IAAA4K,YAAA;QACA,IAAAzF,MAAA;;QAEA;QACA,IAAAnF,IAAA,CAAA4L,iBAAA;UACAjJ,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAA4L,iBAAA,EAAA/I,OAAA,WAAAgJ,MAAA;YAAA,IAAAC,MAAA,OAAApG,eAAA,CAAAC,OAAA,EAAAkG,MAAA;cAAAjG,GAAA,GAAAkG,MAAA;cAAAjG,KAAA,GAAAiG,MAAA;YACAlB,YAAA,CAAAtE,IAAA;cAAAvG,IAAA,EAAA6F,GAAA;cAAAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YAAA;UACA;QACA;QAEA,IAAA/D,IAAA,CAAA+L,uBAAA;UACApJ,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAA+L,uBAAA,EAAAlJ,OAAA,WAAAmJ,MAAA;YAAA,IAAAC,MAAA,OAAAvG,eAAA,CAAAC,OAAA,EAAAqG,MAAA;cAAApG,GAAA,GAAAqG,MAAA;cAAApG,KAAA,GAAAoG,MAAA;YACArB,YAAA,CAAAtE,IAAA;cAAAvG,IAAA,EAAA6F,GAAA;cAAAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YAAA;UACA;QACA;QAEA,IAAA/D,IAAA,CAAAkM,0BAAA;UACAvJ,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAkM,0BAAA,EAAArJ,OAAA,WAAAsJ,MAAA;YAAA,IAAAC,MAAA,OAAA1G,eAAA,CAAAC,OAAA,EAAAwG,MAAA;cAAAvG,GAAA,GAAAwG,MAAA;cAAAvG,KAAA,GAAAuG,MAAA;YACAxB,YAAA,CAAAtE,IAAA;cAAAvG,IAAA,EAAA6F,GAAA;cAAAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YAAA;UACA;QACA;QAEA,IAAA/D,IAAA,CAAAqM,UAAA;UACA1J,MAAA,CAAA2C,OAAA,CAAAtF,IAAA,CAAAqM,UAAA,EAAAxJ,OAAA,WAAAyJ,MAAA;YAAA,IAAAC,MAAA,OAAA7G,eAAA,CAAAC,OAAA,EAAA2G,MAAA;cAAA1G,GAAA,GAAA2G,MAAA;cAAA1G,KAAA,GAAA0G,MAAA;YACA3B,YAAA,CAAAtE,IAAA;cAAAvG,IAAA,EAAA6F,GAAA;cAAAC,KAAA,IAAAjC,MAAA,CAAAiC,KAAA,iBAAA9B,OAAA;YAAA;UACA;QACA;;QAEA;QACA6G,YAAA,CAAA7E,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;QAAA;;QAEA;QACA,IAAA4F,SAAA;QACA,IAAAvG,UAAA;;QAEA;QACA0F,YAAA,CAAAc,OAAA,GAAA7I,OAAA,WAAAuD,IAAA,EAAAC,KAAA;UACAoF,SAAA,CAAAnF,IAAA,CAAAF,IAAA,CAAArG,IAAA;UACAmF,UAAA,CAAAoB,IAAA;YACAT,KAAA,EAAAO,IAAA,CAAAP,KAAA;YACAU,SAAA;cAAAC,KAAA,EAAArB,MAAA,CAAAkB,KAAA,GAAAlB,MAAA,CAAAW,MAAA;YAAA;UACA;QACA;;QAEA;QACA,IAAAY,MAAA;UACAwB,KAAA;YACAf,IAAA;YACAnH,IAAA,EAAAyL,SAAA;YACA7D,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACAtI,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAkF;UACA;QACA;QAEA,KAAAhE,MAAA,CAAAyK,uBAAA,CAAArD,SAAA,CAAA5B,MAAA;QACA9E,OAAA,CAAAC,GAAA;MACA;IACA;IAEA2K,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAtL,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAmL,+BAAA,EAAAhI,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,0BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACAyM,MAAA,CAAAhL,eAAA,GAAAkD,QAAA,CAAA3E,IAAA,CAAAyB,eAAA;UACAgL,MAAA,CAAAjL,iBAAA,GAAAmD,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACA;QACA2H,MAAA,CAAA1H,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACA4H,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAxL,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAqL,gCAAA,EAAAlI,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,2BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA2M,MAAA,CAAAC,oBAAA,CAAAjI,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACA;QACA6H,MAAA,CAAA5H,QAAA,CAAAD,KAAA;MACA;IACA;IAEA+H,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA3L,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAwL,8BAAA,EAAArI,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,yBAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA8M,MAAA,CAAAC,gBAAA,CAAApI,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAgI,MAAA,CAAA/H,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACAiI,gBAAA,WAAAA,iBAAA/M,IAAA;MACA,SAAAkB,MAAA,CAAA8L,UAAA,IAAAhN,IAAA;QACA4B,OAAA,CAAAC,GAAA,sBAAA7B,IAAA;;QAEA;QACA,IAAAiN,MAAA,QAAAC,0CAAA;QACAtL,OAAA,CAAAC,GAAA,aAAAoL,MAAA;;QAEA;QACA,IAAAE,UAAA,QAAAC,oCAAA;QACAxL,OAAA,CAAAC,GAAA,aAAAsL,UAAA;QAEA,IAAAE,eAAA;QACA,IAAAC,mBAAA;;QAEA;QACAH,UAAA,CAAAtK,OAAA,WAAA4B,SAAA;UACA;UACA,IAAA8I,YAAA,GAAAvN,IAAA,CAAAwN,cAAA,IAAAxN,IAAA,CAAAwN,cAAA,CAAA/I,SAAA,IACA,EAAAb,MAAA,CAAA5D,IAAA,CAAAwN,cAAA,CAAA/I,SAAA,kBAAAV,OAAA,MACA;UACAsJ,eAAA,CAAA/G,IAAA,CAAAiH,YAAA;;UAEA;UACA,IAAAE,gBAAA,GAAAzN,IAAA,CAAA0N,kBAAA,IAAA1N,IAAA,CAAA0N,kBAAA,CAAAjJ,SAAA,IACA,EAAAb,MAAA,CAAA5D,IAAA,CAAA0N,kBAAA,CAAAjJ,SAAA,kBAAAV,OAAA,MACA;UACAuJ,mBAAA,CAAAhH,IAAA,CAAAmH,gBAAA;QACA;QAEA7L,OAAA,CAAAC,GAAA,YAAAoL,MAAA,CAAA1H,GAAA,WAAAjF,KAAA;UAAA,UAAAQ,MAAA,CAAAR,KAAA;QAAA;QACAsB,OAAA,CAAAC,GAAA,YAAAwL,eAAA;QACAzL,OAAA,CAAAC,GAAA,YAAAyL,mBAAA;;QAEA;QACA,IAAA5G,MAAA;UACA;UACAiH,MAAA;YACA3N,IAAA;YAAA;YACAsH,SAAA;cAAAd,KAAA;YAAA;UACA;UACAG,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAY,KAAA;YACAR,IAAA;YACAyG,WAAA;YACA5N,IAAA,EAAAiN,MAAA,CAAA1H,GAAA,WAAAjF,KAAA;cAAA,UAAAQ,MAAA,CAAAR,KAAA;YAAA;YAAA;YACAsH,SAAA;cACApB,KAAA;cACAsB,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACApH,IAAA;YACAoI,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UAEA6B,MAAA,GACA;YACAtI,IAAA;YAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAqN,eAAA;YACAQ,MAAA;YAAA;YACA5F,SAAA;cAAAzB,KAAA;cAAAsH,KAAA;YAAA;YACAvH,SAAA;cAAAC,KAAA;YAAA;YACAuH,MAAA;YACAC,UAAA;UACA,GACA;YACAjO,IAAA;YAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAsN,mBAAA;YACAO,MAAA;YAAA;YACA5F,SAAA;cAAAzB,KAAA;cAAAsH,KAAA;YAAA;YACAvH,SAAA;cAAAC,KAAA;YAAA;YACAuH,MAAA;YACAC,UAAA;UACA;QAEA;QAEA,KAAA9M,MAAA,CAAA8L,UAAA,CAAA1E,SAAA,CAAA5B,MAAA;QACA9E,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAoM,wBAAA,WAAAA,yBAAA;MACA,IAAAhB,MAAA;MACA,IAAAiB,WAAA,OAAA/N,IAAA;MAEA,SAAAgO,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAjO,IAAA,CAAA+N,WAAA,CAAA7N,WAAA,IAAA6N,WAAA,CAAA3N,QAAA,KAAA4N,CAAA;QACA,IAAA7N,KAAA,GAAA8N,IAAA,CAAA7N,QAAA;QACA0M,MAAA,CAAA3G,IAAA,CAAAhG,KAAA;MACA;MAEA,OAAA2M,MAAA;IACA;IAEA;IACAoB,kBAAA,WAAAA,mBAAA;MACA,IAAAlB,UAAA;MACA,IAAAe,WAAA,OAAA/N,IAAA;MAEA,SAAAgO,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAjO,IAAA,CAAA+N,WAAA,CAAA7N,WAAA,IAAA6N,WAAA,CAAA3N,QAAA,KAAA4N,CAAA;QACA,IAAA/N,IAAA,GAAAgO,IAAA,CAAA/N,WAAA;QACA,IAAAC,KAAA,GAAA8N,IAAA,CAAA7N,QAAA;QACA,IAAAkE,SAAA,MAAA3D,MAAA,CAAAV,IAAA,EAAAU,MAAA,CAAAC,MAAA,CAAAT,KAAA,EAAAU,QAAA;QACAmM,UAAA,CAAA7G,IAAA,CAAA7B,SAAA;MACA;MAEA,OAAA0I,UAAA;IACA;IAEA;IACAD,0CAAA,WAAAA,2CAAA;MACA,IAAAD,MAAA;MAEA,UAAA7L,gBAAA;QACAQ,OAAA,CAAA6E,IAAA;QACA,YAAAwH,wBAAA;MACA;;MAEA;MACA,IAAAK,qBAAA,QAAAlN,gBAAA,CAAAmN,KAAA,MAAAhJ,GAAA,CAAA3B,MAAA;QAAA4K,sBAAA,OAAA9I,eAAA,CAAAC,OAAA,EAAA2I,qBAAA;QAAAlO,IAAA,GAAAoO,sBAAA;QAAAlO,KAAA,GAAAkO,sBAAA;MACA,IAAAC,cAAA,OAAAtO,IAAA,CAAAC,IAAA,EAAAE,KAAA;;MAEA,SAAA6N,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAjO,IAAA,CAAAsO,cAAA,CAAApO,WAAA,IAAAoO,cAAA,CAAAlO,QAAA,KAAA4N,CAAA;QACA,IAAAO,QAAA,GAAAN,IAAA,CAAA7N,QAAA;QACA0M,MAAA,CAAA3G,IAAA,CAAAoI,QAAA;MACA;MAEA,OAAAzB,MAAA;IACA;IAEA;IACAG,oCAAA,WAAAA,qCAAA;MACA,IAAAD,UAAA;MAEA,UAAA/L,gBAAA;QACAQ,OAAA,CAAA6E,IAAA;QACA,YAAA4H,kBAAA;MACA;;MAEA;MACA,IAAAM,sBAAA,QAAAvN,gBAAA,CAAAmN,KAAA,MAAAhJ,GAAA,CAAA3B,MAAA;QAAAgL,sBAAA,OAAAlJ,eAAA,CAAAC,OAAA,EAAAgJ,sBAAA;QAAAvO,IAAA,GAAAwO,sBAAA;QAAAtO,KAAA,GAAAsO,sBAAA;MACA,IAAAH,cAAA,OAAAtO,IAAA,CAAAC,IAAA,EAAAE,KAAA;;MAEA,SAAA6N,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAjO,IAAA,CAAAsO,cAAA,CAAApO,WAAA,IAAAoO,cAAA,CAAAlO,QAAA,KAAA4N,CAAA;QACA,IAAAU,OAAA,GAAAT,IAAA,CAAA/N,WAAA;QACA,IAAAqO,QAAA,GAAAN,IAAA,CAAA7N,QAAA;QACA,IAAAkE,SAAA,MAAA3D,MAAA,CAAA+N,OAAA,EAAA/N,MAAA,CAAAC,MAAA,CAAA2N,QAAA,EAAA1N,QAAA;QACAmM,UAAA,CAAA7G,IAAA,CAAA7B,SAAA;MACA;MAEA,OAAA0I,UAAA;IACA;IAEA;IACA2B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA5N,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAGA,IAAAyN,0BAAA,EAAAtK,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,qBAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA+O,MAAA,CAAAC,cAAA,CAAArK,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,cAAAA,KAAA;QACAiK,MAAA,CAAAhK,QAAA,CAAAD,KAAA;MACA;IACA;IACA;IACA7C,iBAAA,WAAAA,kBAAA;MAAA,IAAAgN,MAAA;MACA,KAAA1N,iBAAA;MACA,IAAA2N,iCAAA,IAAAxK,IAAA,WAAAC,QAAA;QACAsK,MAAA,CAAA3N,iBAAA,GAAAqD,QAAA,CAAA3E,IAAA;QACA;QACA,IAAAiP,MAAA,CAAA3N,iBAAA,CAAAwE,MAAA;UACAlE,OAAA,CAAAC,GAAA,cAAAoN,MAAA,CAAA3N,iBAAA;UACA2N,MAAA,CAAA9N,UAAA,GAAA8N,MAAA,CAAA3N,iBAAA,IAAAsE,GAAA;UACA;UACAqJ,MAAA,CAAAE,SAAA;YACAF,MAAA,CAAAnN,gBAAA;UACA;QACA;MACA,GAAA+C,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,gBAAAA,KAAA;QACAmK,MAAA,CAAAlK,QAAA,CAAAD,KAAA;MACA,GAAAsK,OAAA;QACAH,MAAA,CAAA1N,iBAAA;MACA;IACA;IAEA;IACAyN,cAAA,WAAAA,eAAAhP,IAAA;MACA,SAAAkB,MAAA,CAAAmO,QAAA,IAAArP,IAAA;QACA;QACA,IAAA0G,MAAA,QAAAxF,MAAA,CAAAmO,QAAA,CAAAC,SAAA;QACA,IAAA5I,MAAA,IAAAA,MAAA,CAAA2B,MAAA,IAAA3B,MAAA,CAAA2B,MAAA;UACA3B,MAAA,CAAA2B,MAAA,IAAArI,IAAA,IACA;YAAA6F,KAAA,GAAA7F,IAAA,CAAAuP,cAAA,UAAAxL,OAAA;YAAAhE,IAAA;YAAAwG,SAAA;cAAAC,KAAA;YAAA;UAAA,GACA;YAAAX,KAAA,GAAA7F,IAAA,CAAAwP,aAAA,UAAAzL,OAAA;YAAAhE,IAAA;YAAAwG,SAAA;cAAAC,KAAA;YAAA;UAAA,GACA;YAAAX,KAAA,GAAA7F,IAAA,CAAAyP,YAAA,UAAA1L,OAAA;YAAAhE,IAAA;YAAAwG,SAAA;cAAAC,KAAA;YAAA;UAAA,GACA;YAAAX,KAAA,GAAA7F,IAAA,CAAA0P,YAAA,UAAA3L,OAAA;YAAAhE,IAAA;YAAAwG,SAAA;cAAAC,KAAA;YAAA;UAAA,EACA,EACA,KAAAtF,MAAA,CAAAmO,QAAA,CAAA/G,SAAA,CAAA5B,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAkG,oBAAA,WAAAA,qBAAA5M,IAAA;MACA,SAAAkB,MAAA,CAAAyO,cAAA,IAAA3P,IAAA;QACA;QACA,IAAAiN,MAAA,QAAAC,0CAAA;QACA,IAAAC,UAAA,QAAAC,oCAAA;;QAEA;QACA,IAAAwC,cAAA,QAAAC,cAAA,CAAA7P,IAAA,CAAA8P,iBAAA,EAAA3C,UAAA;QACA,IAAA4C,aAAA,QAAAF,cAAA,CAAA7P,IAAA,CAAAgQ,gBAAA,EAAA7C,UAAA;QACA,IAAA8C,YAAA,QAAAJ,cAAA,CAAA7P,IAAA,CAAAkQ,eAAA,EAAA/C,UAAA;QACA,IAAAgD,YAAA,QAAAN,cAAA,CAAA7P,IAAA,CAAAoQ,eAAA,EAAAjD,UAAA;;QAEA;QACA,IAAAzG,MAAA;UACAiB,KAAA;YACAR,IAAA;YACAyG,WAAA;YACA5N,IAAA,EAAAiN,MAAA,CAAA1H,GAAA,WAAAjF,KAAA;cAAA,UAAAQ,MAAA,CAAAR,KAAA;YAAA;YAAA;YACAsH,SAAA;cACApB,KAAA;cACAsB,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA,GACA;YACAtI,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAA4P,cAAA;YACA/B,MAAA;YAAA;YACA5F,SAAA;cAAAzB,KAAA;cAAAsH,KAAA;YAAA;YACAvH,SAAA;cAAAC,KAAA;YAAA;YACAuH,MAAA;YACAC,UAAA;UACA,GACA;YACAjO,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAA+P,aAAA;YACAlC,MAAA;YAAA;YACA5F,SAAA;cAAAzB,KAAA;cAAAsH,KAAA;YAAA;YACAvH,SAAA;cAAAC,KAAA;YAAA;YACAuH,MAAA;YACAC,UAAA;UACA,GACA;YACAjO,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAiQ,YAAA;YACApC,MAAA;YAAA;YACA5F,SAAA;cAAAzB,KAAA;cAAAsH,KAAA;YAAA;YACAvH,SAAA;cAAAC,KAAA;YAAA;YACAuH,MAAA;YACAC,UAAA;UACA,GACA;YACAjO,IAAA;YACAoH,IAAA;YACAnH,IAAA,EAAAmQ,YAAA;YACAtC,MAAA;YAAA;YACA5F,SAAA;cAAAzB,KAAA;cAAAsH,KAAA;YAAA;YACAvH,SAAA;cAAAC,KAAA;YAAA;YACAuH,MAAA;YACAC,UAAA;UACA;QAEA;QAEA,KAAA9M,MAAA,CAAAyO,cAAA,CAAArH,SAAA,CAAA5B,MAAA;QACA9E,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAgO,cAAA,WAAAA,eAAAQ,OAAA,EAAAlD,UAAA;MAAA,IAAAmD,gBAAA,GAAAC,SAAA,CAAAzK,MAAA,QAAAyK,SAAA,QAAA7M,SAAA,GAAA6M,SAAA;MACA,KAAAF,OAAA,aAAAG,KAAA,CAAArD,UAAA,CAAArH,MAAA,EAAA2K,IAAA;MAEA,OAAAtD,UAAA,CAAA5H,GAAA,WAAAd,SAAA;QACA,IAAAoB,KAAA,GAAAwK,OAAA,CAAA5L,SAAA;QACA,OAAA6L,gBAAA,IAAAzK,KAAA,UAAA9B,OAAA,MAAA8B,KAAA;MACA;IACA;IAEA;IACA6K,mBAAA,WAAAA,oBAAA;MACA,IAAAzD,MAAA;MACA,IAAAE,UAAA;MACA,IAAAe,WAAA,OAAA/N,IAAA;MAEA,SAAAgO,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAAjO,IAAA,CAAA+N,WAAA,CAAA7N,WAAA,IAAA6N,WAAA,CAAA3N,QAAA,KAAA4N,CAAA;QACA,IAAA/N,IAAA,GAAAgO,IAAA,CAAA/N,WAAA;QACA,IAAAC,KAAA,GAAA8N,IAAA,CAAA7N,QAAA;QAEA0M,MAAA,CAAA3G,IAAA,IAAAxF,MAAA,CAAAR,KAAA;QACA6M,UAAA,CAAA7G,IAAA,CAAAqK,QAAA,IAAA7P,MAAA,CAAAV,IAAA,EAAAU,MAAA,CAAAC,MAAA,CAAAT,KAAA,EAAAU,QAAA;MACA;MAEA;QAAAiM,MAAA,EAAAA,MAAA;QAAAE,UAAA,EAAAA;MAAA;IACA;IAEA;IACArL,gBAAA,WAAAA,iBAAA;MACA;MACA,UAAAX,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACA;MACA;MAEA,KAAAmL,oBAAA;MACA,KAAAsC,eAAA;MACA,KAAApC,qBAAA;MACA,KAAArC,qBAAA;MACA,KAAAG,qBAAA;MACA,KAAAqC,mBAAA;MACA,KAAAvI,uBAAA;MACA,KAAA6F,8BAAA;MACA,KAAAF,6BAAA;;MAEA;MACA;IACA;IAEA,aACA2G,WAAA,WAAAA,YAAA;MACA,KAAA9O,gBAAA;IACA;IAEA,aACA+O,UAAA,WAAAA,WAAA;MACA;MACA,SAAAvP,iBAAA,CAAAwE,MAAA;QACA,KAAA3E,UAAA,QAAAG,iBAAA,IAAAsE,GAAA;MACA;;MAEA;MACA,IAAA1F,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAJ,GAAA,CAAAK,QAAA;MACA,IAAAK,SAAA,GAAAN,KAAA,cAAAA,KAAA;MACA,IAAAO,QAAA,GAAAP,KAAA,SAAAF,IAAA,OAAAA,IAAA;MACA,KAAAgB,gBAAA,MAAAN,MAAA,CAAAD,QAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAH,SAAA,EAAAI,QAAA;MAEA,KAAA+D,QAAA,CAAA+L,OAAA;IACA;IAEA5O,UAAA,WAAAA,WAAA;MACA,IAAA6O,KAAA;;MAEA;MACA,KAAAC,cAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAC,MAAA;UAAA3K,KAAA;QAAA,GACA;UAAA2K,MAAA;UAAA3K,KAAA;QAAA;MAEA;;MAEA;MACA,KAAAtF,MAAA,CAAAmO,QAAA,GAAA3P,OAAA,CAAA0R,IAAA,MAAAC,KAAA,CAAAhC,QAAA,EAAA0B,KAAA;MACA,KAAA7P,MAAA,CAAAyO,cAAA,GAAAjQ,OAAA,CAAA0R,IAAA,MAAAC,KAAA,CAAA1B,cAAA,EAAAoB,KAAA;MACA,KAAA7P,MAAA,CAAAyK,uBAAA,GAAAjM,OAAA,CAAA0R,IAAA,MAAAC,KAAA,CAAA1F,uBAAA,EAAAoF,KAAA;MACA,KAAA7P,MAAA,CAAAyJ,uBAAA,GAAAjL,OAAA,CAAA0R,IAAA,MAAAC,KAAA,CAAA1G,uBAAA,EAAAoG,KAAA;MACA,KAAA7P,MAAA,CAAA8D,cAAA,GAAAtF,OAAA,CAAA0R,IAAA,MAAAC,KAAA,CAAArM,cAAA,EAAA+L,KAAA;MACA,KAAA7P,MAAA,CAAA8L,UAAA,GAAAtN,OAAA,CAAA0R,IAAA,MAAAC,KAAA,CAAArE,UAAA,EAAA+D,KAAA;MACA,KAAA7P,MAAA,CAAAsH,cAAA,GAAA9I,OAAA,CAAA0R,IAAA,MAAAC,KAAA,CAAA7I,cAAA,EAAAuI,KAAA;MACA,KAAA7P,MAAA,CAAAuI,qBAAA,GAAA/J,OAAA,CAAA0R,IAAA,MAAAC,KAAA,CAAA5H,qBAAA,EAAAsH,KAAA;;MAEA;MACA,KAAAO,iBAAA;MACA,KAAAC,uBAAA;MACA,KAAAC,gCAAA;MACA,KAAAC,gCAAA;MACA,KAAAC,uBAAA;MACA,KAAAC,mBAAA;MACA,KAAAC,uBAAA;MACA,KAAAC,8BAAA;IACA;IAEAP,iBAAA,WAAAA,kBAAA;MACA,KAAApQ,MAAA,CAAAmO,QAAA,CAAA/G,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAM,SAAA,WAAAA,UAAA/C,MAAA;YACA,IAAAqB,KAAA,GAAA/B,UAAA,CAAAU,MAAA,CAAAqB,KAAA,EAAA9B,OAAA;YACA,IAAAyD,cAAA,GAAA3B,KAAA,CAAAxC,QAAA,GAAAY,OAAA;YACA,UAAAnD,MAAA,CAAA0D,MAAA,CAAAkD,UAAA,YAAA5G,MAAA,CAAA0D,MAAA,CAAAzE,IAAA,QAAAe,MAAA,CAAA0G,cAAA,oBAAA1G,MAAA,CAAA0D,MAAA,CAAAsN,OAAA;UACA;UACA1K,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAmH,MAAA;UACAoE,GAAA;UACAnL,IAAA;UACAU,SAAA;YAAAd,KAAA;YAAAwL,QAAA;UAAA;QACA;QACA3J,MAAA;UACAtI,IAAA;UACAoH,IAAA;UACA8K,MAAA;UACAjS,IAAA;UACAkS,QAAA;YACA3L,SAAA;cACA4L,UAAA;cACAC,aAAA;cACAC,WAAA;YACA;UACA;UACAC,SAAA;YAAArK,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA+L,KAAA;YACA/L,KAAA;YACAe,SAAA,WAAAA,UAAA/C,MAAA;cACA,IAAAqB,KAAA,GAAA/B,UAAA,CAAAU,MAAA,CAAAqB,KAAA,EAAA9B,OAAA;cACA,IAAAyD,cAAA,GAAA3B,KAAA,CAAAxC,QAAA,GAAAY,OAAA;cACA,UAAAnD,MAAA,CAAA0D,MAAA,CAAAzE,IAAA,OAAAe,MAAA,CAAA0G,cAAA;YACA;UACA;QACA;MACA;IACA;IAIAmK,mBAAA,WAAAA,oBAAA;MACA,KAAAzQ,MAAA,CAAA8L,UAAA,CAAA1E,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAmH,MAAA;UACA3N,IAAA;UACAsH,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACAyG,WAAA;UACA5N,IAAA;UACA4H,SAAA;YACApB,KAAA;YACAsB,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACApH,IAAA;UACAoI,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA,GACA;UACAtI,IAAA;UAAA;UACAoH,IAAA;UACAnH,IAAA;UACA6N,MAAA;UAAA;UACA5F,SAAA;YAAAzB,KAAA;YAAAsH,KAAA;UAAA;UACAvH,SAAA;YAAAC,KAAA;UAAA;UACAuH,MAAA;UACAC,UAAA;QACA,GACA;UACAjO,IAAA;UAAA;UACAoH,IAAA;UACAnH,IAAA;UACA6N,MAAA;UAAA;UACA5F,SAAA;YAAAzB,KAAA;YAAAsH,KAAA;UAAA;UACAvH,SAAA;YAAAC,KAAA;UAAA;UACAuH,MAAA;UACAC,UAAA;QACA;MAEA;IACA;IAEAuD,uBAAA,WAAAA,wBAAA;MACA,KAAArQ,MAAA,CAAAyO,cAAA,CAAArH,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAG,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAmH,MAAA;UACA3N,IAAA;UACAsH,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACAyG,WAAA;UACA5N,IAAA;UACA4H,SAAA;YACApB,KAAA;YACAsB,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACApH,IAAA;UACAoI,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA,GACA;UACAtI,IAAA;UACAoH,IAAA;UACAnH,IAAA;UACA6N,MAAA;UAAA;UACA5F,SAAA;YAAAzB,KAAA;YAAAsH,KAAA;UAAA;UACAvH,SAAA;YAAAC,KAAA;UAAA;UACAuH,MAAA;UACAC,UAAA;QACA,GACA;UACAjO,IAAA;UACAoH,IAAA;UACAnH,IAAA;UACA6N,MAAA;UAAA;UACA5F,SAAA;YAAAzB,KAAA;YAAAsH,KAAA;UAAA;UACAvH,SAAA;YAAAC,KAAA;UAAA;UACAuH,MAAA;UACAC,UAAA;QACA,GACA;UACAjO,IAAA;UACAoH,IAAA;UACAnH,IAAA;UACA6N,MAAA;UAAA;UACA5F,SAAA;YAAAzB,KAAA;YAAAsH,KAAA;UAAA;UACAvH,SAAA;YAAAC,KAAA;UAAA;UACAuH,MAAA;UACAC,UAAA;QACA,GACA;UACAjO,IAAA;UACAoH,IAAA;UACAnH,IAAA;UACA6N,MAAA;UAAA;UACA5F,SAAA;YAAAzB,KAAA;YAAAsH,KAAA;UAAA;UACAvH,SAAA;YAAAC,KAAA;UAAA;UACAuH,MAAA;UACAC,UAAA;QACA;MAEA;IACA;IAMAwE,oBAAA,WAAAA,qBAAA;MACA,KAAAtR,MAAA,CAAAuR,WAAA,CAAAnK,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAE,KAAA;QAAA;QACAc,KAAA;UACAR,IAAA;UACAnH,IAAA;UACA4H,SAAA;YACAC,QAAA;YACAC,MAAA;YAAA;YACAC,KAAA;YAAA;YACAvB,KAAA;UACA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA,GACA;UACAf,IAAA;UACApH,IAAA;UACAoI,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA,GACA;UACAW,IAAA;UACApH,IAAA;UACA2S,GAAA;UACAC,GAAA;UACA/K,SAAA;YACAL,SAAA;YACAf,KAAA;UACA;UACA2B,aAAA;YAAA3B,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA,EACA;QACA6B,MAAA,GACA;UACAtI,IAAA;UACAoH,IAAA;UACAnH,IAAA;UACAuG,SAAA;YAAAC,KAAA;UAAA;QACA,GACA;UACAzG,IAAA;UACAoH,IAAA;UACAyL,UAAA;UACA5S,IAAA;UACAiI,SAAA;YAAAzB,KAAA;YAAAsH,KAAA;UAAA;UACAvH,SAAA;YAAAC,KAAA;UAAA;UACAuH,MAAA;UACAC,UAAA;QACA;MAEA;IACA;IAEAwD,gCAAA,WAAAA,iCAAA;MACA,KAAAtQ,MAAA,CAAAyK,uBAAA,CAAArD,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;UACAe,SAAA,WAAAA,UAAA/C,MAAA;YACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;YACAyE,MAAA,CAAA3B,OAAA,WAAAuD,IAAA;cACA,IAAAoB,cAAA,GAAA1D,UAAA,CAAAsC,IAAA,CAAAP,KAAA,EAAA9B,OAAA,IAAAV,QAAA,GAAAY,OAAA;cACAE,MAAA,IAAAiC,IAAA,CAAAqB,MAAA,aAAAD,cAAA;YACA;YACA,OAAArD,MAAA;UACA;QACA;QACAwC,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAkL,GAAA;UAAAjL,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACApH,IAAA;UACAoI,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UAEAnH,IAAA;UACA4H,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACAtI,IAAA;UACAoH,IAAA;UACAnH,IAAA;QACA;MACA;IACA;IAEAyR,gCAAA,WAAAA,iCAAA;MACA,KAAAvQ,MAAA,CAAAyJ,uBAAA,CAAArC,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;UACAe,SAAA,WAAAA,UAAA/C,MAAA;YACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;YACAyE,MAAA,CAAA3B,OAAA,WAAAuD,IAAA;cACA,IAAAoB,cAAA,GAAA1D,UAAA,CAAAsC,IAAA,CAAAP,KAAA,EAAA9B,OAAA,IAAAV,QAAA,GAAAY,OAAA;cACAE,MAAA,IAAAiC,IAAA,CAAAqB,MAAA,aAAAD,cAAA;YACA;YACA,OAAArD,MAAA;UACA;QACA;QACAwC,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAkL,GAAA;UAAAjL,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACApH,IAAA;UACAoI,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACAnH,IAAA;UACA4H,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACAtI,IAAA;UACAoH,IAAA;UACAnH,IAAA;QACA;MACA;IACA;IAEA0R,uBAAA,WAAAA,wBAAA;MACA,KAAAxQ,MAAA,CAAA8D,cAAA,CAAAsD,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACAnH,IAAA;UACA4H,SAAA;YACAC,QAAA;YACAC,MAAA;YAAA;YACAC,KAAA;YAAA;YACAvB,KAAA;UACA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACApH,IAAA;UACAoI,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA,GACA;UACAtI,IAAA;UACAoH,IAAA;UACA0L,KAAA;UACAtM,SAAA;YACAC,KAAA;YACAa,WAAA;YACAyL,WAAA;UACA;UACAZ,QAAA;YACA3L,SAAA;cACAC,KAAA;YACA;UACA;UACAxG,IAAA;QACA;MAEA;IACA;IAEA4R,uBAAA,WAAAA,wBAAA;MACA,KAAA1Q,MAAA,CAAAsH,cAAA,CAAAF,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACAnH,IAAA;UACA4H,SAAA;YACApB,KAAA;YACAqB,QAAA;YAAA;YACAC,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACApH,IAAA;UACA6H,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACAtI,IAAA;UACAoH,IAAA;UACAnH,IAAA;QACA;MACA;IACA;IAEA6R,8BAAA,WAAAA,+BAAA;MACA,KAAA3Q,MAAA,CAAAuI,qBAAA,CAAAnB,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACAnH,IAAA;UACA4H,SAAA;YACApB,KAAA;YACAqB,QAAA;YAAA;YACAC,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACApH,IAAA;UACA6H,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACAtI,IAAA;UACAoH,IAAA;UACAnH,IAAA;QACA;MACA;IACA;IAEA+S,mBAAA,WAAAA,oBAAA;MACA,KAAA7R,MAAA,CAAA8R,UAAA,CAAA1K,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAmH,MAAA;UACA3N,IAAA;UACAsH,SAAA;YAAAd,KAAA;UAAA;QACA;QACAmB,KAAA;UACAR,IAAA;UACAnH,IAAA;UACA4H,SAAA;YACApB,KAAA;YACAsB,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA,GACA;UACAf,IAAA;UACApH,IAAA;UACAoI,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA,GACA;UACAW,IAAA;UACApH,IAAA;UACAoI,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA,EACA;QACA6B,MAAA,GACA;UACAtI,IAAA;UACAoH,IAAA;UACAnH,IAAA;UACAuG,SAAA;YAAAC,KAAA;UAAA;QACA,GACA;UACAzG,IAAA;UACAoH,IAAA;UACAyL,UAAA;UACA5S,IAAA;UACAiI,SAAA;YAAAzB,KAAA;YAAAsH,KAAA;UAAA;UACAvH,SAAA;YAAAC,KAAA;UAAA;UACAuH,MAAA;UACAC,UAAA;QACA;MAEA;IACA;IAEA3L,YAAA,WAAAA,aAAA;MACAM,MAAA,CAAAC,MAAA,MAAA1B,MAAA,EAAA2B,OAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAA,KAAA,CAAAmQ,MAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}