{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\qualityCostPage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\qualityCostPage\\index.vue", "mtime": 1754382562899}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UXVhbGl0eUNvc3RQYWdlLCBjb3N0Q2VudGVybGlzdCB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3F1YWxpdHlDb3N0RGV0YWlsIjsNCmltcG9ydCB7IGV4cG9ydFF1YWxpdHlDb3N0UGFnZSB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3F1YWxpdHlDb3N0UGFnZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlF1YWxpdHlDb3N0IiwNCiAgY29tcG9uZW50czogew0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g6LSo6YeP5oiQ5pys5pWw5o2u5YiX6KGoDQogICAgICBxdWFsaXR5Q29zdExpc3Q6IFtdLA0KICAgICAgLy8g5oiQ5pys5Lit5b+D6YCJ6aG5DQogICAgICBjb3N0Q2VudGVyT3B0aW9uczogW10sDQogICAgICAvLyDkuqfph4/kv6Hmga8NCiAgICAgIHByb2R1Y3Rpb25JbmZvOiBudWxsLA0KICAgICAgLy8g5ZCI5bm25Y2V5YWD5qC86YWN572uDQogICAgICBtZXJnZUNlbGxzOiBbXSwNCiAgICAgIC8vIOaIkOacrOexu+Wei+mAiemhuQ0KICAgICAgY29zdFR5cGVPcHRpb25zOiBbDQogICAgICAgIHsgdmFsdWU6ICcnLCBsYWJlbDogJ+WFqOmDqCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0EnLCBsYWJlbDogJ0Et6aKE6Ziy5oiQ5pysJyB9LA0KICAgICAgICB7IHZhbHVlOiAnQicsIGxhYmVsOiAnQi3pibTlrprmiJDmnKwnIH0sDQogICAgICAgIHsgdmFsdWU6ICdDJywgbGFiZWw6ICdDLeWGhemDqOaNn+WkseaIkOacrCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJ0QnLCBsYWJlbDogJ0Qt5aSW6YOo5o2f5aSx5oiQ5pysJyB9DQogICAgICBdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBjb3N0Q2VudGVyOiAnSllYQ1RaRycsIC8vIOm7mOiupOmAieS4reWFtOa+hOeJuemSouaxh+aAuw0KICAgICAgICB5ZWFyTW9udGg6IG51bGwsDQogICAgICAgIGNvc3RUeXBlOiBbJyddDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvKiog5Yik5pat5piv5ZCm5pi+56S65rGH5oC76KGMICovDQogICAgc2hvdWxkU2hvd1N1bW1hcnlSb3dzKCkgew0KICAgICAgLy8g5b2T5oiQ5pys57G75Z6L6YCJ5oup5Li6IuWFqOmDqCLml7bmmL7npLrmsYfmgLvooYwNCiAgICAgIHJldHVybiB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RUeXBlLmluY2x1ZGVzKCcnKSB8fCB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RUeXBlLmxlbmd0aCA9PT0gMDsNCiAgICB9LA0KICAgIC8qKiDorqHnrpfooajmoLzpq5jluqYgKi8NCiAgICB0YWJsZUhlaWdodCgpIHsNCiAgICAgIGlmICghdGhpcy5xdWFsaXR5Q29zdExpc3QgfHwgdGhpcy5xdWFsaXR5Q29zdExpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiAnYXV0byc7DQogICAgICB9DQoNCiAgICAgIC8vIOW9k+aIkOacrOexu+Wei+mAieaLqemdnuWFqOmDqOaXtu+8jOS9v+eUqGF1dG/pq5jluqbvvIzkuI3pmZDliLbmnIDlpKfpq5jluqYNCiAgICAgIGlmICghdGhpcy5zaG91bGRTaG93U3VtbWFyeVJvd3MpIHsNCiAgICAgICAgcmV0dXJuICdhdXRvJzsNCiAgICAgIH0NCg0KICAgICAgLy8g6K6h566X5omA6ZyA6auY5bqm77ya6KGo5aS06auY5bqmICsg5pWw5o2u6KGM6auY5bqmDQogICAgICBjb25zdCBoZWFkZXJIZWlnaHQgPSA0MDsgLy8g6KGo5aS06auY5bqmDQogICAgICBjb25zdCByb3dIZWlnaHQgPSAzMjsgLy8g5q+P6KGM5pWw5o2u6auY5bqmDQogICAgICBjb25zdCBwYWRkaW5nID0gMTA7IC8vIOmineWklumXtOi3nQ0KDQogICAgICBjb25zdCBkYXRhUm93c0NvdW50ID0gdGhpcy5xdWFsaXR5Q29zdExpc3QubGVuZ3RoOw0KICAgICAgY29uc3QgY2FsY3VsYXRlZEhlaWdodCA9IGhlYWRlckhlaWdodCArIChkYXRhUm93c0NvdW50ICogcm93SGVpZ2h0KSArIHBhZGRpbmc7DQoNCiAgICAgIC8vIOacgOWkp+mrmOW6pumZkOWItu+8iDgwdmggLSA4MHB477yJDQogICAgICBjb25zdCBtYXhIZWlnaHQgPSB3aW5kb3cuaW5uZXJIZWlnaHQgKiAwLjggLSA4MDsNCg0KICAgICAgLy8g6L+U5Zue6K6h566X6auY5bqm5ZKM5pyA5aSn6auY5bqm5Lit55qE6L6D5bCP5YC8DQogICAgICByZXR1cm4gTWF0aC5taW4oY2FsY3VsYXRlZEhlaWdodCwgbWF4SGVpZ2h0KSArICdweCc7DQogICAgfSwNCiAgICAvKiog5Yik5pat5piv5ZCm6ZyA6KaB5pi+56S65rua5Yqo5p2hICovDQogICAgbmVlZFNjcm9sbGJhcigpIHsNCiAgICAgIC8vIOW9k+aIkOacrOexu+Wei+mAieaLqemdnuWFqOmDqOaXtu+8jOS4jeaYvuekuua7muWKqOadoQ0KICAgICAgaWYgKCF0aGlzLnNob3VsZFNob3dTdW1tYXJ5Um93cykgew0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy5xdWFsaXR5Q29zdExpc3QgfHwgdGhpcy5xdWFsaXR5Q29zdExpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgaGVhZGVySGVpZ2h0ID0gNDA7DQogICAgICBjb25zdCByb3dIZWlnaHQgPSAzMjsNCiAgICAgIGNvbnN0IHBhZGRpbmcgPSAxMDsNCiAgICAgIGNvbnN0IGRhdGFSb3dzQ291bnQgPSB0aGlzLnF1YWxpdHlDb3N0TGlzdC5sZW5ndGg7DQogICAgICBjb25zdCBjYWxjdWxhdGVkSGVpZ2h0ID0gaGVhZGVySGVpZ2h0ICsgKGRhdGFSb3dzQ291bnQgKiByb3dIZWlnaHQpICsgcGFkZGluZzsNCiAgICAgIGNvbnN0IG1heEhlaWdodCA9IHdpbmRvdy5pbm5lckhlaWdodCAqIDAuOCAtIDgwOw0KDQogICAgICByZXR1cm4gY2FsY3VsYXRlZEhlaWdodCA+IG1heEhlaWdodDsNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5xdWVyeVBhcmFtcy55ZWFyTW9udGggPSB0aGlzLmdldERlZmF1bHRZZWFyTW9udGgoKTsNCiAgICB0aGlzLmdldENvc3RDZW50ZXJMaXN0KCk7DQogICAgLy8g6aG16Z2i5Yqg6L295pe26Ieq5Yqo6Kem5Y+R5pCc57SiDQogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0pOw0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIC8vIOebkeWQrOeql+WPo+Wkp+Wwj+WPmOWMlg0KICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLmhhbmRsZVJlc2l6ZSk7DQogIH0sDQogIGJlZm9yZURlc3Ryb3koKSB7DQogICAgLy8g56e76Zmk56qX5Y+j5aSn5bCP5Y+Y5YyW55uR5ZCsDQogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDojrflj5bmiJDmnKzkuK3lv4PliJfooaggKi8NCiAgICBnZXRDb3N0Q2VudGVyTGlzdCgpIHsNCiAgICAgIGNvc3RDZW50ZXJsaXN0KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnN0IG9wdGlvbnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgICAvLyDlsIZKWVhDVFpH6YCJ6aG55o6S5Zyo56ys5LiA5L2NDQogICAgICAgIGNvbnN0IGp5eGN0emdPcHRpb24gPSBvcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLmtleSA9PT0gJ0pZWENUWkcnKTsNCiAgICAgICAgY29uc3Qgb3RoZXJPcHRpb25zID0gb3B0aW9ucy5maWx0ZXIoaXRlbSA9PiBpdGVtLmtleSAhPT0gJ0pZWENUWkcnKTsNCg0KICAgICAgICBpZiAoanl4Y3R6Z09wdGlvbikgew0KICAgICAgICAgIHRoaXMuY29zdENlbnRlck9wdGlvbnMgPSBbanl4Y3R6Z09wdGlvbiwgLi4ub3RoZXJPcHRpb25zXTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmNvc3RDZW50ZXJPcHRpb25zID0gb3B0aW9uczsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmiJDmnKzkuK3lv4PliJfooajlpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5oiQ5pys57G75Z6LICovDQogICAgZ2V0Q29zdFR5cGUodHlwZUNvZGUpIHsNCiAgICAgIGlmICghdHlwZUNvZGUpIHJldHVybiAnJzsNCiAgICAgIGNvbnN0IGZpcnN0Q2hhciA9IHR5cGVDb2RlLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpOw0KICAgICAgc3dpdGNoIChmaXJzdENoYXIpIHsNCiAgICAgICAgY2FzZSAnQSc6IHJldHVybiAn6aKE6Ziy5oiQ5pysJzsNCiAgICAgICAgY2FzZSAnQic6IHJldHVybiAn6Ym05a6a5oiQ5pysJzsNCiAgICAgICAgY2FzZSAnQyc6IHJldHVybiAn5YaF6YOo5o2f5aSx5oiQ5pysJzsNCiAgICAgICAgY2FzZSAnRCc6IHJldHVybiAn5aSW6YOo5o2f5aSx5oiQ5pysJzsNCiAgICAgICAgZGVmYXVsdDogcmV0dXJuICfmsYfmgLsnOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog6K6h566X5ZCI5bm25Y2V5YWD5qC8ICovDQogICAgY2FsY3VsYXRlTWVyZ2VDZWxscygpIHsNCiAgICAgIGNvbnN0IG1lcmdlQ2VsbHMgPSBbXTsNCiAgICAgIGNvbnN0IGNvc3RUeXBlR3JvdXBzID0ge307DQoNCiAgICAgIC8vIOaMieaIkOacrOexu+Wei+WIhue7hA0KICAgICAgdGhpcy5xdWFsaXR5Q29zdExpc3QuZm9yRWFjaCgocm93LCBpbmRleCkgPT4gew0KICAgICAgICBjb25zdCBjb3N0VHlwZSA9IHJvdy5jb3N0VHlwZTsNCiAgICAgICAgaWYgKCFjb3N0VHlwZUdyb3Vwc1tjb3N0VHlwZV0pIHsNCiAgICAgICAgICBjb3N0VHlwZUdyb3Vwc1tjb3N0VHlwZV0gPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBjb3N0VHlwZUdyb3Vwc1tjb3N0VHlwZV0ucHVzaChpbmRleCk7DQogICAgICB9KTsNCg0KICAgICAgLy8g55Sf5oiQ5ZCI5bm26YWN572uDQogICAgICBPYmplY3QudmFsdWVzKGNvc3RUeXBlR3JvdXBzKS5mb3JFYWNoKGdyb3VwID0+IHsNCiAgICAgICAgaWYgKGdyb3VwLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgICBtZXJnZUNlbGxzLnB1c2goew0KICAgICAgICAgICAgcm93OiBncm91cFswXSwNCiAgICAgICAgICAgIGNvbDogMCwNCiAgICAgICAgICAgIHJvd3NwYW46IGdyb3VwLmxlbmd0aCwNCiAgICAgICAgICAgIGNvbHNwYW46IDENCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIHRoaXMubWVyZ2VDZWxscyA9IG1lcmdlQ2VsbHM7DQogICAgfSwNCg0KICAgIC8qKiDmoLzlvI/ljJbmlbDlrZcgKi8NCiAgICBmb3JtYXROdW1iZXIobnVtKSB7DQogICAgICBpZiAobnVtID09PSBudWxsIHx8IG51bSA9PT0gdW5kZWZpbmVkIHx8IG51bSA9PT0gJycgfHwgbnVtID09PSAwKSB7DQogICAgICAgIHJldHVybiAnLSc7DQogICAgICB9DQogICAgICByZXR1cm4gTnVtYmVyKG51bSkudG9Mb2NhbGVTdHJpbmcoJ3poLUNOJywgew0KICAgICAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDIsDQogICAgICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogMg0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDmoLzlvI/ljJbnmb7liIbmr5QgKi8NCiAgICBmb3JtYXRQZXJjZW50YWdlKG51bSkgew0KICAgICAgaWYgKG51bSA9PT0gbnVsbCB8fCBudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09ICcnIHx8IG51bSA9PT0gMCkgew0KICAgICAgICByZXR1cm4gJy0nOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIChOdW1iZXIobnVtKSAqIDEwMCkudG9GaXhlZCgzKSArICclJzsNCiAgICB9LA0KDQogICAgLyoqIOagvOW8j+WMlui0p+W4gSAqLw0KICAgIGZvcm1hdEN1cnJlbmN5KG51bSkgew0KICAgICAgaWYgKG51bSA9PT0gbnVsbCB8fCBudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09ICcnIHx8IG51bSA9PT0gMCkgew0KICAgICAgICByZXR1cm4gJy0nOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIE51bWJlcihudW0pLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLA0KICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDINCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5qC85byP5YyW5Lqn6YePL+mUgOmHjyAqLw0KICAgIGZvcm1hdFByb2R1Y3Rpb24obnVtKSB7DQogICAgICBpZiAobnVtID09PSBudWxsIHx8IG51bSA9PT0gdW5kZWZpbmVkIHx8IG51bSA9PT0gJycgfHwgbnVtID09PSAwKSB7DQogICAgICAgIHJldHVybiAnLSc7DQogICAgICB9DQogICAgICByZXR1cm4gTnVtYmVyKG51bSkudG9GaXhlZCgzKTsNCiAgICB9LA0KDQogICAgLyoqIOaIkOacrOexu+Wei+WPmOWMluWkhOeQhiAqLw0KICAgIGhhbmRsZUNvc3RUeXBlQ2hhbmdlKHZhbHVlKSB7DQogICAgICAvLyDlpoLmnpzpgInmi6nkuoYi5YWo6YOoIu+8jOa4heepuuWFtuS7lumAiemhuQ0KICAgICAgaWYgKHZhbHVlLmluY2x1ZGVzKCcnKSkgew0KICAgICAgICBpZiAodmFsdWUubGVuZ3RoID4gMSkgew0KICAgICAgICAgIC8vIOWmguaenOWQjOaXtumAieaLqeS6hiLlhajpg6gi5ZKM5YW25LuW6YCJ6aG577yM5Y+q5L+d55WZIuWFqOmDqCINCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RUeXBlID0gWycnXTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ6YCJ5oupIuWFqOmDqCLvvIznoa7kv50i5YWo6YOoIuS4jeWcqOmAieaLqeWIl+ihqOS4rQ0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RUeXBlID0gdmFsdWUuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPT0gJycpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5Lqn6YeP5qCH562+ICovDQogICAgZ2V0UHJvZHVjdGlvbkxhYmVsKCkgew0KICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuY29zdENlbnRlciA9PT0gJ0pZWENUWkcnKSB7DQogICAgICAgIHJldHVybiAn6ZSA6YePJzsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAn5Lqn6YePJzsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOafpeivoui0qOmHj+aIkOacrOWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICAvLyDpqozor4Hlv4Xloavlj4LmlbANCiAgICAgIGlmICghdGhpcy5xdWVyeVBhcmFtcy55ZWFyTW9udGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nkvJrorqHmnJ8nKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KDQogICAgICAvLyDlpITnkIbml6XmnJ/moLzlvI/vvJrlsIYgMjAyNS0wNiDovazmjaLkuLogMjAyNTA2DQogICAgICBjb25zdCBmb3JtYXRZZWFyTW9udGggPSB0aGlzLnF1ZXJ5UGFyYW1zLnllYXJNb250aC5yZXBsYWNlKCctJywgJycpOw0KDQogICAgICAvLyDlpITnkIbmiJDmnKznsbvlnovlj4LmlbANCiAgICAgIGxldCB0eXBlQ29kZUxpc3QgPSBbXTsNCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RUeXBlICYmIHRoaXMucXVlcnlQYXJhbXMuY29zdFR5cGUubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyDlpoLmnpzpgInmi6nkuoYi5YWo6YOoIuaIluaVsOe7hOS4uuepuu+8jOS8oOmAkuepuuaVsOe7hA0KICAgICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5jb3N0VHlwZS5pbmNsdWRlcygnJykgfHwgdGhpcy5xdWVyeVBhcmFtcy5jb3N0VHlwZS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICB0eXBlQ29kZUxpc3QgPSBbXTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0eXBlQ29kZUxpc3QgPSB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RUeXBlOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHJlcXVlc3RQYXJhbXMgPSB7DQogICAgICAgIGNvc3RDZW50ZXI6IHRoaXMucXVlcnlQYXJhbXMuY29zdENlbnRlciB8fCAnJywNCiAgICAgICAgeWVhck1vbnRoOiBmb3JtYXRZZWFyTW9udGgsDQogICAgICAgIHR5cGVDb2RlTGlzdDogdHlwZUNvZGVMaXN0DQogICAgICB9Ow0KDQogICAgICBsaXN0UXVhbGl0eUNvc3RQYWdlKHJlcXVlc3RQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YSB8fCB7fTsNCg0KICAgICAgICAvLyDku47ov5Tlm57nmoRRdWFsaXR5Q29zdE1vbnRobHlWT+WvueixoeS4reiOt+WPluaVsOaNrg0KICAgICAgICBsZXQgcXVhbGl0eUNvc3REZXRhaWxMaXN0ID0gZGF0YS5xdWFsaXR5Q29zdERldGFpbExpc3QgfHwgW107DQoNCiAgICAgICAgLy8g57uf5LiA5L2/55SodHlwZUNvZGXkuLonWifnmoTmlbDmja7kvZzkuLrkuqfph48v6ZSA6YeP5L+h5oGvDQogICAgICAgIGNvbnN0IHByb2R1Y3Rpb25EYXRhID0gcXVhbGl0eUNvc3REZXRhaWxMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLnR5cGVDb2RlID09PSAnWicpOw0KICAgICAgICB0aGlzLnByb2R1Y3Rpb25JbmZvID0gcHJvZHVjdGlvbkRhdGEgfHwgbnVsbDsNCg0KDQoNCiAgICAgICAgLy8g5Li65q+P5LiA6KGM5re75Yqg5oiQ5pys57G75Yir5L+h5oGv77yM55So5LqO5YmN56uv6KGo5qC85pi+56S65ZKM5ZCI5bm2DQogICAgICAgIHF1YWxpdHlDb3N0RGV0YWlsTGlzdC5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgcm93LmNvc3RUeXBlID0gdGhpcy5nZXRDb3N0VHlwZShyb3cudHlwZUNvZGUpOw0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDmoLnmja7miJDmnKznsbvlnovpgInmi6nov4fmu6TmsYfmgLvooYwNCiAgICAgICAgaWYgKCF0aGlzLnNob3VsZFNob3dTdW1tYXJ5Um93cykgew0KICAgICAgICAgIC8vIOi/h+a7pOaOieWMheWQqyLlkIjorqEi44CBIuWwj+iuoSLjgIEi5rGH5oC7IueahOihjA0KICAgICAgICAgIHF1YWxpdHlDb3N0RGV0YWlsTGlzdCA9IHF1YWxpdHlDb3N0RGV0YWlsTGlzdC5maWx0ZXIocm93ID0+IHsNCiAgICAgICAgICAgIHJldHVybiAhKHJvdy50eXBlTmFtZSAmJiAoDQogICAgICAgICAgICAgIHJvdy50eXBlTmFtZS5pbmNsdWRlcygn5ZCI6K6hJykgfHwNCiAgICAgICAgICAgICAgcm93LnR5cGVOYW1lLmluY2x1ZGVzKCflsI/orqEnKSB8fA0KICAgICAgICAgICAgICByb3cudHlwZU5hbWUuaW5jbHVkZXMoJ+axh+aAuycpDQogICAgICAgICAgICApKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMucXVhbGl0eUNvc3RMaXN0ID0gcXVhbGl0eUNvc3REZXRhaWxMaXN0Ow0KDQogICAgICAgIC8vIOiuoeeul+WQiOW5tuWNleWFg+agvA0KICAgICAgICB0aGlzLmNhbGN1bGF0ZU1lcmdlQ2VsbHMoKTsNCg0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+afpeivouWksei0pScpOw0KICAgICAgICB0aGlzLnF1YWxpdHlDb3N0TGlzdCA9IFtdOw0KICAgICAgICB0aGlzLnByb2R1Y3Rpb25JbmZvID0gbnVsbDsNCiAgICAgICAgdGhpcy5tZXJnZUNlbGxzID0gW107DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCg0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgLy8g6YeN572u5Li66buY6K6k5YC8DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RDZW50ZXIgPSAnSllYQ1RaRyc7IC8vIOm7mOiupOmAieS4reWFtOa+hOeJuemSouaxh+aAuw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy55ZWFyTW9udGggPSB0aGlzLmdldERlZmF1bHRZZWFyTW9udGgoKTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY29zdFR5cGUgPSBbJyddOw0KICAgICAgLy8g5riF56m65pWw5o2uDQogICAgICB0aGlzLnF1YWxpdHlDb3N0TGlzdCA9IFtdOw0KICAgICAgdGhpcy5wcm9kdWN0aW9uSW5mbyA9IG51bGw7DQogICAgICB0aGlzLm1lcmdlQ2VsbHMgPSBbXTsNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlum7mOiupOS8muiuoeacn++8iOS4iuS4quaciO+8iSAqLw0KICAgIGdldERlZmF1bHRZZWFyTW9udGgoKSB7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgY29uc3QgeWVhciA9IG5vdy5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW9udGggPSBub3cuZ2V0TW9udGgoKSArIDE7IC8vIDEtMTINCiAgICAgIGNvbnN0IGRheSA9IG5vdy5nZXREYXRlKCk7DQogICAgICBjb25zdCBob3VyID0gbm93LmdldEhvdXJzKCk7DQoNCiAgICAgIC8vIOWmguaenOS7iuWkqeaYr+acrOaciDI15Y+3OOeCueWJje+8iOWQqzI15Y+3Nzo1Oe+8ie+8jOWImeeUqOS4iuS4quaciA0KICAgICAgaWYgKGRheSA8IDI4IHx8IChkYXkgPT09IDI4ICYmIGhvdXIgPCAxKSkgew0KICAgICAgICAvLyDlpITnkIYx5pyI5pe255qE6Leo5bm0DQogICAgICAgIGNvbnN0IHByZXZNb250aCA9IG1vbnRoID09PSAxID8gMTIgOiBtb250aCAtIDE7DQogICAgICAgIGNvbnN0IHByZXZZZWFyID0gbW9udGggPT09IDEgPyB5ZWFyIC0gMSA6IHllYXI7DQogICAgICAgIHJldHVybiBgJHtwcmV2WWVhcn0tJHtTdHJpbmcocHJldk1vbnRoKS5wYWRTdGFydCgyLCAnMCcpfWA7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gYCR7eWVhcn0tJHtTdHJpbmcobW9udGgpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWkhOeQhueql+WPo+Wkp+Wwj+WPmOWMliAqLw0KICAgIGhhbmRsZVJlc2l6ZSgpIHsNCiAgICAgIC8vIOW8uuWItumHjeaWsOiuoeeul+ihqOagvOmrmOW6pg0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICB9LA0KDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIC8vIOajgOafpeaYr+WQpuacieaVsOaNrg0KICAgICAgaWYgKCF0aGlzLnF1YWxpdHlDb3N0TGlzdCB8fCB0aGlzLnF1YWxpdHlDb3N0TGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmmoLml6DmlbDmja7lj6/lr7zlh7rvvIzor7flhYjmn6Xor6LmlbDmja4nKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDmnoTlu7rmn6Xor6Llj4LmlbDvvIzkuI7mn6Xor6LmlbDmja7ml7bkv53mjIHkuIDoh7TnmoTmoLzlvI8NCiAgICAgIGxldCBxdWVyeVBhcmFtcyA9IHsNCiAgICAgICAgY29zdENlbnRlcjogdGhpcy5xdWVyeVBhcmFtcy5jb3N0Q2VudGVyLA0KICAgICAgICB5ZWFyTW9udGg6IHRoaXMucXVlcnlQYXJhbXMueWVhck1vbnRoLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgdHlwZUNvZGVMaXN0OiB0aGlzLnF1ZXJ5UGFyYW1zLmNvc3RUeXBlLA0KICAgICAgICBjb3N0VG9uOiB0aGlzLmZvcm1hdFByb2R1Y3Rpb24odGhpcy5wcm9kdWN0aW9uSW5mby5jb3N0VG9uKQ0KICAgICAgfTsNCg0KDQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rlhbTmvoTnibnpkqLotKjph4/miJDmnKzooajmlbDmja4/JywgIuitpuWRiiIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgcmV0dXJuIGV4cG9ydFF1YWxpdHlDb3N0UGFnZShxdWVyeVBhcmFtcyk7DQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9DQoNCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/qualityCostPage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\" style=\"height: auto !important; min-height: auto !important; max-height: none !important;\">\r\n    <!-- 顶部筛选区域 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"成本中心\" prop=\"costCenter\">\r\n        <el-select v-model=\"queryParams.costCenter\" placeholder=\"请选择成本中心\" clearable size=\"small\" style=\"width: 200px;\">\r\n          <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"会计期\" prop=\"yearMonth\">\r\n        <el-date-picker v-model=\"queryParams.yearMonth\" type=\"month\" placeholder=\"请选择年月\" format=\"yyyy-MM\"\r\n          value-format=\"yyyy-MM\" size=\"small\" style=\"width: 200px;\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"成本类型\" prop=\"costType\">\r\n        <el-select v-model=\"queryParams.costType\" placeholder=\"请选择成本类型\" multiple clearable size=\"small\"\r\n          style=\"width: 300px;\" @change=\"handleCostTypeChange\">\r\n          <el-option v-for=\"item in costTypeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\">导出</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 质量成本表格 -->\r\n    <div v-if=\"qualityCostList.length > 0\"\r\n      style=\"width: 100%; height: 80vh; overflow: visible; margin: 0; padding: 0; display: flex; flex-direction: column; align-items: center;\">\r\n      <!-- 标题和产量信息 -->\r\n      <div style=\"position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;\">\r\n        <h3 style=\"text-align: center; margin: 0;\">兴澄特钢质量成本表</h3>\r\n        <div style=\"position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;\">\r\n          <span style=\"margin-right: 20px;\">单位：元</span>\r\n          {{ getProductionLabel() }}：{{ productionInfo ? formatProduction(productionInfo.costTon) + '吨' : '无' }}\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"overflow-x: auto; width: 95%; max-height: calc(100% - 80px); margin: 0; padding: 0; border: none;\">\r\n        <vxe-table\r\n          :loading=\"loading\"\r\n          :data=\"qualityCostList\"\r\n          border\r\n          :height=\"tableHeight\"\r\n          :class=\"{ 'no-scroll': !needScrollbar }\"\r\n          style=\"table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;\"\r\n          :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#303133' }\"\r\n          :merge-cells=\"mergeCells\"\r\n          stripe\r\n          :auto-resize=\"false\"\r\n          :scroll-y=\"{enabled: needScrollbar}\"\r\n          :scroll-x=\"{enabled: true}\">\r\n          <vxe-column title=\"成本类别\" align=\"center\" field=\"costType\" width=\"5%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                {{ row.costType }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"科目\" field=\"typeName\" width=\"15%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{\r\n                  fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal',\r\n                  textAlign: (row.typeName && row.typeName.includes('小计')) ? 'center' : 'left',\r\n                  display: 'block'\r\n                }\">\r\n                {{ row.typeName }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <!-- 质量成本分组 -->\r\n          <vxe-colgroup title=\"质量成本（列入项）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"costEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.costEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"costPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.costPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 不列入项分组 -->\r\n          <vxe-colgroup title=\"质量成本（不列入项）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"nincEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.nincEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"nincPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.nincPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 质量成本（含不列入项）分组 -->\r\n          <vxe-colgroup title=\"质量成本（总）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"allcEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.allcEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"allcPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.allcPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 金额百分比列 -->\r\n          <vxe-column title=\"金额百分比\" align=\"center\" field=\"amountPercent\" width=\"8%\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                {{ formatPercentage(row.amountPercent) }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据提示 -->\r\n    <div v-else-if=\"!loading\" style=\"text-align: center; padding: 50px;\">\r\n      <el-empty description=\"请选择查询条件后点击搜索查看数据\"></el-empty>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listQualityCostPage, costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { exportQualityCostPage } from \"@/api/qualityCost/qualityCostPage\";\r\n\r\nexport default {\r\n  name: \"QualityCost\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 质量成本数据列表\r\n      qualityCostList: [],\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      // 产量信息\r\n      productionInfo: null,\r\n      // 合并单元格配置\r\n      mergeCells: [],\r\n      // 成本类型选项\r\n      costTypeOptions: [\r\n        { value: '', label: '全部' },\r\n        { value: 'A', label: 'A-预防成本' },\r\n        { value: 'B', label: 'B-鉴定成本' },\r\n        { value: 'C', label: 'C-内部损失成本' },\r\n        { value: 'D', label: 'D-外部损失成本' }\r\n      ],\r\n      // 查询参数\r\n      queryParams: {\r\n        costCenter: 'JYXCTZG', // 默认选中兴澄特钢汇总\r\n        yearMonth: null,\r\n        costType: ['']\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    /** 判断是否显示汇总行 */\r\n    shouldShowSummaryRows() {\r\n      // 当成本类型选择为\"全部\"时显示汇总行\r\n      return this.queryParams.costType.includes('') || this.queryParams.costType.length === 0;\r\n    },\r\n    /** 计算表格高度 */\r\n    tableHeight() {\r\n      if (!this.qualityCostList || this.qualityCostList.length === 0) {\r\n        return 'auto';\r\n      }\r\n\r\n      // 当成本类型选择非全部时，使用auto高度，不限制最大高度\r\n      if (!this.shouldShowSummaryRows) {\r\n        return 'auto';\r\n      }\r\n\r\n      // 计算所需高度：表头高度 + 数据行高度\r\n      const headerHeight = 40; // 表头高度\r\n      const rowHeight = 32; // 每行数据高度\r\n      const padding = 10; // 额外间距\r\n\r\n      const dataRowsCount = this.qualityCostList.length;\r\n      const calculatedHeight = headerHeight + (dataRowsCount * rowHeight) + padding;\r\n\r\n      // 最大高度限制（80vh - 80px）\r\n      const maxHeight = window.innerHeight * 0.8 - 80;\r\n\r\n      // 返回计算高度和最大高度中的较小值\r\n      return Math.min(calculatedHeight, maxHeight) + 'px';\r\n    },\r\n    /** 判断是否需要显示滚动条 */\r\n    needScrollbar() {\r\n      // 当成本类型选择非全部时，不显示滚动条\r\n      if (!this.shouldShowSummaryRows) {\r\n        return false;\r\n      }\r\n\r\n      if (!this.qualityCostList || this.qualityCostList.length === 0) {\r\n        return false;\r\n      }\r\n\r\n      const headerHeight = 40;\r\n      const rowHeight = 32;\r\n      const padding = 10;\r\n      const dataRowsCount = this.qualityCostList.length;\r\n      const calculatedHeight = headerHeight + (dataRowsCount * rowHeight) + padding;\r\n      const maxHeight = window.innerHeight * 0.8 - 80;\r\n\r\n      return calculatedHeight > maxHeight;\r\n    }\r\n  },\r\n  created() {\r\n    this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n    this.getCostCenterList();\r\n    // 页面加载时自动触发搜索\r\n    this.$nextTick(() => {\r\n      this.handleQuery();\r\n    });\r\n  },\r\n  mounted() {\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize);\r\n  },\r\n  beforeDestroy() {\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.handleResize);\r\n  },\r\n  methods: {\r\n    /** 获取成本中心列表 */\r\n    getCostCenterList() {\r\n      costCenterlist().then(response => {\r\n        const options = response.data || [];\r\n        // 将JYXCTZG选项排在第一位\r\n        const jyxctzgOption = options.find(item => item.key === 'JYXCTZG');\r\n        const otherOptions = options.filter(item => item.key !== 'JYXCTZG');\r\n\r\n        if (jyxctzgOption) {\r\n          this.costCenterOptions = [jyxctzgOption, ...otherOptions];\r\n        } else {\r\n          this.costCenterOptions = options;\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取成本中心列表失败');\r\n      });\r\n    },\r\n\r\n    /** 获取成本类型 */\r\n    getCostType(typeCode) {\r\n      if (!typeCode) return '';\r\n      const firstChar = typeCode.charAt(0).toUpperCase();\r\n      switch (firstChar) {\r\n        case 'A': return '预防成本';\r\n        case 'B': return '鉴定成本';\r\n        case 'C': return '内部损失成本';\r\n        case 'D': return '外部损失成本';\r\n        default: return '汇总';\r\n      }\r\n    },\r\n\r\n    /** 计算合并单元格 */\r\n    calculateMergeCells() {\r\n      const mergeCells = [];\r\n      const costTypeGroups = {};\r\n\r\n      // 按成本类型分组\r\n      this.qualityCostList.forEach((row, index) => {\r\n        const costType = row.costType;\r\n        if (!costTypeGroups[costType]) {\r\n          costTypeGroups[costType] = [];\r\n        }\r\n        costTypeGroups[costType].push(index);\r\n      });\r\n\r\n      // 生成合并配置\r\n      Object.values(costTypeGroups).forEach(group => {\r\n        if (group.length > 1) {\r\n          mergeCells.push({\r\n            row: group[0],\r\n            col: 0,\r\n            rowspan: group.length,\r\n            colspan: 1\r\n          });\r\n        }\r\n      });\r\n\r\n      this.mergeCells = mergeCells;\r\n    },\r\n\r\n    /** 格式化数字 */\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化百分比 */\r\n    formatPercentage(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return (Number(num) * 100).toFixed(3) + '%';\r\n    },\r\n\r\n    /** 格式化货币 */\r\n    formatCurrency(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化产量/销量 */\r\n    formatProduction(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toFixed(3);\r\n    },\r\n\r\n    /** 成本类型变化处理 */\r\n    handleCostTypeChange(value) {\r\n      // 如果选择了\"全部\"，清空其他选项\r\n      if (value.includes('')) {\r\n        if (value.length > 1) {\r\n          // 如果同时选择了\"全部\"和其他选项，只保留\"全部\"\r\n          this.queryParams.costType = [''];\r\n        }\r\n      } else {\r\n        // 如果没有选择\"全部\"，确保\"全部\"不在选择列表中\r\n        this.queryParams.costType = value.filter(item => item !== '');\r\n      }\r\n    },\r\n\r\n    /** 获取产量标签 */\r\n    getProductionLabel() {\r\n      if (this.queryParams.costCenter === 'JYXCTZG') {\r\n        return '销量';\r\n      } else {\r\n        return '产量';\r\n      }\r\n    },\r\n\r\n    /** 查询质量成本列表 */\r\n    getList() {\r\n      // 验证必填参数\r\n      if (!this.queryParams.yearMonth) {\r\n        this.$message.warning('请选择会计期');\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n\r\n      // 处理日期格式：将 2025-06 转换为 202506\r\n      const formatYearMonth = this.queryParams.yearMonth.replace('-', '');\r\n\r\n      // 处理成本类型参数\r\n      let typeCodeList = [];\r\n      if (this.queryParams.costType && this.queryParams.costType.length > 0) {\r\n        // 如果选择了\"全部\"或数组为空，传递空数组\r\n        if (this.queryParams.costType.includes('') || this.queryParams.costType.length === 0) {\r\n          typeCodeList = [];\r\n        } else {\r\n          typeCodeList = this.queryParams.costType;\r\n        }\r\n      }\r\n\r\n      const requestParams = {\r\n        costCenter: this.queryParams.costCenter || '',\r\n        yearMonth: formatYearMonth,\r\n        typeCodeList: typeCodeList\r\n      };\r\n\r\n      listQualityCostPage(requestParams).then(response => {\r\n        const data = response.data || {};\r\n\r\n        // 从返回的QualityCostMonthlyVO对象中获取数据\r\n        let qualityCostDetailList = data.qualityCostDetailList || [];\r\n\r\n        // 统一使用typeCode为'Z'的数据作为产量/销量信息\r\n        const productionData = qualityCostDetailList.find(item => item.typeCode === 'Z');\r\n        this.productionInfo = productionData || null;\r\n\r\n\r\n\r\n        // 为每一行添加成本类别信息，用于前端表格显示和合并\r\n        qualityCostDetailList.forEach(row => {\r\n          row.costType = this.getCostType(row.typeCode);\r\n        });\r\n\r\n        // 根据成本类型选择过滤汇总行\r\n        if (!this.shouldShowSummaryRows) {\r\n          // 过滤掉包含\"合计\"、\"小计\"、\"汇总\"的行\r\n          qualityCostDetailList = qualityCostDetailList.filter(row => {\r\n            return !(row.typeName && (\r\n              row.typeName.includes('合计') ||\r\n              row.typeName.includes('小计') ||\r\n              row.typeName.includes('汇总')\r\n            ));\r\n          });\r\n        }\r\n\r\n        this.qualityCostList = qualityCostDetailList;\r\n\r\n        // 计算合并单元格\r\n        this.calculateMergeCells();\r\n\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n        this.$message.error('查询失败');\r\n        this.qualityCostList = [];\r\n        this.productionInfo = null;\r\n        this.mergeCells = [];\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      // 重置为默认值\r\n      this.queryParams.costCenter = 'JYXCTZG'; // 默认选中兴澄特钢汇总\r\n      this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n      this.queryParams.costType = [''];\r\n      // 清空数据\r\n      this.qualityCostList = [];\r\n      this.productionInfo = null;\r\n      this.mergeCells = [];\r\n    },\r\n\r\n    /** 获取默认会计期（上个月） */\r\n    getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    /** 处理窗口大小变化 */\r\n    handleResize() {\r\n      // 强制重新计算表格高度\r\n      this.$forceUpdate();\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 检查是否有数据\r\n      if (!this.qualityCostList || this.qualityCostList.length === 0) {\r\n        this.$message.warning('暂无数据可导出，请先查询数据');\r\n        return;\r\n      }\r\n\r\n      // 构建查询参数，与查询数据时保持一致的格式\r\n      let queryParams = {\r\n        costCenter: this.queryParams.costCenter,\r\n        yearMonth: this.queryParams.yearMonth.replace('-', ''), // 将 2025-06 转换为 202506\r\n        typeCodeList: this.queryParams.costType,\r\n        costTon: this.formatProduction(this.productionInfo.costTon)\r\n      };\r\n\r\n\r\n      this.$confirm('是否确认导出兴澄特钢质量成本表数据?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.loading = true;\r\n        return exportQualityCostPage(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    }\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 重置所有可能的高度约束 */\r\n*,\r\n*::before,\r\n*::after {\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 容器样式 */\r\n.app-container {\r\n  overflow: visible !important;\r\n  height: auto !important;\r\n  min-height: auto !important;\r\n  max-height: none !important;\r\n  padding: 20px !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.vxe-table ::v-deep .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-footer--column {\r\n  background-color: #fafafa;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 筛选区域样式 */\r\n.el-form--inline .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.vxe-table .vxe-cell {\r\n  padding: 0 5px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 主要表格样式 */\r\n.vxe-table {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 固定列样式 - 确保固定列正常显示 */\r\n.vxe-table ::v-deep .vxe-table--fixed-left-wrapper {\r\n  z-index: 10;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--fixed-right-wrapper {\r\n  z-index: 10;\r\n}\r\n\r\n/* 汇总表格合计行样式 */\r\n.vxe-table ::v-deep .summary-row {\r\n  background-color: #f8f9fa;\r\n  font-weight: bold;\r\n}\r\n\r\n.vxe-table ::v-deep .summary-row .vxe-cell {\r\n  background-color: #f8f9fa !important;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 自定义滚动条样式 - 浅蓝色加粗 */\r\n/* vxe-table 内部滚动条样式 */\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n  z-index: 999 !important;\r\n}\r\n\r\n/* 当不需要滚动时隐藏所有滚动条 */\r\n.vxe-table.no-scroll ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.vxe-table.no-scroll ::v-deep .vxe-body--wrapper::-webkit-scrollbar,\r\n.vxe-table.no-scroll ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar {\r\n  width: 0 !important;\r\n  height: 0 !important;\r\n  display: none !important;\r\n}\r\n\r\n/* 当不需要滚动时，确保表格内容完全显示 */\r\n.vxe-table.no-scroll ::v-deep .vxe-table--body-wrapper,\r\n.vxe-table.no-scroll ::v-deep .vxe-body--wrapper,\r\n.vxe-table.no-scroll ::v-deep .vxe-table--main-wrapper {\r\n  overflow: visible !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-track,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-track {\r\n  background: #f0f8ff !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-thumb,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-thumb {\r\n  background: #87ceeb !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f0f8ff !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-thumb:hover,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #4682b4 !important;\r\n}\r\n\r\n/* 表格容器滚动条样式 */\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n  z-index: 999 !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-track {\r\n  background: #f0f8ff !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-thumb {\r\n  background: #87ceeb !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f0f8ff !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-thumb:hover {\r\n  background: #4682b4 !important;\r\n}\r\n\r\n/* 通用滚动条样式 - 更高优先级 */\r\n.vxe-table ::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n  z-index: 1000 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-track {\r\n  background: #f0f8ff !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #87ceeb !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f0f8ff !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #4682b4 !important;\r\n}\r\n\r\n/* 滚动条容器层级调整 */\r\n.vxe-table ::v-deep .vxe-table--body-wrapper,\r\n.vxe-table ::v-deep .vxe-body--wrapper,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper {\r\n  position: relative !important;\r\n  z-index: 1 !important;\r\n}\r\n</style>\r\n"]}]}