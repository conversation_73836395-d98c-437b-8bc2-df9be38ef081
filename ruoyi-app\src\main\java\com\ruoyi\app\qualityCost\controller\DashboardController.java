package com.ruoyi.app.qualityCost.controller;

import com.ruoyi.app.qualityCost.domain.*;
import com.ruoyi.app.qualityCost.service.IDashboardService;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/qualityCost/qualityCostDashboard")
public class DashboardController {

    @Autowired
    private IDashboardService dashboardService;

    @GetMapping("/getPieChartData")
    public AjaxResult getPieChartData(QualityCostDetail qualityCostDetail)
    {
        PieChart pieChart = dashboardService.getPieChartData(qualityCostDetail);
        return AjaxResult.success(pieChart);
    }

    @GetMapping("/getMultiLineChartData")
    public AjaxResult getMultiLineChartData(QualityCostDetail qualityCostDetail)
    {
        MultiLineChart multiLineChart = dashboardService.getMultiLineChartData(qualityCostDetail);
        return AjaxResult.success(multiLineChart);
    }

    @GetMapping("/getExternalCostDetail")
    public AjaxResult getExternalCostDetail(QualityCostDetail qualityCostDetail)
    {
        ExternalCostDetailChart externalCostDetailChart = dashboardService.getExternalCostDetail(qualityCostDetail);
        return AjaxResult.success(externalCostDetailChart);
    }

    @GetMapping("/getInternalCostDetail")
    public AjaxResult getInternalCostDetail(QualityCostDetail qualityCostDetail)
    {
        InternalCostDetailChart internalCostDetail= dashboardService.getInternalCostDetail(qualityCostDetail);
        return AjaxResult.success(internalCostDetail);
    }

    @GetMapping("/getComboChartDetail")
    public AjaxResult getComboChartDetail(QualityCostDetail qualityCostDetail)
    {
        ComboChart comboChart= dashboardService.getComboChartDetail(qualityCostDetail);
        return AjaxResult.success(comboChart);
    }

    @GetMapping("/getWaterfallChartDetail")
    public AjaxResult getWaterfallChartDetail(QualityCostDetail qualityCostDetail)
    {
        WaterfallChart waterfallChart= dashboardService.getWaterfallChartDetail(qualityCostDetail);
        return AjaxResult.success(waterfallChart);
    }

    @GetMapping("/getScrapLossChartDetailsDetail")
    public AjaxResult getScrapLossChartDetailsDetail(QualityCostDetail qualityCostDetail)
    {
        ScrapLossChart scrapLossChart= dashboardService.getScrapLossChartDetailsDetail(qualityCostDetail);
        return AjaxResult.success(scrapLossChart);
    }

    @GetMapping("/getQualityObjectionLossDetail")
    public AjaxResult getQualityObjectionLossDetail(QualityCostDetail qualityCostDetail)
    {
        QualityObjectionLossChart qualityObjectionLossChart= dashboardService.getQualityObjectionLossDetail(qualityCostDetail);
        return AjaxResult.success(qualityObjectionLossChart);
    }


    @GetMapping("/getQualityCostDetail")
    public AjaxResult getQualityCostDetail(QualityCostDetail qualityCostDetail)
    {
        //currentYearMonth
        QualityCostDetail currentDetail = dashboardService.getQualityCostDetail(qualityCostDetail);

        // 提取年份和月份
        int year = Integer.parseInt(qualityCostDetail.getYearMonth().substring(0, 4));
        int month = Integer.parseInt(qualityCostDetail.getYearMonth().substring(4, 6));

        // 验证月份有效性（01-12）
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("月份部分必须在01-12之间");
        }

        // 计算上一个月
        month--;
        if (month == 0) {
            month = 12;
            year--;
        }

        // 格式化结果（月份补零）
        String previousYearMonth = String.format("%d%02d", year, month);

        //previousYearMonth
        qualityCostDetail.setYearMonth(previousYearMonth);
        QualityCostDetail previousDetail = dashboardService.getQualityCostDetail(qualityCostDetail);

        String costTonUpPercent = null;
        String costExPercent = null;
        String costPerExPercent = null;

        if (qualityCostDetail.getContainType() == 1) {
            //计算增长率
            if (currentDetail != null && previousDetail != null && currentDetail.getCostTon() != null && currentDetail.getCostTon().doubleValue() != 0 && previousDetail.getCostTon() != null && previousDetail.getCostTon().doubleValue() != 0) {
                double growthRate = (currentDetail.getCostTon().doubleValue() - previousDetail.getCostTon().doubleValue()) / previousDetail.getCostTon().doubleValue() * 100;
                costTonUpPercent = String.format("%.2f%%", growthRate);
            }
            if (currentDetail != null && previousDetail != null && currentDetail.getCostEx() != null && currentDetail.getCostEx().doubleValue() != 0 && previousDetail.getCostEx() != null && previousDetail.getCostEx().doubleValue() != 0) {
                double growthRate = (currentDetail.getCostEx().doubleValue() - previousDetail.getCostEx().doubleValue()) / previousDetail.getCostEx().doubleValue() * 100;
                costExPercent = String.format("%.2f%%", growthRate);
            }
            if (currentDetail != null && previousDetail != null && currentDetail.getCostPerEx() != null && currentDetail.getCostPerEx().doubleValue() != 0 && previousDetail.getCostPerEx() != null && previousDetail.getCostTon().doubleValue() != 0) {
                double growthRate = (currentDetail.getCostPerEx().doubleValue() - previousDetail.getCostPerEx().doubleValue()) / previousDetail.getCostPerEx().doubleValue() * 100;
                costPerExPercent = String.format("%.2f%%", growthRate);
            }
        } else {
            //计算增长率
            if (currentDetail != null && previousDetail != null && currentDetail.getAllcTon() != null && currentDetail.getAllcTon().doubleValue() != 0 && previousDetail.getAllcTon() != null && previousDetail.getAllcTon().doubleValue() != 0) {
                double growthRate = (currentDetail.getAllcTon().doubleValue() - previousDetail.getAllcTon().doubleValue()) / previousDetail.getAllcTon().doubleValue() * 100;
                costTonUpPercent = String.format("%.2f%%", growthRate);
            }
            if (currentDetail != null && previousDetail != null && currentDetail.getAllcEx() != null && currentDetail.getAllcEx().doubleValue() != 0 && previousDetail.getAllcEx() != null && previousDetail.getAllcEx().doubleValue() != 0) {
                double growthRate = (currentDetail.getAllcEx().doubleValue() - previousDetail.getAllcEx().doubleValue()) / previousDetail.getAllcEx().doubleValue() * 100;
                costExPercent = String.format("%.2f%%", growthRate);
            }
            if (currentDetail != null && previousDetail != null && currentDetail.getAllcPerEx() != null && currentDetail.getAllcPerEx().doubleValue() != 0 && previousDetail.getAllcPerEx() != null && previousDetail.getAllcPerEx().doubleValue() != 0) {
                double growthRate = (currentDetail.getAllcPerEx().doubleValue() - previousDetail.getAllcPerEx().doubleValue()) / previousDetail.getAllcPerEx().doubleValue() * 100;
                costPerExPercent = String.format("%.2f%%", growthRate);
            }
        }


        QualityCostDetailVo qualityCostDetailVo = new QualityCostDetailVo();
        qualityCostDetailVo.setQualityCostData(currentDetail);
        qualityCostDetailVo.setCostTonUpPercent(costTonUpPercent);
        qualityCostDetailVo.setCostExPercent(costExPercent);
        qualityCostDetailVo.setCostPerExPercent(costPerExPercent);
        return AjaxResult.success(qualityCostDetailVo);
    }
}
