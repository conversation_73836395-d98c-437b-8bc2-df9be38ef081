package com.ruoyi.app.qualityCost.domain;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 兴澄特钢质量成本总对象 TACQM01
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
public class QualityCostDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录创建责任者 */
    @Excel(name = "记录创建责任者")
    private String recCreator;

    /** 记录创建时刻 */
    @Excel(name = "记录创建时刻")
    private String recCreateTime;

    /** 记录修改责任者 */
    @Excel(name = "记录修改责任者")
    private String recRevisor;

    /** 记录修改时刻 */
    @Excel(name = "记录修改时刻")
    private String recReviseTime;

    /** 归档标记 */
    @Excel(name = "归档标记")
    private String archiveFlag;

    /** 成本中心代码 */
    @Excel(name = "成本中心代码")
    private String costCenter;

    /** 成本中心名称 */
    private String costCenterCname;

    /** 会计期 */
    private String yearMonth;

    /** 产线名称（中文） */
    @Excel(name = "产线名称", readConverterExp = "中=文")
    private String factory;

    /** 科目名称 */
    private String typeName;

    /** 科目代码 */
    @Excel(name = "科目代码")
    private String typeCode;

    /** 质量成本-吨位（吨） */
    @Excel(name = "质量成本-吨位", readConverterExp = "吨=")
    private BigDecimal costTon;

    /** 质量成本-单价（元/吨） */
    @Excel(name = "质量成本-单价", readConverterExp = "元=/吨")
    private BigDecimal costPerTon;

    /** 质量成本-金额（元） */
    @Excel(name = "质量成本-金额", readConverterExp = "元=")
    private BigDecimal costEx;

    /** 质量成本-吨钢金额（元） */
    @Excel(name = "质量成本-吨钢金额", readConverterExp = "元=")
    private BigDecimal costPerEx;

    /** 不列入项-吨位（吨） */
    @Excel(name = "不列入项-吨位", readConverterExp = "吨=")
    private BigDecimal nincTon;

    /** 不列入项-单价（元/吨） */
    @Excel(name = "不列入项-单价", readConverterExp = "元=/吨")
    private BigDecimal nincPerTon;

    /** 不列入项-金额（元） */
    @Excel(name = "不列入项-金额", readConverterExp = "元=")
    private BigDecimal nincEx;

    /** 不列入项-吨钢金额（元） */
    @Excel(name = "不列入项-吨钢金额", readConverterExp = "元=")
    private BigDecimal nincPerEx;

    /** 质量成本（含不列入项）-吨位（吨） */
    @Excel(name = "质量成本", readConverterExp = "含=不列入项")
    private BigDecimal allcTon;

    /** 质量成本（含不列入项）-单价（元/吨） */
    @Excel(name = "质量成本", readConverterExp = "含=不列入项")
    private BigDecimal allcPerTon;

    /** 质量成本（含不列入项）-金额（元） */
    @Excel(name = "质量成本", readConverterExp = "含=不列入项")
    private BigDecimal allcEx;

    /** 质量成本（含不列入项）-吨钢金额（元） */
    @Excel(name = "质量成本", readConverterExp = "含=不列入项")
    private BigDecimal allcPerEx;

    /**金额百分比 */
    @Excel(name = "金额百分比")
    private BigDecimal amountPercent;

    /**是否合计 */
    @Excel(name = "是否合计")
    private String isTotal;

    /** 备用字段1 */
    @Excel(name = "备用字段1")
    private String filed1;

    /** 备用字段2 */
    @Excel(name = "备用字段2")
    private String filed2;

    /** 备用字段3 */
    @Excel(name = "备用字段3")
    private String filed3;

    /** 备用字段4 */
    @Excel(name = "备用字段4")
    private String filed4;

    /** 备用字段5 */
    @Excel(name = "备用字段5")
    private String filed5;

    private List<String> typeCodeList;

    private Integer containType;

    public List<String> getTypeCodeList() {
        return typeCodeList;
    }

    public void setTypeCodeList(List<String> typeCodeList) {
        this.typeCodeList = typeCodeList;
    }

    public void setRecCreator(String recCreator)
    {
        this.recCreator = recCreator;
    }

    public String getRecCreator() 
    {
        return recCreator;
    }
    public void setRecCreateTime(String recCreateTime) 
    {
        this.recCreateTime = recCreateTime;
    }

    public String getRecCreateTime() 
    {
        return recCreateTime;
    }
    public void setRecRevisor(String recRevisor) 
    {
        this.recRevisor = recRevisor;
    }

    public String getRecRevisor() 
    {
        return recRevisor;
    }
    public void setRecReviseTime(String recReviseTime) 
    {
        this.recReviseTime = recReviseTime;
    }

    public String getRecReviseTime() 
    {
        return recReviseTime;
    }
    public void setArchiveFlag(String archiveFlag) 
    {
        this.archiveFlag = archiveFlag;
    }

    public String getArchiveFlag() 
    {
        return archiveFlag;
    }
    public void setCostCenter(String costCenter) 
    {
        this.costCenter = costCenter;
    }

    public String getCostCenter() 
    {
        return costCenter;
    }
    public void setCostCenterCname(String costCenterCname) 
    {
        this.costCenterCname = costCenterCname;
    }

    public String getCostCenterCname() 
    {
        return costCenterCname;
    }
    public void setYearMonth(String yearMonth) 
    {
        this.yearMonth = yearMonth;
    }

    public String getYearMonth() 
    {
        return yearMonth;
    }
    public void setFactory(String factory) 
    {
        this.factory = factory;
    }

    public String getFactory() 
    {
        return factory;
    }
    public void setTypeName(String typeName) 
    {
        this.typeName = typeName;
    }

    public String getTypeName() 
    {
        return typeName;
    }
    public void setTypeCode(String typeCode) 
    {
        this.typeCode = typeCode;
    }

    public String getTypeCode() 
    {
        return typeCode;
    }
    public void setCostTon(BigDecimal costTon) 
    {
        this.costTon = costTon;
    }

    public BigDecimal getCostTon() 
    {
        return costTon;
    }
    public void setCostPerTon(BigDecimal costPerTon) 
    {
        this.costPerTon = costPerTon;
    }

    public BigDecimal getCostPerTon() 
    {
        return costPerTon;
    }
    public void setCostEx(BigDecimal costEx) 
    {
        this.costEx = costEx;
    }

    public BigDecimal getCostEx() 
    {
        return costEx;
    }
    public void setCostPerEx(BigDecimal costPerEx) 
    {
        this.costPerEx = costPerEx;
    }

    public BigDecimal getCostPerEx() 
    {
        return costPerEx;
    }
    public void setNincTon(BigDecimal nincTon) 
    {
        this.nincTon = nincTon;
    }

    public BigDecimal getNincTon() 
    {
        return nincTon;
    }
    public void setNincPerTon(BigDecimal nincPerTon) 
    {
        this.nincPerTon = nincPerTon;
    }

    public BigDecimal getNincPerTon() 
    {
        return nincPerTon;
    }
    public void setNincEx(BigDecimal nincEx) 
    {
        this.nincEx = nincEx;
    }

    public BigDecimal getNincEx() 
    {
        return nincEx;
    }
    public void setNincPerEx(BigDecimal nincPerEx) 
    {
        this.nincPerEx = nincPerEx;
    }

    public BigDecimal getNincPerEx() 
    {
        return nincPerEx;
    }
    public void setAllcTon(BigDecimal allcTon) 
    {
        this.allcTon = allcTon;
    }

    public BigDecimal getAllcTon() 
    {
        return allcTon;
    }
    public void setAllcPerTon(BigDecimal allcPerTon) 
    {
        this.allcPerTon = allcPerTon;
    }

    public BigDecimal getAllcPerTon() 
    {
        return allcPerTon;
    }
    public void setAllcEx(BigDecimal allcEx) 
    {
        this.allcEx = allcEx;
    }

    public BigDecimal getAllcEx() 
    {
        return allcEx;
    }
    public void setAllcPerEx(BigDecimal allcPerEx) 
    {
        this.allcPerEx = allcPerEx;
    }

    public BigDecimal getAllcPerEx() 
    {
        return allcPerEx;
    }
    public void setFiled1(String filed1) 
    {
        this.filed1 = filed1;
    }

    public String getFiled1() 
    {
        return filed1;
    }
    public void setFiled2(String filed2) 
    {
        this.filed2 = filed2;
    }

    public String getFiled2() 
    {
        return filed2;
    }
    public void setFiled3(String filed3) 
    {
        this.filed3 = filed3;
    }

    public String getFiled3() 
    {
        return filed3;
    }
    public void setFiled4(String filed4) 
    {
        this.filed4 = filed4;
    }

    public String getFiled4() 
    {
        return filed4;
    }
    public void setFiled5(String filed5) 
    {
        this.filed5 = filed5;
    }

    public String getFiled5() 
    {
        return filed5;
    }
    public void setAmountPercent(BigDecimal amountPercent) 
    {
        this.amountPercent = amountPercent;
    }
    public BigDecimal getAmountPercent() 
    {
        return amountPercent;
    }
    public void setIsTotal(String isTotal) 
    {
        this.isTotal = isTotal;
    }
    public String getIsTotal() 
    {
        return isTotal;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recCreator", getRecCreator())
            .append("recCreateTime", getRecCreateTime())
            .append("recRevisor", getRecRevisor())
            .append("recReviseTime", getRecReviseTime())
            .append("archiveFlag", getArchiveFlag())
            .append("costCenter", getCostCenter())
            .append("costCenterCname", getCostCenterCname())
            .append("yearMonth", getYearMonth())
            .append("factory", getFactory())
            .append("typeName", getTypeName())
            .append("typeCode", getTypeCode())
            .append("costTon", getCostTon())
            .append("costPerTon", getCostPerTon())
            .append("costEx", getCostEx())
            .append("costPerEx", getCostPerEx())
            .append("nincTon", getNincTon())
            .append("nincPerTon", getNincPerTon())
            .append("nincEx", getNincEx())
            .append("nincPerEx", getNincPerEx())
            .append("allcTon", getAllcTon())
            .append("allcPerTon", getAllcPerTon())
            .append("allcEx", getAllcEx())
            .append("allcPerEx", getAllcPerEx())
            .append("remark", getRemark())
            .append("filed1", getFiled1())
            .append("filed2", getFiled2())
            .append("filed3", getFiled3())
            .append("filed4", getFiled4())
            .append("filed5", getFiled5())
            .toString();
    }
}
