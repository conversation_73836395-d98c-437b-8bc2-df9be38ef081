{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\qualityCostPage\\index.vue?vue&type=style&index=0&id=34153b51&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\qualityCostPage\\index.vue", "mtime": 1754382964686}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog6YeN572u5omA5pyJ5Y+v6IO955qE6auY5bqm57qm5p2fICovDQoqLA0KKjo6YmVmb3JlLA0KKjo6YWZ0ZXIgew0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KfQ0KDQovKiDlrrnlmajmoLflvI8gKi8NCi5hcHAtY29udGFpbmVyIHsNCiAgb3ZlcmZsb3c6IHZpc2libGUgIWltcG9ydGFudDsNCiAgaGVpZ2h0OiBhdXRvICFpbXBvcnRhbnQ7DQogIG1pbi1oZWlnaHQ6IGF1dG8gIWltcG9ydGFudDsNCiAgbWF4LWhlaWdodDogbm9uZSAhaW1wb3J0YW50Ow0KICBwYWRkaW5nOiAyMHB4ICFpbXBvcnRhbnQ7DQogIG1hcmdpbjogMCAhaW1wb3J0YW50Ow0KfQ0KDQovKiDooajmoLzmoLflvI/kvJjljJYgKi8NCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS1oZWFkZXItLWNvbHVtbiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLnZ4ZS10YWJsZSA6OnYtZGVlcCAudnhlLWZvb3Rlci0tY29sdW1uIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi8qIOetm+mAieWMuuWfn+agt+W8jyAqLw0KLmVsLWZvcm0tLWlubGluZSAuZWwtZm9ybS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLnZ4ZS10YWJsZSAudnhlLWNlbGwgew0KICBwYWRkaW5nOiAwIDVweDsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQp9DQoNCi8qIOS4u+imgeihqOagvOagt+W8jyAqLw0KLnZ4ZS10YWJsZSB7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLyog5Zu65a6a5YiX5qC35byPIC0g56Gu5L+d5Zu65a6a5YiX5q2j5bi45pi+56S6ICovDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLWZpeGVkLWxlZnQtd3JhcHBlciB7DQogIHotaW5kZXg6IDEwOw0KfQ0KDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLWZpeGVkLXJpZ2h0LXdyYXBwZXIgew0KICB6LWluZGV4OiAxMDsNCn0NCg0KLyog5rGH5oC76KGo5qC85ZCI6K6h6KGM5qC35byPICovDQoudnhlLXRhYmxlIDo6di1kZWVwIC5zdW1tYXJ5LXJvdyB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQoudnhlLXRhYmxlIDo6di1kZWVwIC5zdW1tYXJ5LXJvdyAudnhlLWNlbGwgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhICFpbXBvcnRhbnQ7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQovKiDoh6rlrprkuYnmu5rliqjmnaHmoLflvI8gLSDmtYXok53oibLliqDnspcgKi8NCi8qIHZ4ZS10YWJsZSDlhoXpg6jmu5rliqjmnaHmoLflvI8gKi8NCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0tYm9keS13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhciwNCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS1ib2R5LS13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhciwNCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0tbWFpbi13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiAyNHB4ICFpbXBvcnRhbnQ7DQogIGhlaWdodDogMjRweCAhaW1wb3J0YW50Ow0KICB6LWluZGV4OiA5OTkgIWltcG9ydGFudDsNCn0NCg0KLyog5b2T5LiN6ZyA6KaB5rua5Yqo5pe26ZqQ6JeP5omA5pyJ5rua5Yqo5p2hICovDQoudnhlLXRhYmxlLm5vLXNjcm9sbCA6OnYtZGVlcCAudnhlLXRhYmxlLS1ib2R5LXdyYXBwZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLA0KLnZ4ZS10YWJsZS5uby1zY3JvbGwgOjp2LWRlZXAgLnZ4ZS1ib2R5LS13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhciwNCi52eGUtdGFibGUubm8tc2Nyb2xsIDo6di1kZWVwIC52eGUtdGFibGUtLW1haW4td3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogMCAhaW1wb3J0YW50Ow0KICBoZWlnaHQ6IDAgIWltcG9ydGFudDsNCiAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDlvZPkuI3pnIDopoHmu5rliqjml7bvvIznoa7kv53ooajmoLzlhoXlrrnlrozlhajmmL7npLogKi8NCi52eGUtdGFibGUubm8tc2Nyb2xsIDo6di1kZWVwIC52eGUtdGFibGUtLWJvZHktd3JhcHBlciwNCi52eGUtdGFibGUubm8tc2Nyb2xsIDo6di1kZWVwIC52eGUtYm9keS0td3JhcHBlciwNCi52eGUtdGFibGUubm8tc2Nyb2xsIDo6di1kZWVwIC52eGUtdGFibGUtLW1haW4td3JhcHBlciB7DQogIG92ZXJmbG93OiB2aXNpYmxlICFpbXBvcnRhbnQ7DQp9DQoNCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0tYm9keS13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhci10cmFjaywNCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS1ib2R5LS13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhci10cmFjaywNCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0tbWFpbi13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIGJhY2tncm91bmQ6ICNmMGY4ZmYgIWltcG9ydGFudDsNCiAgYm9yZGVyLXJhZGl1czogMTJweCAhaW1wb3J0YW50Ow0KfQ0KDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLWJvZHktd3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIsDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtYm9keS0td3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIsDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLW1haW4td3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICBiYWNrZ3JvdW5kOiAjODdjZWViICFpbXBvcnRhbnQ7DQogIGJvcmRlci1yYWRpdXM6IDEycHggIWltcG9ydGFudDsNCiAgYm9yZGVyOiAycHggc29saWQgI2YwZjhmZiAhaW1wb3J0YW50Ow0KfQ0KDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLWJvZHktd3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIsDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtYm9keS0td3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIsDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLW1haW4td3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjNDY4MmI0ICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOihqOagvOWuueWZqOa7muWKqOadoeagt+W8jyAqLw0KZGl2W3N0eWxlKj0ib3ZlcmZsb3cteDogYXV0byJdOjotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiAyNHB4ICFpbXBvcnRhbnQ7DQogIGhlaWdodDogMjRweCAhaW1wb3J0YW50Ow0KICB6LWluZGV4OiA5OTkgIWltcG9ydGFudDsNCn0NCg0KZGl2W3N0eWxlKj0ib3ZlcmZsb3cteDogYXV0byJdOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIGJhY2tncm91bmQ6ICNmMGY4ZmYgIWltcG9ydGFudDsNCiAgYm9yZGVyLXJhZGl1czogMTJweCAhaW1wb3J0YW50Ow0KfQ0KDQpkaXZbc3R5bGUqPSJvdmVyZmxvdy14OiBhdXRvIl06Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZDogIzg3Y2VlYiAhaW1wb3J0YW50Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4ICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogMnB4IHNvbGlkICNmMGY4ZmYgIWltcG9ydGFudDsNCn0NCg0KZGl2W3N0eWxlKj0ib3ZlcmZsb3cteDogYXV0byJdOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQ6ICM0NjgyYjQgIWltcG9ydGFudDsNCn0NCg0KLyog6YCa55So5rua5Yqo5p2h5qC35byPIC0g5pu06auY5LyY5YWI57qnICovDQoudnhlLXRhYmxlIDo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogMjRweCAhaW1wb3J0YW50Ow0KICBoZWlnaHQ6IDI0cHggIWltcG9ydGFudDsNCiAgei1pbmRleDogMTAwMCAhaW1wb3J0YW50Ow0KfQ0KDQoudnhlLXRhYmxlIDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kOiAjZjBmOGZmICFpbXBvcnRhbnQ7DQogIGJvcmRlci1yYWRpdXM6IDEycHggIWltcG9ydGFudDsNCn0NCg0KLnZ4ZS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZDogIzg3Y2VlYiAhaW1wb3J0YW50Ow0KICBib3JkZXItcmFkaXVzOiAxMnB4ICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogMnB4IHNvbGlkICNmMGY4ZmYgIWltcG9ydGFudDsNCn0NCg0KLnZ4ZS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogIzQ2ODJiNCAhaW1wb3J0YW50Ow0KfQ0KDQovKiDmu5rliqjmnaHlrrnlmajlsYLnuqfosIPmlbQgKi8NCi52eGUtdGFibGUgOjp2LWRlZXAgLnZ4ZS10YWJsZS0tYm9keS13cmFwcGVyLA0KLnZ4ZS10YWJsZSA6OnYtZGVlcCAudnhlLWJvZHktLXdyYXBwZXIsDQoudnhlLXRhYmxlIDo6di1kZWVwIC52eGUtdGFibGUtLW1haW4td3JhcHBlciB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50Ow0KICB6LWluZGV4OiAxICFpbXBvcnRhbnQ7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2hBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/qualityCostPage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\" style=\"height: auto !important; min-height: auto !important; max-height: none !important;\">\r\n    <!-- 顶部筛选区域 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"成本中心\" prop=\"costCenter\">\r\n        <el-select v-model=\"queryParams.costCenter\" placeholder=\"请选择成本中心\" clearable size=\"small\" style=\"width: 200px;\">\r\n          <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"会计期\" prop=\"yearMonth\">\r\n        <el-date-picker v-model=\"queryParams.yearMonth\" type=\"month\" placeholder=\"请选择年月\" format=\"yyyy-MM\"\r\n          value-format=\"yyyy-MM\" size=\"small\" style=\"width: 200px;\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"成本类型\" prop=\"costType\">\r\n        <el-select v-model=\"queryParams.costType\" placeholder=\"请选择成本类型\" multiple clearable size=\"small\"\r\n          style=\"width: 300px;\" @change=\"handleCostTypeChange\">\r\n          <el-option v-for=\"item in costTypeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\">导出</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 质量成本表格 -->\r\n    <div v-if=\"qualityCostList.length > 0\"\r\n      style=\"width: 100%; height: 80vh; overflow: visible; margin: 0; padding: 0; display: flex; flex-direction: column; align-items: center;\">\r\n      <!-- 标题和产量信息 -->\r\n      <div style=\"position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;\">\r\n        <h3 style=\"text-align: center; margin: 0;\">兴澄特钢质量成本表</h3>\r\n        <div style=\"position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;\">\r\n          <span style=\"margin-right: 20px;\">单位：元</span>\r\n          {{ getProductionLabel() }}：{{ productionInfo ? formatProduction(productionInfo.costTon) + '吨' : '无' }}\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"overflow-x: auto; width: 95%; max-height: calc(100% - 80px); margin: 0; padding: 0; border: none;\">\r\n        <vxe-table\r\n          :loading=\"loading\"\r\n          :data=\"qualityCostList\"\r\n          border\r\n          :height=\"tableHeight\"\r\n          :class=\"{ 'no-scroll': !needScrollbar }\"\r\n          style=\"table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;\"\r\n          :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#303133' }\"\r\n          :merge-cells=\"mergeCells\"\r\n          stripe\r\n          :auto-resize=\"false\"\r\n          :scroll-y=\"{enabled: needScrollbar}\"\r\n          :scroll-x=\"{enabled: true}\">\r\n          <vxe-column title=\"成本类别\" align=\"center\" field=\"costType\" width=\"5%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                {{ row.costType }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"科目\" field=\"typeName\" width=\"15%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{\r\n                  fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal',\r\n                  textAlign: (row.typeName && row.typeName.includes('小计')) ? 'center' : 'left',\r\n                  display: 'block'\r\n                }\">\r\n                {{ row.typeName }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <!-- 质量成本分组 -->\r\n          <vxe-colgroup title=\"质量成本（列入项）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"costEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.costEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"costPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.costPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 不列入项分组 -->\r\n          <vxe-colgroup title=\"质量成本（不列入项）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"nincEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.nincEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"nincPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.nincPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 质量成本（含不列入项）分组 -->\r\n          <vxe-colgroup title=\"质量成本（总）\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"allcEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.allcEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨钢金额（元/吨）\" align=\"center\" field=\"allcPerEx\" width=\"12%\">\r\n              <template #default=\"{ row }\">\r\n                <span\r\n                  :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                  {{ formatCurrency(row.allcPerEx) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 金额百分比列 -->\r\n          <vxe-column title=\"金额百分比\" align=\"center\" field=\"amountPercent\" width=\"8%\">\r\n            <template #default=\"{ row }\">\r\n              <span\r\n                :style=\"{ fontWeight: (row.typeName && (row.typeName.includes('合计') || row.typeName.includes('小计'))) ? 'bold' : 'normal' }\">\r\n                {{ formatPercentage(row.amountPercent) }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据提示 -->\r\n    <div v-else-if=\"!loading\" style=\"text-align: center; padding: 50px;\">\r\n      <el-empty description=\"请选择查询条件后点击搜索查看数据\"></el-empty>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listQualityCostPage, costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { exportQualityCostPage } from \"@/api/qualityCost/qualityCostPage\";\r\n\r\nexport default {\r\n  name: \"QualityCost\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 质量成本数据列表\r\n      qualityCostList: [],\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      // 产量信息\r\n      productionInfo: null,\r\n      // 合并单元格配置\r\n      mergeCells: [],\r\n      // 成本类型选项\r\n      costTypeOptions: [\r\n        { value: '', label: '全部' },\r\n        { value: 'A', label: 'A-预防成本' },\r\n        { value: 'B', label: 'B-鉴定成本' },\r\n        { value: 'C', label: 'C-内部损失成本' },\r\n        { value: 'D', label: 'D-外部损失成本' }\r\n      ],\r\n      // 查询参数\r\n      queryParams: {\r\n        costCenter: 'JYXCTZG', // 默认选中兴澄特钢汇总\r\n        yearMonth: null,\r\n        costType: ['']\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    /** 判断是否显示汇总行 */\r\n    shouldShowSummaryRows() {\r\n      // 当成本类型选择为\"全部\"时显示汇总行\r\n      return this.queryParams.costType.includes('') || this.queryParams.costType.length === 0;\r\n    },\r\n    /** 计算表格高度 */\r\n    tableHeight() {\r\n      if (!this.qualityCostList || this.qualityCostList.length === 0) {\r\n        return 'auto';\r\n      }\r\n\r\n      // 当成本类型选择非全部时，使用auto高度，不限制最大高度\r\n      if (!this.shouldShowSummaryRows) {\r\n        return 'auto';\r\n      }\r\n\r\n      // 计算所需高度：表头高度 + 数据行高度\r\n      const headerHeight = 40; // 表头高度\r\n      const rowHeight = 32; // 每行数据高度\r\n      const padding = 10; // 额外间距\r\n\r\n      const dataRowsCount = this.qualityCostList.length;\r\n      const calculatedHeight = headerHeight + (dataRowsCount * rowHeight) + padding;\r\n\r\n      // 最大高度限制（80vh - 80px）\r\n      const maxHeight = window.innerHeight * 0.8 - 80;\r\n\r\n      // 返回计算高度和最大高度中的较小值\r\n      return Math.min(calculatedHeight, maxHeight) + 'px';\r\n    },\r\n    /** 判断是否需要显示滚动条 */\r\n    needScrollbar() {\r\n      // 当成本类型选择非全部时，不显示滚动条\r\n      if (!this.shouldShowSummaryRows) {\r\n        return false;\r\n      }\r\n\r\n      if (!this.qualityCostList || this.qualityCostList.length === 0) {\r\n        return false;\r\n      }\r\n\r\n      const headerHeight = 40;\r\n      const rowHeight = 32;\r\n      const padding = 10;\r\n      const dataRowsCount = this.qualityCostList.length;\r\n      const calculatedHeight = headerHeight + (dataRowsCount * rowHeight) + padding;\r\n      const maxHeight = window.innerHeight * 0.8 - 80;\r\n\r\n      return calculatedHeight > maxHeight;\r\n    }\r\n  },\r\n  created() {\r\n    this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n    this.getCostCenterList();\r\n    // 页面加载时自动触发搜索\r\n    this.$nextTick(() => {\r\n      this.handleQuery();\r\n    });\r\n  },\r\n  mounted() {\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize);\r\n  },\r\n  beforeDestroy() {\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.handleResize);\r\n  },\r\n  methods: {\r\n    /** 获取成本中心列表 */\r\n    getCostCenterList() {\r\n      costCenterlist().then(response => {\r\n        const options = response.data || [];\r\n        // 将JYXCTZG选项排在第一位\r\n        const jyxctzgOption = options.find(item => item.key === 'JYXCTZG');\r\n        const otherOptions = options.filter(item => item.key !== 'JYXCTZG');\r\n\r\n        if (jyxctzgOption) {\r\n          this.costCenterOptions = [jyxctzgOption, ...otherOptions];\r\n        } else {\r\n          this.costCenterOptions = options;\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取成本中心列表失败');\r\n      });\r\n    },\r\n\r\n    /** 获取成本类型 */\r\n    getCostType(typeCode) {\r\n      if (!typeCode) return '';\r\n      const firstChar = typeCode.charAt(0).toUpperCase();\r\n      switch (firstChar) {\r\n        case 'A': return '预防成本';\r\n        case 'B': return '鉴定成本';\r\n        case 'C': return '内部损失成本';\r\n        case 'D': return '外部损失成本';\r\n        default: return '汇总';\r\n      }\r\n    },\r\n\r\n    /** 计算合并单元格 */\r\n    calculateMergeCells() {\r\n      const mergeCells = [];\r\n      const costTypeGroups = {};\r\n\r\n      // 按成本类型分组\r\n      this.qualityCostList.forEach((row, index) => {\r\n        const costType = row.costType;\r\n        if (!costTypeGroups[costType]) {\r\n          costTypeGroups[costType] = [];\r\n        }\r\n        costTypeGroups[costType].push(index);\r\n      });\r\n\r\n      // 生成合并配置\r\n      Object.values(costTypeGroups).forEach(group => {\r\n        if (group.length > 1) {\r\n          mergeCells.push({\r\n            row: group[0],\r\n            col: 0,\r\n            rowspan: group.length,\r\n            colspan: 1\r\n          });\r\n        }\r\n      });\r\n\r\n      this.mergeCells = mergeCells;\r\n    },\r\n\r\n    /** 格式化数字 */\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化百分比 */\r\n    formatPercentage(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return (Number(num) * 100).toFixed(3) + '%';\r\n    },\r\n\r\n    /** 格式化货币 */\r\n    formatCurrency(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化产量/销量 */\r\n    formatProduction(num) {\r\n      if (num === null || num === undefined || num === '' || num === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toFixed(3);\r\n    },\r\n\r\n    /** 成本类型变化处理 */\r\n    handleCostTypeChange(value) {\r\n      // 如果选择了\"全部\"，清空其他选项\r\n      if (value.includes('')) {\r\n        if (value.length > 1) {\r\n          // 如果同时选择了\"全部\"和其他选项，只保留\"全部\"\r\n          this.queryParams.costType = [''];\r\n        }\r\n      } else {\r\n        // 如果没有选择\"全部\"，确保\"全部\"不在选择列表中\r\n        this.queryParams.costType = value.filter(item => item !== '');\r\n      }\r\n    },\r\n\r\n    /** 获取产量标签 */\r\n    getProductionLabel() {\r\n      if (this.queryParams.costCenter === 'JYXCTZG') {\r\n        return '销量';\r\n      } else {\r\n        return '产量';\r\n      }\r\n    },\r\n\r\n    /** 查询质量成本列表 */\r\n    getList() {\r\n      // 验证必填参数\r\n      if (!this.queryParams.yearMonth) {\r\n        this.$message.warning('请选择会计期');\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n\r\n      // 处理日期格式：将 2025-06 转换为 202506\r\n      const formatYearMonth = this.queryParams.yearMonth.replace('-', '');\r\n\r\n      // 处理成本类型参数\r\n      let typeCodeList = [];\r\n      if (this.queryParams.costType && this.queryParams.costType.length > 0) {\r\n        // 如果选择了\"全部\"或数组为空，传递空数组\r\n        if (this.queryParams.costType.includes('') || this.queryParams.costType.length === 0) {\r\n          typeCodeList = [];\r\n        } else {\r\n          typeCodeList = this.queryParams.costType;\r\n        }\r\n      }\r\n\r\n      const requestParams = {\r\n        costCenter: this.queryParams.costCenter || '',\r\n        yearMonth: formatYearMonth,\r\n        typeCodeList: typeCodeList\r\n      };\r\n\r\n      listQualityCostPage(requestParams).then(response => {\r\n        const data = response.data || {};\r\n\r\n        // 从返回的QualityCostMonthlyVO对象中获取数据\r\n        let qualityCostDetailList = data.qualityCostDetailList || [];\r\n\r\n        // 统一使用typeCode为'Z'的数据作为产量/销量信息\r\n        const productionData = qualityCostDetailList.find(item => item.typeCode === 'Z');\r\n        this.productionInfo = productionData || null;\r\n\r\n\r\n\r\n        // 为每一行添加成本类别信息，用于前端表格显示和合并\r\n        qualityCostDetailList.forEach(row => {\r\n          row.costType = this.getCostType(row.typeCode);\r\n        });\r\n\r\n        // 根据成本类型选择过滤汇总行\r\n        if (!this.shouldShowSummaryRows) {\r\n          // 过滤掉包含\"合计\"、\"小计\"、\"汇总\"的行\r\n          qualityCostDetailList = qualityCostDetailList.filter(row => {\r\n            return !(row.typeName && (\r\n              row.typeName.includes('合计') ||\r\n              row.typeName.includes('小计') ||\r\n              row.typeName.includes('汇总')\r\n            ));\r\n          });\r\n        }\r\n\r\n        this.qualityCostList = qualityCostDetailList;\r\n\r\n        // 计算合并单元格\r\n        this.calculateMergeCells();\r\n\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n        this.$message.error('查询失败');\r\n        this.qualityCostList = [];\r\n        this.productionInfo = null;\r\n        this.mergeCells = [];\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      // 重置为默认值\r\n      this.queryParams.costCenter = 'JYXCTZG'; // 默认选中兴澄特钢汇总\r\n      this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n      this.queryParams.costType = [''];\r\n      // 清空数据\r\n      this.qualityCostList = [];\r\n      this.productionInfo = null;\r\n      this.mergeCells = [];\r\n    },\r\n\r\n    /** 获取默认会计期（上个月） */\r\n    getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    /** 处理窗口大小变化 */\r\n    handleResize() {\r\n      // 强制重新计算表格高度\r\n      this.$forceUpdate();\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 检查是否有数据\r\n      if (!this.qualityCostList || this.qualityCostList.length === 0) {\r\n        this.$message.warning('暂无数据可导出，请先查询数据');\r\n        return;\r\n      }\r\n\r\n      // 构建查询参数，与查询数据时保持一致的格式\r\n      let queryParams = {\r\n        costCenter: this.queryParams.costCenter,\r\n        yearMonth: this.queryParams.yearMonth.replace('-', ''), // 将 2025-06 转换为 202506\r\n        typeCodeList: this.queryParams.costType,\r\n        costTon: this.formatProduction(this.productionInfo.costTon)\r\n      };\r\n\r\n\r\n      this.$confirm('是否确认导出兴澄特钢质量成本表数据?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.loading = true;\r\n        return exportQualityCostPage(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    }\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 重置所有可能的高度约束 */\r\n*,\r\n*::before,\r\n*::after {\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 容器样式 */\r\n.app-container {\r\n  overflow: visible !important;\r\n  height: auto !important;\r\n  min-height: auto !important;\r\n  max-height: none !important;\r\n  padding: 20px !important;\r\n  margin: 0 !important;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.vxe-table ::v-deep .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-footer--column {\r\n  background-color: #fafafa;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 筛选区域样式 */\r\n.el-form--inline .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.vxe-table .vxe-cell {\r\n  padding: 0 5px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 主要表格样式 */\r\n.vxe-table {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 固定列样式 - 确保固定列正常显示 */\r\n.vxe-table ::v-deep .vxe-table--fixed-left-wrapper {\r\n  z-index: 10;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--fixed-right-wrapper {\r\n  z-index: 10;\r\n}\r\n\r\n/* 汇总表格合计行样式 */\r\n.vxe-table ::v-deep .summary-row {\r\n  background-color: #f8f9fa;\r\n  font-weight: bold;\r\n}\r\n\r\n.vxe-table ::v-deep .summary-row .vxe-cell {\r\n  background-color: #f8f9fa !important;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 自定义滚动条样式 - 浅蓝色加粗 */\r\n/* vxe-table 内部滚动条样式 */\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n  z-index: 999 !important;\r\n}\r\n\r\n/* 当不需要滚动时隐藏所有滚动条 */\r\n.vxe-table.no-scroll ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.vxe-table.no-scroll ::v-deep .vxe-body--wrapper::-webkit-scrollbar,\r\n.vxe-table.no-scroll ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar {\r\n  width: 0 !important;\r\n  height: 0 !important;\r\n  display: none !important;\r\n}\r\n\r\n/* 当不需要滚动时，确保表格内容完全显示 */\r\n.vxe-table.no-scroll ::v-deep .vxe-table--body-wrapper,\r\n.vxe-table.no-scroll ::v-deep .vxe-body--wrapper,\r\n.vxe-table.no-scroll ::v-deep .vxe-table--main-wrapper {\r\n  overflow: visible !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-track,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-track {\r\n  background: #f0f8ff !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-thumb,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-thumb {\r\n  background: #87ceeb !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f0f8ff !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.vxe-table ::v-deep .vxe-body--wrapper::-webkit-scrollbar-thumb:hover,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #4682b4 !important;\r\n}\r\n\r\n/* 表格容器滚动条样式 */\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n  z-index: 999 !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-track {\r\n  background: #f0f8ff !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-thumb {\r\n  background: #87ceeb !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f0f8ff !important;\r\n}\r\n\r\ndiv[style*=\"overflow-x: auto\"]::-webkit-scrollbar-thumb:hover {\r\n  background: #4682b4 !important;\r\n}\r\n\r\n/* 通用滚动条样式 - 更高优先级 */\r\n.vxe-table ::-webkit-scrollbar {\r\n  width: 24px !important;\r\n  height: 24px !important;\r\n  z-index: 1000 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-track {\r\n  background: #f0f8ff !important;\r\n  border-radius: 12px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #87ceeb !important;\r\n  border-radius: 12px !important;\r\n  border: 2px solid #f0f8ff !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #4682b4 !important;\r\n}\r\n\r\n/* 滚动条容器层级调整 */\r\n.vxe-table ::v-deep .vxe-table--body-wrapper,\r\n.vxe-table ::v-deep .vxe-body--wrapper,\r\n.vxe-table ::v-deep .vxe-table--main-wrapper {\r\n  position: relative !important;\r\n  z-index: 1 !important;\r\n}\r\n</style>\r\n"]}]}