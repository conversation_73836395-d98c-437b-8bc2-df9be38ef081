{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\regradeDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\regradeDetail\\index.vue", "mtime": 1754372909833}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_qualityCostDetail", "require", "_regradeDetail", "name", "data", "queryParams", "pageNum", "pageSize", "costCenter", "accountingPeriod", "getDefaultYearMonth", "planFlag", "planFlagOptions", "label", "value", "tableData", "tableLoading", "costCenterOptions", "costCenterLoading", "sumData", "total", "searchParams", "oldSgSign", "oldSgStd", "newSgSign", "newSgStd", "reason", "searchMode", "searchModeOptions", "computed", "subtotalData", "length", "totalTonnage", "totalPriceDifference", "totalAmount", "costTon", "costPerTon", "costEx", "watch", "handler", "fetchTableData", "mounted", "getCostCenterList", "methods", "now", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "hour", "getHours", "prevMonth", "prevYear", "concat", "String", "padStart", "convertStockArea", "stockArea", "areaStr", "toString", "toUpperCase", "_this", "costCenterlist", "then", "response", "key", "$nextTick", "catch", "error", "console", "$message", "finally", "_this2", "costCenterParam", "selectedOption", "find", "item", "yearMonth", "replace", "Object", "assign", "log", "listAllRegradeDetail", "rows", "getSum", "getPlanFlagValue", "getPlanFlagTagType", "getCurrentMonth", "formatNumber", "decimals", "arguments", "undefined", "Number", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "subtotalSpanMethod", "_ref", "row", "column", "rowIndex", "columnIndex", "rowspan", "colspan", "handleSearch", "handleReset"], "sources": ["src/views/qualityCost/regradeDetail/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"regrade-detail-container\">\r\n      <!-- 表格标题 -->\r\n      <div class=\"table-title\">\r\n        <h2>兴澄特钢质量成本表-产品改判损失</h2>\r\n      </div>\r\n\r\n      <!-- 表格头部信息 -->\r\n      <div class=\"table-header-info\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">成本中心名称：</span>\r\n          <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\">\r\n            <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">会计期：</span>\r\n          <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"2025-06\" format=\"yyyy-MM\"\r\n            value-format=\"yyyy-MM\" style=\"width: 150px;\">\r\n          </el-date-picker>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">是否计划内：</span>\r\n          <el-select v-model=\"planFlag\" placeholder=\"全部\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in planFlagOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"search-bar-row\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判前钢种：</span>\r\n          <el-input v-model=\"searchParams.oldSgSign\" placeholder=\"请输入改判前钢种\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判前标准：</span>\r\n          <el-input v-model=\"searchParams.oldSgStd\" placeholder=\"请输入改判前标准\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判后钢种：</span>\r\n          <el-input v-model=\"searchParams.newSgSign\" placeholder=\"请输入改判后钢种\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判后标准：</span>\r\n          <el-input v-model=\"searchParams.newSgStd\" placeholder=\"请输入改判后标准\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判原因：</span>\r\n          <el-input v-model=\"searchParams.reason\" placeholder=\"请输入改判原因\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">搜索模式：</span>\r\n          <el-select v-model=\"searchParams.searchMode\" placeholder=\"请选择搜索模式\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in searchModeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item button-group\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\" size=\"small\">搜索</el-button>\r\n          <el-button @click=\"handleReset\" size=\"small\">重置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主表格 -->\r\n      <div class=\"main-table\">\r\n        <el-table :data=\"tableData\" border style=\"width: 100%; table-layout: fixed;\" class=\"regrade-detail-table\" v-loading=\"tableLoading\"\r\n          element-loading-text=\"加载中...\">\r\n          <el-table-column prop=\"oldSgSign\" label=\"改判前钢种\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"oldSgStd\" label=\"改判前标准\" align=\"center\" width=\"150\" />\r\n          <el-table-column prop=\"newSgSign\" label=\"改判后钢种\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"newSgStd\" label=\"改判后标准\" align=\"center\" width=\"150\" />\r\n          <el-table-column prop=\"crShp\" label=\"截面\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"thick\" label=\"厚度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.thick !== null && scope.row.thick !== undefined\">\r\n                {{ scope.row.thick }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"width\" label=\"宽度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.width !== null && scope.row.width !== undefined\">\r\n                {{ scope.row.width }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"len\" label=\"长度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.len !== null && scope.row.len !== undefined\">\r\n                {{ scope.row.len }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costPerTon\" label=\"售价差（元/吨）\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costPerTon !== null && scope.row.costPerTon !== undefined\">\r\n                {{ formatNumber(scope.row.costPerTon) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costTon\" label=\"吨位\" align=\"right\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costTon !== null && scope.row.costTon !== undefined\">\r\n                {{ formatNumber(scope.row.costTon, 2) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"costEx\" label=\"损失金额（元）\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costEx !== null && scope.row.costEx !== undefined\">\r\n                {{ formatNumber(scope.row.costEx) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"currStock\" label=\"当前库存\" align=\"center\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.currStock !== null && scope.row.currStock !== undefined && scope.row.currStock !== ''\">\r\n                {{ convertStockArea(scope.row.currStock) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"reason\" label=\"改判原因\" align=\"center\" min-width=\"120\" />\r\n          <el-table-column prop=\"planFlag\" label=\"是否计划内\" align=\"center\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"getPlanFlagTagType(scope.row.planFlag)\">\r\n                {{ getPlanFlagValue(scope.row.planFlag) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 小计行 -->\r\n      <div class=\"subtotal-section\">\r\n        <el-table :data=\"subtotalData\" border style=\"width: 100%; table-layout: fixed;\" class=\"subtotal-table\" :show-header=\"false\"\r\n          :span-method=\"subtotalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" width=\"150\"/>\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" width=\"150\"/>\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty8\" label=\"\" align=\"center\" width=\"130\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalPriceDifference\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalPriceDifference) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty9\" label=\"\" align=\"center\" width=\"100\" />\r\n          <el-table-column prop=\"empty10\" label=\"\" align=\"center\" min-width=\"120\" />\r\n          <el-table-column prop=\"empty11\" label=\"\" align=\"center\" width=\"100\" />\r\n        </el-table>\r\n      </div>\r\n      <pagination :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"fetchTableData\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { listAllRegradeDetail, getSum } from \"@/api/qualityCost/regradeDetail\";\r\n\r\nexport default {\r\n  name: \"RegradeDetail\",\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: this.getDefaultYearMonth(),\r\n      // 新增：是否计划内筛选\r\n      planFlag: '1',\r\n      planFlagOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '是', value: '1' },\r\n        { label: '否', value: '0' }\r\n      ],\r\n      // 表格数据\r\n      tableData: [],\r\n      // 表格加载状态\r\n      tableLoading: false,\r\n      // 成本中心列表\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      sumData: {},\r\n      total: 0,\r\n      // 新增：搜索参数\r\n      searchParams: {\r\n        oldSgSign: '',\r\n        oldSgStd: '',\r\n        newSgSign: '',\r\n        newSgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      },\r\n      searchModeOptions: [\r\n        { label: '模糊搜索', value: '模糊搜索' },\r\n        { label: '精确搜索', value: '精确搜索' }\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算小计数据\r\n    subtotalData() {\r\n      if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品改判损失小计\",\r\n          totalTonnage: 0,\r\n          totalPriceDifference: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.sumData.costTon;\r\n\r\n      const totalPriceDifference = this.sumData.costPerTon;\r\n\r\n      const totalAmount = this.sumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品改判损失小计\",\r\n        totalTonnage: totalTonnage,\r\n        totalPriceDifference: totalPriceDifference,\r\n        totalAmount: totalAmount\r\n      }];\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 新增：监听是否计划内变化\r\n    planFlag: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n  },\r\n  methods: {\r\n     /** 获取默认会计期 */\r\n      getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    // 库区数据转换\r\n    convertStockArea(stockArea) {\r\n      if (!stockArea) return '';\r\n\r\n      const areaStr = stockArea.toString().toUpperCase();\r\n\r\n      if (areaStr === 'P') {\r\n        return '坯料';\r\n      } else if (areaStr === 'M') {\r\n        return '在制品';\r\n      } else if (areaStr === 'C') {\r\n        return '成品';\r\n      } else {\r\n        return stockArea; // 如果不是P、M、C，返回原值\r\n      }\r\n    },\r\n\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据获取\r\n          this.$nextTick(() => {\r\n            this.fetchTableData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n\r\n    // 获取表格数据\r\n    fetchTableData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod) {\r\n        this.tableData = [];\r\n        return;\r\n      }\r\n\r\n      this.tableLoading = true;\r\n\r\n      // 当选择\"江阴兴澄特种钢铁\"时，查询所有数据（不传costCenter参数）\r\n      let costCenterParam = this.costCenter;\r\n      const selectedOption = this.costCenterOptions.find(item => item.key === this.costCenter);\r\n      if (selectedOption && selectedOption.label === '公司') {\r\n        costCenterParam = ''; // 设置为空字符串，查询所有数据\r\n      }\r\n\r\n      this.queryParams.costCenter = costCenterParam;\r\n      this.queryParams.yearMonth = this.accountingPeriod.replace('-', '');\r\n      this.queryParams.planFlag = this.planFlag;\r\n      this.queryParams.searchMode = this.searchParams.searchMode; // 添加搜索模式参数\r\n\r\n      // 合并搜索参数到查询参数\r\n      Object.assign(this.queryParams, this.searchParams);\r\n\r\n      console.log('查询参数:', this.queryParams);\r\n\r\n      listAllRegradeDetail(this.queryParams).then(response => {\r\n        //this.tableData = (response.rows || []).filter(item => item.costEx !== null && item.costEx !== undefined && item.costEx !== 0);\r\n        this.tableData = response.rows || [];\r\n        this.total = response.total || 0;\r\n      }).catch(error => {\r\n        console.error('获取改判损失数据失败:', error);\r\n        this.$message.error('获取改判损失数据失败');\r\n        this.tableData = [];\r\n        this.total = 0;\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getSum(this.queryParams).then(response => {\r\n        this.sumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.sumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n    },\r\n    // 处理计划内标志值\r\n    getPlanFlagValue(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return '否';\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return '是';\r\n      }\r\n      return '未知'; // 既不是0也不是1时显示未知\r\n    },\r\n    // 获取计划内标志标签类型\r\n    getPlanFlagTagType(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return 'danger'; // 绿色\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return 'success'; // 红色\r\n      }\r\n      return 'warning'; // 黄色（未知状态）\r\n    },\r\n    // 获取当前年月\r\n    getCurrentMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      return `${year}-${month}`;\r\n    },\r\n    // 格式化数字显示\r\n    formatNumber(value, decimals = 2) {\r\n      if (value === null || value === undefined) return '';\r\n      return Number(value).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: decimals,\r\n        maximumFractionDigits: decimals\r\n      });\r\n    },\r\n    subtotalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前8列为小计标签\r\n      if (columnIndex >= 0 && columnIndex <= 8) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 9\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 搜索按钮点击事件\r\n    handleSearch() {\r\n      this.queryParams.pageNum = 1; // 搜索时重置页码\r\n      this.fetchTableData();\r\n    },\r\n    // 重置按钮点击事件\r\n    handleReset() {\r\n      this.searchParams = {\r\n        oldSgSign: '',\r\n        oldSgStd: '',\r\n        newSgSign: '',\r\n        newSgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      };\r\n      this.queryParams.pageNum = 1; // 重置页码\r\n      this.fetchTableData();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.regrade-detail-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.table-header-info {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.header-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-item .label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .value {\r\n  color: #303133;\r\n}\r\n\r\n.header-item:first-child .label {\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.search-bar-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 12px;\r\n}\r\n\r\n.button-group {\r\n  margin-left: auto;\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.main-table {\r\n  margin-bottom: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.subtotal-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n/* 表格样式定制 */\r\n.regrade-detail-table {\r\n  font-size: 14px;\r\n  table-layout: fixed !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header-wrapper) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th) {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n  padding: 12px 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body td) {\r\n  box-sizing: border-box;\r\n  padding: 12px 8px;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body tr:nth-child(odd)) {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body tr:hover) {\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n/* 小计表格样式 */\r\n.subtotal-table {\r\n  font-size: 14px;\r\n  table-layout: fixed !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body tr) {\r\n  background-color: #f0f9ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body td) {\r\n  background-color: #f0f9ff !important;\r\n  padding: 12px 8px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 确保表格容器对齐 */\r\n.main-table,\r\n.subtotal-section {\r\n  width: 100%;\r\n}\r\n\r\n.main-table .el-table,\r\n.subtotal-section .el-table {\r\n  width: 100% !important;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1400px) {\r\n  .table-header-info {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .regrade-detail-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .regrade-detail-table :deep(.el-table__body td) {\r\n    padding: 8px 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .regrade-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .main-table {\r\n    overflow-x: auto;\r\n  }\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.header-item .el-input {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n/* 360浏览器兼容性修复 */\r\n.regrade-detail-table :deep(.el-table__header),\r\n.regrade-detail-table :deep(.el-table__body) {\r\n  table-layout: fixed;\r\n  width: 100%;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header colgroup),\r\n.regrade-detail-table :deep(.el-table__body colgroup) {\r\n  width: 100%;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body) {\r\n  table-layout: fixed;\r\n  width: 100%;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body colgroup) {\r\n  width: 100%;\r\n}\r\n\r\n/* 强制表格列宽度同步 */\r\n.regrade-detail-table :deep(.el-table__header th),\r\n.regrade-detail-table :deep(.el-table__body td) {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 修复边框对齐问题 */\r\n.regrade-detail-table :deep(.el-table),\r\n.subtotal-table :deep(.el-table) {\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th),\r\n.regrade-detail-table :deep(.el-table__body td),\r\n.subtotal-table :deep(.el-table__body td) {\r\n  border-right: 1px solid #ebeef5;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th:last-child),\r\n.regrade-detail-table :deep(.el-table__body td:last-child),\r\n.subtotal-table :deep(.el-table__body td:last-child) {\r\n  border-right: 1px solid #ebeef5;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA6KA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA;MACAC,UAAA;MACAC,gBAAA,OAAAC,mBAAA;MACA;MACAC,QAAA;MACAC,eAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAC,SAAA;MACA;MACAC,YAAA;MACA;MACAC,iBAAA;MACAC,iBAAA;MACAC,OAAA;MACAC,KAAA;MACA;MACAC,YAAA;QACAC,SAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACAC,iBAAA,GACA;QAAAf,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAe,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,UAAAf,SAAA,SAAAA,SAAA,CAAAgB,MAAA;QACA;UACAlB,KAAA;UACAmB,YAAA;UACAC,oBAAA;UACAC,WAAA;QACA;MACA;MAEA,IAAAF,YAAA,QAAAb,OAAA,CAAAgB,OAAA;MAEA,IAAAF,oBAAA,QAAAd,OAAA,CAAAiB,UAAA;MAEA,IAAAF,WAAA,QAAAf,OAAA,CAAAkB,MAAA;MAEA;QACAxB,KAAA;QACAmB,YAAA,EAAAA,YAAA;QACAC,oBAAA,EAAAA,oBAAA;QACAC,WAAA,EAAAA;MACA;IACA;EACA;EACAI,KAAA;IACA;IACA9B,UAAA;MACA+B,OAAA,WAAAA,QAAA;QACA,KAAAlC,WAAA,CAAAC,OAAA;QACA,KAAAkC,cAAA;MACA;IACA;IACA;IACA/B,gBAAA;MACA8B,OAAA,WAAAA,QAAA;QACA,KAAAlC,WAAA,CAAAC,OAAA;QACA,KAAAkC,cAAA;MACA;IACA;IACA;IACA7B,QAAA;MACA4B,OAAA,WAAAA,QAAA;QACA,KAAAlC,WAAA,CAAAC,OAAA;QACA,KAAAkC,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACA,cACAjC,mBAAA,WAAAA,oBAAA;MACA,IAAAkC,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAJ,GAAA,CAAAK,QAAA;MACA,IAAAC,GAAA,GAAAN,GAAA,CAAAO,OAAA;MACA,IAAAC,IAAA,GAAAR,GAAA,CAAAS,QAAA;;MAEA;MACA,IAAAH,GAAA,SAAAA,GAAA,WAAAE,IAAA;QACA;QACA,IAAAE,SAAA,GAAAN,KAAA,cAAAA,KAAA;QACA,IAAAO,QAAA,GAAAP,KAAA,SAAAF,IAAA,OAAAA,IAAA;QACA,UAAAU,MAAA,CAAAD,QAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAH,SAAA,EAAAI,QAAA;MACA;QACA,UAAAF,MAAA,CAAAV,IAAA,OAAAU,MAAA,CAAAC,MAAA,CAAAT,KAAA,EAAAU,QAAA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAC,SAAA;MACA,KAAAA,SAAA;MAEA,IAAAC,OAAA,GAAAD,SAAA,CAAAE,QAAA,GAAAC,WAAA;MAEA,IAAAF,OAAA;QACA;MACA,WAAAA,OAAA;QACA;MACA,WAAAA,OAAA;QACA;MACA;QACA,OAAAD,SAAA;MACA;IACA;IAEA;IACAlB,iBAAA,WAAAA,kBAAA;MAAA,IAAAsB,KAAA;MACA,KAAA9C,iBAAA;MACA,IAAA+C,iCAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA/C,iBAAA,GAAAkD,QAAA,CAAA/D,IAAA;QACA;QACA,IAAA4D,KAAA,CAAA/C,iBAAA,CAAAc,MAAA;UACAiC,KAAA,CAAAxD,UAAA,GAAAwD,KAAA,CAAA/C,iBAAA,IAAAmD,GAAA;UACA;UACAJ,KAAA,CAAAK,SAAA;YACAL,KAAA,CAAAxB,cAAA;UACA;QACA;MACA,GAAA8B,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,gBAAAA,KAAA;QACAP,KAAA,CAAAS,QAAA,CAAAF,KAAA;MACA,GAAAG,OAAA;QACAV,KAAA,CAAA9C,iBAAA;MACA;IACA;IAEA;IACAsB,cAAA,WAAAA,eAAA;MAAA,IAAAmC,MAAA;MACA;MACA,UAAAnE,UAAA,UAAAC,gBAAA;QACA,KAAAM,SAAA;QACA;MACA;MAEA,KAAAC,YAAA;;MAEA;MACA,IAAA4D,eAAA,QAAApE,UAAA;MACA,IAAAqE,cAAA,QAAA5D,iBAAA,CAAA6D,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAX,GAAA,KAAAO,MAAA,CAAAnE,UAAA;MAAA;MACA,IAAAqE,cAAA,IAAAA,cAAA,CAAAhE,KAAA;QACA+D,eAAA;MACA;MAEA,KAAAvE,WAAA,CAAAG,UAAA,GAAAoE,eAAA;MACA,KAAAvE,WAAA,CAAA2E,SAAA,QAAAvE,gBAAA,CAAAwE,OAAA;MACA,KAAA5E,WAAA,CAAAM,QAAA,QAAAA,QAAA;MACA,KAAAN,WAAA,CAAAsB,UAAA,QAAAN,YAAA,CAAAM,UAAA;;MAEA;MACAuD,MAAA,CAAAC,MAAA,MAAA9E,WAAA,OAAAgB,YAAA;MAEAmD,OAAA,CAAAY,GAAA,eAAA/E,WAAA;MAEA,IAAAgF,mCAAA,OAAAhF,WAAA,EAAA6D,IAAA,WAAAC,QAAA;QACA;QACAQ,MAAA,CAAA5D,SAAA,GAAAoD,QAAA,CAAAmB,IAAA;QACAX,MAAA,CAAAvD,KAAA,GAAA+C,QAAA,CAAA/C,KAAA;MACA,GAAAkD,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,gBAAAA,KAAA;QACAI,MAAA,CAAAF,QAAA,CAAAF,KAAA;QACAI,MAAA,CAAA5D,SAAA;QACA4D,MAAA,CAAAvD,KAAA;MACA,GAAAsD,OAAA;QACAC,MAAA,CAAA3D,YAAA;MACA;MAEA,IAAAuE,qBAAA,OAAAlF,WAAA,EAAA6D,IAAA,WAAAC,QAAA;QACAQ,MAAA,CAAAxD,OAAA,GAAAgD,QAAA,CAAA/D,IAAA;MACA,GAAAkE,KAAA,WAAAC,KAAA;QACAI,MAAA,CAAAF,QAAA,CAAAF,KAAA;QACAI,MAAA,CAAAxD,OAAA;MACA,GAAAuD,OAAA;QACAC,MAAA,CAAA3D,YAAA;MACA;IACA;IACA;IACAwE,gBAAA,WAAAA,iBAAA7E,QAAA;MACA,IAAAA,QAAA,YAAAA,QAAA;QACA;MACA,WAAAA,QAAA,YAAAA,QAAA;QACA;MACA;MACA;IACA;IACA;IACA8E,kBAAA,WAAAA,mBAAA9E,QAAA;MACA,IAAAA,QAAA,YAAAA,QAAA;QACA;MACA,WAAAA,QAAA,YAAAA,QAAA;QACA;MACA;MACA;IACA;IACA;IACA+E,eAAA,WAAAA,gBAAA;MACA,IAAA9C,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAS,MAAA,CAAAb,GAAA,CAAAK,QAAA,QAAAS,QAAA;MACA,UAAAF,MAAA,CAAAV,IAAA,OAAAU,MAAA,CAAAR,KAAA;IACA;IACA;IACA2C,YAAA,WAAAA,aAAA7E,KAAA;MAAA,IAAA8E,QAAA,GAAAC,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAA/E,KAAA,aAAAA,KAAA,KAAAgF,SAAA;MACA,OAAAC,MAAA,CAAAjF,KAAA,EAAAkF,cAAA;QACAC,qBAAA,EAAAL,QAAA;QACAM,qBAAA,EAAAN;MACA;IACA;IACAO,kBAAA,WAAAA,mBAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,MAAA,GAAAF,IAAA,CAAAE,MAAA;QAAAC,QAAA,GAAAH,IAAA,CAAAG,QAAA;QAAAC,WAAA,GAAAJ,IAAA,CAAAI,WAAA;MACA;MACA,IAAAA,WAAA,SAAAA,WAAA;QACA,IAAAA,WAAA;UACA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA;YACAD,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA;MACA;QACAD,OAAA;QACAC,OAAA;MACA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAtG,WAAA,CAAAC,OAAA;MACA,KAAAkC,cAAA;IACA;IACA;IACAoE,WAAA,WAAAA,YAAA;MACA,KAAAvF,YAAA;QACAC,SAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACA,KAAAtB,WAAA,CAAAC,OAAA;MACA,KAAAkC,cAAA;IACA;EACA;AACA", "ignoreList": []}]}