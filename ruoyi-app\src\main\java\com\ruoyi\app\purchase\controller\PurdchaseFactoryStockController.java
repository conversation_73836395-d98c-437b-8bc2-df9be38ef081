package com.ruoyi.app.purchase.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.purchase.domain.PurdchaseFactoryStock;
import com.ruoyi.app.purchase.service.IPurdchaseFactoryStockService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 采购分厂库存Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/purchase/purdchaseFactoryStock")
public class PurdchaseFactoryStockController extends BaseController
{
    @Autowired
    private IPurdchaseFactoryStockService purdchaseFactoryStockService;

    /**
     * 查询采购分厂库存列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PurdchaseFactoryStock purdchaseFactoryStock)
    {
        startPage();
        List<PurdchaseFactoryStock> list = purdchaseFactoryStockService.selectPurdchaseFactoryStockList(purdchaseFactoryStock);
        return getDataTable(list);
    }

    /**
     * 查询最近31天的采购分厂库存列表
     * 参数为分厂名称
     */
    @GetMapping("/listMonthly")
    public AjaxResult listMonthly(PurdchaseFactoryStock purdchaseFactoryStock)
    {
        List<PurdchaseFactoryStock> list = purdchaseFactoryStockService.selectPurdchaseFactoryStockListMonthly(purdchaseFactoryStock);
        return AjaxResult.success(list);
    }

    /**
     * 获取分厂名称列表
     * @param purdchaseFactoryStock
     * @return
     */
    @GetMapping("/depNameList")
    public AjaxResult depNameList()
    {
        return AjaxResult.success(purdchaseFactoryStockService.selectDepNameList());
    }

    /**
     * 导出采购分厂库存列表
     */
    @Log(title = "采购分厂库存", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(PurdchaseFactoryStock purdchaseFactoryStock)
    {
        List<PurdchaseFactoryStock> list = purdchaseFactoryStockService.selectPurdchaseFactoryStockList(purdchaseFactoryStock);
        ExcelUtil<PurdchaseFactoryStock> util = new ExcelUtil<PurdchaseFactoryStock>(PurdchaseFactoryStock.class);
        return util.exportExcel(list, "purdchaseFactoryStock");
    }

    /**
     * 获取采购分厂库存详细信息
     */
    @GetMapping(value = "/{dep}")
    public AjaxResult getInfo(@PathVariable("dep") String dep)
    {
        return AjaxResult.success(purdchaseFactoryStockService.selectPurdchaseFactoryStockById(dep));
    }

    /**
     * 新增采购分厂库存
     */
    @Log(title = "采购分厂库存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PurdchaseFactoryStock purdchaseFactoryStock)
    {
        return toAjax(purdchaseFactoryStockService.insertPurdchaseFactoryStock(purdchaseFactoryStock));
    }

    /**
     * 修改采购分厂库存
     */
    @Log(title = "采购分厂库存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PurdchaseFactoryStock purdchaseFactoryStock)
    {
        return toAjax(purdchaseFactoryStockService.updatePurdchaseFactoryStock(purdchaseFactoryStock));
    }

    /**
     * 删除采购分厂库存
     */
    @Log(title = "采购分厂库存", businessType = BusinessType.DELETE)
	@DeleteMapping("/{deps}")
    public AjaxResult remove(@PathVariable String[] deps)
    {
        return toAjax(purdchaseFactoryStockService.deletePurdchaseFactoryStockByIds(deps));
    }
}
