{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue", "mtime": 1754372909829}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQppbXBvcnQgeyBjb3N0Q2VudGVybGlzdCB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3F1YWxpdHlDb3N0RGV0YWlsIjsNCmltcG9ydCB7IGdldFBpZUNoYXJ0RGF0YSwgZ2V0TXVsdGlMaW5lQ2hhcnREYXRhLCBnZXRRdWFsaXR5Q29zdERldGFpbCwgZ2V0RXh0ZXJuYWxDb3N0RGV0YWlsLCBnZXRJbnRlcm5hbENvc3REZXRhaWwsIGdldENvbWJvQ2hhcnREZXRhaWwsZ2V0V2F0ZXJmYWxsQ2hhcnREZXRhaWwsZ2V0U2NyYXBMb3NzQ2hhcnREZXRhaWxzRGV0YWlsLGdldFF1YWxpdHlPYmplY3Rpb25Mb3NzRGV0YWlsIH0gZnJvbSAiQC9hcGkvcXVhbGl0eUNvc3QvZGFzaGJvYXJkIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUXVhbGl0eUNvc3REYXNoYm9hcmQnLA0KICBkYXRhKCkgew0KICAgIC8vIOiOt+WPlum7mOiupOS8muiuoeacn++8iOS4iuS4quaciO+8iQ0KICAgIGNvbnN0IGdldERlZmF1bHRZZWFyTW9udGggPSAoKSA9PiB7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgY29uc3QgeWVhciA9IG5vdy5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW9udGggPSBub3cuZ2V0TW9udGgoKSArIDE7IC8vIDEtMTINCiAgICAgIGNvbnN0IGRheSA9IG5vdy5nZXREYXRlKCk7DQogICAgICBjb25zdCBob3VyID0gbm93LmdldEhvdXJzKCk7DQoNCiAgICAgIC8vIOWmguaenOS7iuWkqeaYr+acrOaciDI15Y+3OOeCueWJje+8iOWQqzI15Y+3Nzo1Oe+8ie+8jOWImeeUqOS4iuS4quaciA0KICAgICAgaWYgKGRheSA8IDI4IHx8IChkYXkgPT09IDI4ICYmIGhvdXIgPCAxKSkgew0KICAgICAgICAvLyDlpITnkIYx5pyI5pe255qE6Leo5bm0DQogICAgICAgIGNvbnN0IHByZXZNb250aCA9IG1vbnRoID09PSAxID8gMTIgOiBtb250aCAtIDE7DQogICAgICAgIGNvbnN0IHByZXZZZWFyID0gbW9udGggPT09IDEgPyB5ZWFyIC0gMSA6IHllYXI7DQogICAgICAgIHJldHVybiBgJHtwcmV2WWVhcn0tJHtTdHJpbmcocHJldk1vbnRoKS5wYWRTdGFydCgyLCAnMCcpfWA7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gYCR7eWVhcn0tJHtTdHJpbmcobW9udGgpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICAgIH0NCiAgICB9Ow0KDQogICAgcmV0dXJuIHsNCiAgICAgIHVwZGF0ZVRpbWU6ICcyMDIzLTEwLTI3IDEwOjAwJywNCiAgICAgIGNoYXJ0czoge30sDQogICAgICAvLyDmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ8NCiAgICAgIGNvc3RDZW50ZXI6ICcnLA0KICAgICAgYWNjb3VudGluZ1BlcmlvZDogZ2V0RGVmYXVsdFllYXJNb250aCgpLA0KICAgICAgLy8g6LSo6YeP5oiQ5pys57G75Z6L77yM6buY6K6k5YC85Li6Me+8iOS4jeWQq+WIl+WFpemhue+8iQ0KICAgICAgY29udGFpblR5cGU6IDEsDQogICAgICAvLyDmiJDmnKzkuK3lv4PpgInpobkNCiAgICAgIGNvc3RDZW50ZXJPcHRpb25zOiBbXSwNCiAgICAgIGNvc3RDZW50ZXJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHF1YWxpdHlDb3N0RGV0YWlsOiB7fSwNCiAgICAgIHF1YWxpdHlDb3N0RGF0YToge30NCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgLy8g55uR5ZCs5oiQ5pys5Lit5b+D5Y+Y5YyWDQogICAgY29zdENlbnRlcjogew0KICAgICAgaGFuZGxlcigpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aIkOacrOS4reW/g+WPmOWMljonLCB0aGlzLmNvc3RDZW50ZXIpOw0KICAgICAgICB0aGlzLnJlZnJlc2hDaGFydERhdGEoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOebkeWQrOS8muiuoeacn+WPmOWMlg0KICAgIGFjY291bnRpbmdQZXJpb2Q6IHsNCiAgICAgIGhhbmRsZXIoKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfkvJrorqHmnJ/lj5jljJY6JywgdGhpcy5hY2NvdW50aW5nUGVyaW9kKTsNCiAgICAgICAgdGhpcy5yZWZyZXNoQ2hhcnREYXRhKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDnm5HlkKzotKjph4/miJDmnKznsbvlnovlj5jljJYNCiAgICBjb250YWluVHlwZTogew0KICAgICAgaGFuZGxlcigpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+i0qOmHj+aIkOacrOexu+Wei+WPmOWMljonLCB0aGlzLmNvbnRhaW5UeXBlKTsNCiAgICAgICAgdGhpcy5yZWZyZXNoQ2hhcnREYXRhKCk7DQogICAgICB9DQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuZ2V0Q29zdENlbnRlckxpc3QoKTsNCiAgICAvL+i0qOmHj+aIkOacrOWbm+Wkp+exu+WIq+WNoOavlA0KDQogICAgdGhpcy5pbml0Q2hhcnRzKCk7DQogICAgdGhpcy5yZXNpemVPYnNlcnZlciA9IG5ldyBSZXNpemVPYnNlcnZlcigoKSA9PiB7DQogICAgICB0aGlzLnJlc2l6ZUNoYXJ0cygpDQogICAgfSkNCiAgICB0aGlzLnJlc2l6ZU9ic2VydmVyLm9ic2VydmUodGhpcy4kZWwpDQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnRzKQ0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIC8vIOmUgOavgeaJgOacieWbvuihqOWunuS+iw0KICAgIE9iamVjdC52YWx1ZXModGhpcy5jaGFydHMpLmZvckVhY2goY2hhcnQgPT4gew0KICAgICAgaWYgKGNoYXJ0KSB7DQogICAgICAgIGNoYXJ0LmRpc3Bvc2UoKQ0KICAgICAgfQ0KICAgIH0pDQogICAgaWYgKHRoaXMucmVzaXplT2JzZXJ2ZXIpIHsNCiAgICAgIHRoaXMucmVzaXplT2JzZXJ2ZXIuZGlzY29ubmVjdCgpDQogICAgfQ0KICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnJlc2l6ZUNoYXJ0cykNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOWIpOaWreeZvuWIhuavlOaYr+WQpuS4uui0n+aVsA0KICAgIGlzTmVnYXRpdmVQZXJjZW50YWdlKHBlcmNlbnRhZ2UpIHsNCiAgICAgIGlmICghcGVyY2VudGFnZSkgcmV0dXJuIGZhbHNlOw0KICAgICAgcmV0dXJuIHBlcmNlbnRhZ2UudG9TdHJpbmcoKS5zdGFydHNXaXRoKCctJyk7DQogICAgfSwNCg0KICAgIC8vIOagueaNrueZvuWIhuavlOato+i0n+WAvOi/lOWbnuWvueW6lOeahENTU+exuw0KICAgIGdldFBlcmNlbnRhZ2VDbGFzcyhwZXJjZW50YWdlKSB7DQogICAgICBpZiAoIXBlcmNlbnRhZ2UpIHJldHVybiAnbmV1dHJhbCc7DQogICAgICByZXR1cm4gdGhpcy5pc05lZ2F0aXZlUGVyY2VudGFnZShwZXJjZW50YWdlKSA/ICduZWdhdGl2ZScgOiAncG9zaXRpdmUnOw0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbmlbDlrZfvvIzmnIDlpJrkv53nlZnkuKTkvY3lsI/mlbANCiAgICBmb3JtYXROdW1iZXIobnVtKSB7DQogICAgICBpZiAobnVtID09PSBudWxsIHx8IG51bSA9PT0gdW5kZWZpbmVkIHx8IG51bSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuICcwJzsNCiAgICAgIH0NCiAgICAgIGNvbnN0IG51bWJlciA9IE51bWJlcihudW0pOw0KICAgICAgaWYgKGlzTmFOKG51bWJlcikpIHsNCiAgICAgICAgcmV0dXJuICcwJzsNCiAgICAgIH0NCiAgICAgIC8vIOS9v+eUqHRvRml4ZWQoMinkv53nlZnkuKTkvY3lsI/mlbDvvIznhLblkI7nlKhwYXJzZUZsb2F05Y675o6J5pyr5bC+55qEMA0KICAgICAgcmV0dXJuIHBhcnNlRmxvYXQobnVtYmVyLnRvRml4ZWQoMikpLnRvU3RyaW5nKCk7DQogICAgfSwNCg0KICAgIC8vIOa3u+WKoOWNg+WIhuS9jeWIhumalOespg0KICAgIGFkZFRob3VzYW5kU2VwYXJhdG9yKG51bSkgew0KICAgICAgcmV0dXJuIG51bS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMluS6p+mHjy/plIDph4/kuLrkuIflkKjljZXkvY0NCiAgICBmb3JtYXRUb25uYWdlKG51bSkgew0KICAgICAgaWYgKG51bSA9PT0gbnVsbCB8fCBudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09ICcnKSB7DQogICAgICAgIHJldHVybiAnMOS4h+WQqCc7DQogICAgICB9DQogICAgICBjb25zdCBudW1iZXIgPSBOdW1iZXIobnVtKTsNCiAgICAgIGlmIChpc05hTihudW1iZXIpKSB7DQogICAgICAgIHJldHVybiAnMOS4h+WQqCc7DQogICAgICB9DQogICAgICAvLyDovazmjaLkuLrkuIflkKjlubbkv53nlZnkuKTkvY3lsI/mlbDvvIzmt7vliqDljYPliIbkvY3liIbpmpTnrKYNCiAgICAgIGNvbnN0IHJlc3VsdCA9IChudW1iZXIgLyAxMDAwMCkudG9GaXhlZCgyKTsNCiAgICAgIHJldHVybiBgJHt0aGlzLmFkZFRob3VzYW5kU2VwYXJhdG9yKHJlc3VsdCl95LiH5ZCoYDsNCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW5oC76YeR6aKd5Li65LiH5YWD5Y2V5L2NDQogICAgZm9ybWF0QW1vdW50KG51bSkgew0KICAgICAgaWYgKG51bSA9PT0gbnVsbCB8fCBudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09ICcnKSB7DQogICAgICAgIHJldHVybiAnMOS4h+WFgyc7DQogICAgICB9DQogICAgICBjb25zdCBudW1iZXIgPSBOdW1iZXIobnVtKTsNCiAgICAgIGlmIChpc05hTihudW1iZXIpKSB7DQogICAgICAgIHJldHVybiAnMOS4h+WFgyc7DQogICAgICB9DQogICAgICAvLyDovazmjaLkuLrkuIflhYPlubbkv53nlZnkuKTkvY3lsI/mlbDvvIzmt7vliqDljYPliIbkvY3liIbpmpTnrKYNCiAgICAgIGNvbnN0IHJlc3VsdCA9IChudW1iZXIgLyAxMDAwMCkudG9GaXhlZCgyKTsNCiAgICAgIHJldHVybiBgJHt0aGlzLmFkZFRob3VzYW5kU2VwYXJhdG9yKHJlc3VsdCl95LiH5YWDYDsNCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW5ZCo6ZKi5oiQ5pys5Li65YWDL+WQqOWNleS9jQ0KICAgIGZvcm1hdFVuaXRDb3N0KG51bSkgew0KICAgICAgaWYgKG51bSA9PT0gbnVsbCB8fCBudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09ICcnKSB7DQogICAgICAgIHJldHVybiAnMOWFgy/lkKgnOw0KICAgICAgfQ0KICAgICAgY29uc3QgbnVtYmVyID0gTnVtYmVyKG51bSk7DQogICAgICBpZiAoaXNOYU4obnVtYmVyKSkgew0KICAgICAgICByZXR1cm4gJzDlhYMv5ZCoJzsNCiAgICAgIH0NCiAgICAgIC8vIOS/neeVmeS4pOS9jeWwj+aVsOW5tua3u+WKoOWNleS9je+8jOa3u+WKoOWNg+WIhuS9jeWIhumalOespg0KICAgICAgY29uc3QgcmVzdWx0ID0gbnVtYmVyLnRvRml4ZWQoMik7DQogICAgICByZXR1cm4gYCR7dGhpcy5hZGRUaG91c2FuZFNlcGFyYXRvcihyZXN1bHQpfeWFgy/lkKhgOw0KICAgIH0sDQoNCg0KICAgIGdldFdhdGVyZmFsbENoYXJ0RGV0YWlsKCkgew0KICAgICAgLy8g5Y+q5pyJ5b2T5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyf6YO95pyJ5YC85pe25omN6K+35rGCDQogICAgICBpZiAoIXRoaXMuY29zdENlbnRlciB8fCAhdGhpcy5hY2NvdW50aW5nUGVyaW9kIHx8ICh0aGlzLmNvbnRhaW5UeXBlICE9PSAyICYmIHRoaXMuY29udGFpblR5cGUgIT09IDEpKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmiJDmnKzkuK3lv4PjgIHkvJrorqHmnJ/miJbotKjph4/miJDmnKznsbvlnovkuLrnqbrvvIzot7Pov4fmlbDmja7or7fmsYInKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIGNvc3RDZW50ZXI6IHRoaXMuY29zdENlbnRlciwNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLmFjY291bnRpbmdQZXJpb2QucmVwbGFjZSgnLScsICcnKSwgLy8g5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgICBjb250YWluVHlwZTogdGhpcy5jb250YWluVHlwZQ0KICAgICAgfTsNCg0KICAgICAgZ2V0V2F0ZXJmYWxsQ2hhcnREZXRhaWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ2dldFdhdGVyZmFsbENoYXJ0RGV0YWlsOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICAvLyDmm7TmlrBXYXRlcmZhbGxDaGFydOafseeKtuWbvg0KICAgICAgICAgIHRoaXMudXBkYXRlV2F0ZXJmYWxsQ2hhcnQocmVzcG9uc2UuZGF0YSk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+WV2F0ZXJmYWxsQ2hhcnTmlbDmja7lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5ZXYXRlcmZhbGxDaGFydOaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOabtOaWsFdhdGVyZmFsbENoYXJ05p+x54q25Zu+DQogICAgdXBkYXRlV2F0ZXJmYWxsQ2hhcnQoZGF0YSkgew0KICAgICAgaWYgKHRoaXMuY2hhcnRzLndhdGVyZmFsbENoYXJ0ICYmIGRhdGEpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aOpeaUtuWIsOeahFdhdGVyZmFsbENoYXJ05pWw5o2uOicsIGRhdGEpOw0KDQogICAgICAgIC8vIOWkhOeQhnJlc2N1ZVByb2plY3TmlbDmja4NCiAgICAgICAgY29uc3QgeEF4aXNEYXRhID0gW107ICAgICAgLy8geOi9tOe7tOW6puaVsOaNrg0KICAgICAgICBjb25zdCBzZXJpZXNEYXRhID0gW107ICAgICAvLyDmn7Hnirblm77mlbDmja4NCiAgICAgICAgY29uc3QgY29sb3JzID0gWycjOTNDNUZEJywgJyM4NkVGQUMnLCAnI0ZERTY4QScsICcjRkNBNUE1JywgJyNDNEI1RkQnLCAnI0YzRThGRiddOw0KDQogICAgICAgIGxldCBkYXRhSXRlbXMgPSBbXTsNCg0KICAgICAgICBpZiAoZGF0YS5yZXNjdWVQcm9qZWN0KSB7DQogICAgICAgICAgLy8g5bCGcmVzY3VlUHJvamVjdOWvueixoei9rOaNouS4uuaVsOe7hO+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICAgIGRhdGFJdGVtcyA9IE9iamVjdC5lbnRyaWVzKGRhdGEucmVzY3VlUHJvamVjdCkubWFwKChba2V5LCB2YWx1ZV0pID0+ICh7DQogICAgICAgICAgICBuYW1lOiBrZXksICAgIC8vIOesrOS4gOmhueS4uue7tOW6puWQjeensA0KICAgICAgICAgICAgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpICAvLyDnrKzkuozpobnkuLrlr7nlupTnu7TluqbnmoTlgLzvvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygn5aSE55CG5ZCO55qE5pWw5o2u6aG5OicsIGRhdGFJdGVtcyk7DQoNCiAgICAgICAgaWYgKGRhdGFJdGVtcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g5oyJ5pWw5YC85LuO6auY5Yiw5L2O5o6S5bqPDQogICAgICAgICAgZGF0YUl0ZW1zLnNvcnQoKGEsIGIpID0+IGIudmFsdWUgLSBhLnZhbHVlKTsNCg0KICAgICAgICAgIC8vIOWPquWPluWJjeWNgeS4quacgOWkp+eahOaVsOaNrg0KICAgICAgICAgIGNvbnN0IHRvcFRlbkl0ZW1zID0gZGF0YUl0ZW1zLnNsaWNlKDAsIDEwKTsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5Y+W5YmN5Y2B5Liq5pyA5aSn5pWw5o2uOicsIHRvcFRlbkl0ZW1zKTsNCg0KICAgICAgICAgIC8vIOWIhuemu+aOkuW6j+WQjueahOaVsOaNrg0KICAgICAgICAgIHRvcFRlbkl0ZW1zLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgICB4QXhpc0RhdGEucHVzaChpdGVtLm5hbWUpOw0KICAgICAgICAgICAgc2VyaWVzRGF0YS5wdXNoKHsNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUsDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzW2luZGV4ICUgY29sb3JzLmxlbmd0aF0gfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCfmsqHmnInmib7liLDmnInmlYjnmoTmlbDmja7pobknKTsNCiAgICAgICAgICAvLyDmt7vliqDpu5jorqTmlbDmja7ku6Xkvr/mtYvor5UNCiAgICAgICAgICB4QXhpc0RhdGEucHVzaCgn5peg5pWw5o2uJyk7DQogICAgICAgICAgc2VyaWVzRGF0YS5wdXNoKHsNCiAgICAgICAgICAgIHZhbHVlOiAwLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbMF0gfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ3jovbTnu7TluqbmlbDmja46JywgeEF4aXNEYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+afseeKtuWbvuaVsOaNrjonLCBzZXJpZXNEYXRhKTsNCg0KICAgICAgICAvLyDmm7TmlrDlm77ooajphY3nva4NCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIGdyaWQ6IHsgbGVmdDogJzglJywgcmlnaHQ6ICc0JScsIGJvdHRvbTogJzMlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgICAgYXhpc1BvaW50ZXI6IHsgdHlwZTogJ3NoYWRvdycgfSwNCiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI2ZmZicgfSwNCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgew0KICAgICAgICAgICAgICBsZXQgcmVzdWx0ID0gcGFyYW1zWzBdLm5hbWUgKyAnPGJyLz4nOw0KICAgICAgICAgICAgICBwYXJhbXMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFZhbHVlID0gcGFyc2VGbG9hdChpdGVtLnZhbHVlKS50b0ZpeGVkKDIpLnRvU3RyaW5nKCkucmVwbGFjZSgvXEIoPz0oXGR7M30pKyg/IVxkKSkvZywgJywnKTsNCiAgICAgICAgICAgICAgICByZXN1bHQgKz0gaXRlbS5tYXJrZXIgKyAnICcgKyBpdGVtLnNlcmllc05hbWUgKyAnOiAnICsgZm9ybWF0dGVkVmFsdWUgKyAn5LiH5YWDPGJyLz4nOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgZGF0YTogeEF4aXNEYXRhLA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICAgIGludGVydmFsOiAwLCAvLyDmmL7npLrmiYDmnInmoIfnrb4NCiAgICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgICBuYW1lOiAn6YeR6aKdICjkuIflhYMpJywNCiAgICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICAgIHNwbGl0TGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjMzc0MTUxJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICAgIG5hbWU6ICfmjL3mlZHlpITnkIbmiJDmnKwnLA0KICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICBkYXRhOiBzZXJpZXNEYXRhDQogICAgICAgICAgfV0NCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLmNoYXJ0cy53YXRlcmZhbGxDaGFydC5zZXRPcHRpb24ob3B0aW9uLCB0cnVlKTsgLy8g5L2/55SodHJ1ZeW8uuWItuWIt+aWsA0KICAgICAgICBjb25zb2xlLmxvZygnV2F0ZXJmYWxsQ2hhcnTmn7Hnirblm77mlbDmja7lt7Lmm7TmlrAnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ1dhdGVyZmFsbENoYXJ05a6e5L6L5LiN5a2Y5Zyo5oiW5pWw5o2u5Li656m6Jyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOabtOaWsFNjcmFwTG9zc0NoYXJ05p+x54q25Zu+DQogICAgdXBkYXRlU2NyYXBMb3NzQ2hhcnQoZGF0YSkgew0KICAgICAgaWYgKHRoaXMuY2hhcnRzLnNjcmFwTG9zc0NoYXJ0ICYmIGRhdGEpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aOpeaUtuWIsOeahFNjcmFwTG9zc0NoYXJ05pWw5o2uOicsIGRhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygn5pWw5o2u57G75Z6LOicsIHR5cGVvZiBkYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNrumUrjonLCBPYmplY3Qua2V5cyhkYXRhKSk7DQoNCiAgICAgICAgLy8g5aSE55CG5oql5bqf5o2f5aSx5pWw5o2u77yM5bCd6K+V5aSa56eN5Y+v6IO955qE5pWw5o2u57uT5p6EDQogICAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IFtdOyAgICAgIC8vIHjovbTnu7TluqbmlbDmja4NCiAgICAgICAgY29uc3Qgc2VyaWVzRGF0YSA9IFtdOyAgICAgLy8g5p+x54q25Zu+5pWw5o2uDQogICAgICAgIGNvbnN0IGNvbG9ycyA9IFsnIzkzQzVGRCcsICcjODZFRkFDJywgJyNGREU2OEEnLCAnI0ZDQTVBNScsICcjQzRCNUZEJywgJyNGM0U4RkYnXTsNCiAgICAgICAgbGV0IGRhdGFJdGVtcyA9IFtdOw0KDQogICAgICAgIC8vIOWwneivleS4jeWQjOeahOaVsOaNrue7k+aehO+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICBpZiAoZGF0YS5zY3JhcExvc3NNYXApIHsNCiAgICAgICAgICAvLyDmg4XlhrUxOiDkvb/nlKhzY3JhcExvc3NNYXDmlbDmja7vvIjmoLnmja7lrp7pmYVBUEnov5Tlm57nmoTmlbDmja7nu5PmnoTvvIkNCiAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55Soc2NyYXBMb3NzTWFw5pWw5o2uJyk7DQogICAgICAgICAgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YS5zY3JhcExvc3NNYXApLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoew0KICAgICAgICAgICAgbmFtZToga2V5LA0KICAgICAgICAgICAgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpDQogICAgICAgICAgfSkpOw0KICAgICAgICB9IGVsc2UgaWYgKGRhdGEucmVzY3VlUHJvamVjdCkgew0KICAgICAgICAgIC8vIOaDheWGtTI6IOS9v+eUqHJlc2N1ZVByb2plY3TmlbDmja7vvIjkuI5XYXRlcmZhbGxDaGFydOebuOWQjO+8iQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCfkvb/nlKhyZXNjdWVQcm9qZWN05pWw5o2uJyk7DQogICAgICAgICAgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YS5yZXNjdWVQcm9qZWN0KS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGtleSwNCiAgICAgICAgICAgIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKQ0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfSBlbHNlIGlmIChkYXRhLnNjcmFwTG9zcykgew0KICAgICAgICAgIC8vIOaDheWGtTM6IOS9v+eUqHNjcmFwTG9zc+aVsOaNrg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfkvb/nlKhzY3JhcExvc3PmlbDmja4nKTsNCiAgICAgICAgICBkYXRhSXRlbXMgPSBPYmplY3QuZW50cmllcyhkYXRhLnNjcmFwTG9zcykubWFwKChba2V5LCB2YWx1ZV0pID0+ICh7DQogICAgICAgICAgICBuYW1lOiBrZXksDQogICAgICAgICAgICB2YWx1ZTogKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMikNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0gZWxzZSBpZiAoZGF0YS5zY3JhcExvc3NQcm9qZWN0KSB7DQogICAgICAgICAgLy8g5oOF5Ya1NDog5L2/55Soc2NyYXBMb3NzUHJvamVjdOaVsOaNrg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfkvb/nlKhzY3JhcExvc3NQcm9qZWN05pWw5o2uJyk7DQogICAgICAgICAgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YS5zY3JhcExvc3NQcm9qZWN0KS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGtleSwNCiAgICAgICAgICAgIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKQ0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmg4XlhrU1OiDnm7TmjqXkvb/nlKhkYXRh5L2c5Li65a+56LGhDQogICAgICAgICAgY29uc29sZS5sb2coJ+ebtOaOpeS9v+eUqGRhdGHlr7nosaEnKTsNCiAgICAgICAgICBkYXRhSXRlbXMgPSBPYmplY3QuZW50cmllcyhkYXRhKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGtleSwNCiAgICAgICAgICAgIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKQ0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCflpITnkIblkI7nmoTmlbDmja7pobk6JywgZGF0YUl0ZW1zKTsNCg0KICAgICAgICBpZiAoZGF0YUl0ZW1zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAvLyDmjInmlbDlgLzku47pq5jliLDkvY7mjpLluo8NCiAgICAgICAgICBkYXRhSXRlbXMuc29ydCgoYSwgYikgPT4gYi52YWx1ZSAtIGEudmFsdWUpOw0KDQogICAgICAgICAgLy8g5Y+q5Y+W5YmN5Y2B5Liq5pyA5aSn55qE5pWw5o2uDQogICAgICAgICAgY29uc3QgdG9wVGVuSXRlbXMgPSBkYXRhSXRlbXMuc2xpY2UoMCwgMTApOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCflj5bliY3ljYHkuKrmnIDlpKfmlbDmja46JywgdG9wVGVuSXRlbXMpOw0KDQogICAgICAgICAgLy8g5YiG56a75o6S5bqP5ZCO55qE5pWw5o2uDQogICAgICAgICAgdG9wVGVuSXRlbXMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKGl0ZW0ubmFtZSk7DQogICAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXSB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oJ+ayoeacieaJvuWIsOacieaViOeahOaVsOaNrumhuScpOw0KICAgICAgICAgIC8vIOa3u+WKoOm7mOiupOaVsOaNruS7peS+v+a1i+ivlQ0KICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKCfml6DmlbDmja4nKTsNCiAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgdmFsdWU6IDAsDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1swXSB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygneOi9tOe7tOW6puaVsOaNrjonLCB4QXhpc0RhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygn5p+x54q25Zu+5pWw5o2uOicsIHNlcmllc0RhdGEpOw0KDQogICAgICAgIC8vIOabtOaWsOWbvuihqOmFjee9rg0KICAgICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgICAgZ3JpZDogeyBsZWZ0OiAnOCUnLCByaWdodDogJzQlJywgYm90dG9tOiAnMyUnLCBjb250YWluTGFiZWw6IHRydWUgfSwNCiAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzkzQzVGRCcsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW1zKSB7DQogICAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPic7DQogICAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgY29uc3QgZm9ybWF0dGVkVmFsdWUgPSBwYXJzZUZsb2F0KGl0ZW0udmFsdWUpLnRvRml4ZWQoMikudG9TdHJpbmcoKS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAnLCcpOw0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSBpdGVtLm1hcmtlciArICcgJyArIGl0ZW0uc2VyaWVzTmFtZSArICc6ICcgKyBmb3JtYXR0ZWRWYWx1ZSArICfkuIflhYM8YnIvPic7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgICBkYXRhOiB4QXhpc0RhdGEsDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB5QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6ICfph5Hpop0gKOS4h+WFgyknLA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9LA0KICAgICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgICAgbmFtZTogJ+aKpeW6n+aNn+WkseaIkOacrCcsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGRhdGE6IHNlcmllc0RhdGENCiAgICAgICAgICB9XQ0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY2hhcnRzLnNjcmFwTG9zc0NoYXJ0LnNldE9wdGlvbihvcHRpb24sIHRydWUpOyAvLyDkvb/nlKh0cnVl5by65Yi25Yi35pawDQogICAgICAgIGNvbnNvbGUubG9nKCdTY3JhcExvc3NDaGFydOafseeKtuWbvuaVsOaNruW3suabtOaWsCcpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignU2NyYXBMb3NzQ2hhcnTlrp7kvovkuI3lrZjlnKjmiJbmlbDmja7kuLrnqbonKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5pu05pawUXVhbGl0eU9iamVjdGlvbkNoYXJ05p+x54q25Zu+DQogICAgdXBkYXRlUXVhbGl0eU9iamVjdGlvbkNoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5xdWFsaXR5T2JqZWN0aW9uQ2hhcnQgJiYgZGF0YSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5o6l5pS25Yiw55qEUXVhbGl0eU9iamVjdGlvbkNoYXJ05pWw5o2uOicsIGRhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygn5pWw5o2u57G75Z6LOicsIHR5cGVvZiBkYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNrumUrjonLCBPYmplY3Qua2V5cyhkYXRhKSk7DQoNCiAgICAgICAgLy8g5aSE55CG6LSo6YeP5byC6K6u5o2f5aSx5pWw5o2u77yM5bCd6K+V5aSa56eN5Y+v6IO955qE5pWw5o2u57uT5p6EDQogICAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IFtdOyAgICAgIC8vIHjovbTnu7TluqbmlbDmja4NCiAgICAgICAgY29uc3Qgc2VyaWVzRGF0YSA9IFtdOyAgICAgLy8g5p+x54q25Zu+5pWw5o2uDQogICAgICAgIGNvbnN0IGNvbG9ycyA9IFsnIzkzQzVGRCcsICcjODZFRkFDJywgJyNGREU2OEEnLCAnI0ZDQTVBNScsICcjQzRCNUZEJywgJyNGM0U4RkYnXTsNCiAgICAgICAgbGV0IGRhdGFJdGVtcyA9IFtdOw0KDQogICAgICAgIC8vIOWwneivleS4jeWQjOeahOaVsOaNrue7k+aehO+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICBpZiAoZGF0YS5xdWFsaXR5T2JqZWN0aW9uTG9zc01hcCkgew0KICAgICAgICAgIC8vIOaDheWGtTE6IOS9v+eUqHF1YWxpdHlPYmplY3Rpb25Mb3NzTWFw5pWw5o2uDQogICAgICAgICAgY29uc29sZS5sb2coJ+S9v+eUqHF1YWxpdHlPYmplY3Rpb25Mb3NzTWFw5pWw5o2uJyk7DQogICAgICAgICAgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YS5xdWFsaXR5T2JqZWN0aW9uTG9zc01hcCkubWFwKChba2V5LCB2YWx1ZV0pID0+ICh7DQogICAgICAgICAgICBuYW1lOiBrZXksDQogICAgICAgICAgICB2YWx1ZTogKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMikNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0gZWxzZSBpZiAoZGF0YS5yZXNjdWVQcm9qZWN0KSB7DQogICAgICAgICAgLy8g5oOF5Ya1Mjog5L2/55SocmVzY3VlUHJvamVjdOaVsOaNru+8iOS4jldhdGVyZmFsbENoYXJ055u45ZCM77yJDQogICAgICAgICAgY29uc29sZS5sb2coJ+S9v+eUqHJlc2N1ZVByb2plY3TmlbDmja4nKTsNCiAgICAgICAgICBkYXRhSXRlbXMgPSBPYmplY3QuZW50cmllcyhkYXRhLnJlc2N1ZVByb2plY3QpLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoew0KICAgICAgICAgICAgbmFtZToga2V5LA0KICAgICAgICAgICAgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpDQogICAgICAgICAgfSkpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOaDheWGtTM6IOebtOaOpeS9v+eUqGRhdGHkvZzkuLrlr7nosaENCiAgICAgICAgICBjb25zb2xlLmxvZygn55u05o6l5L2/55SoZGF0YeWvueixoScpOw0KICAgICAgICAgIGRhdGFJdGVtcyA9IE9iamVjdC5lbnRyaWVzKGRhdGEpLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoew0KICAgICAgICAgICAgbmFtZToga2V5LA0KICAgICAgICAgICAgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpDQogICAgICAgICAgfSkpOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ+WkhOeQhuWQjueahOaVsOaNrumhuTonLCBkYXRhSXRlbXMpOw0KDQogICAgICAgIGlmIChkYXRhSXRlbXMubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOaMieaVsOWAvOS7jumrmOWIsOS9juaOkuW6jw0KICAgICAgICAgIGRhdGFJdGVtcy5zb3J0KChhLCBiKSA9PiBiLnZhbHVlIC0gYS52YWx1ZSk7DQoNCiAgICAgICAgICAvLyDlj6rlj5bliY3ljYHkuKrmnIDlpKfnmoTmlbDmja4NCiAgICAgICAgICBjb25zdCB0b3BUZW5JdGVtcyA9IGRhdGFJdGVtcy5zbGljZSgwLCAxMCk7DQogICAgICAgICAgY29uc29sZS5sb2coJ+WPluWJjeWNgeS4quacgOWkp+aVsOaNrjonLCB0b3BUZW5JdGVtcyk7DQoNCiAgICAgICAgICAvLyDliIbnprvmjpLluo/lkI7nmoTmlbDmja4NCiAgICAgICAgICB0b3BUZW5JdGVtcy5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgeEF4aXNEYXRhLnB1c2goaXRlbS5uYW1lKTsNCiAgICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLnZhbHVlLA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1tpbmRleCAlIGNvbG9ycy5sZW5ndGhdIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUud2Fybign5rKh5pyJ5om+5Yiw5pyJ5pWI55qE5pWw5o2u6aG5Jyk7DQogICAgICAgICAgLy8g5re75Yqg6buY6K6k5pWw5o2u5Lul5L6/5rWL6K+VDQogICAgICAgICAgeEF4aXNEYXRhLnB1c2goJ+aXoOaVsOaNricpOw0KICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICB2YWx1ZTogMCwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzWzBdIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCd46L2057u05bqm5pWw5o2uOicsIHhBeGlzRGF0YSk7DQogICAgICAgIGNvbnNvbGUubG9nKCfmn7Hnirblm77mlbDmja46Jywgc2VyaWVzRGF0YSk7DQoNCiAgICAgICAgLy8g5pu05paw5Zu+6KGo6YWN572uDQogICAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgICBncmlkOiB7IGxlZnQ6ICc4JScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICAgIGF4aXNQb2ludGVyOiB7IHR5cGU6ICdzaGFkb3cnIH0sDQogICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDMwLCA0MSwgNTksIDAuOSknLA0KICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0sDQogICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIChwYXJhbXMpIHsNCiAgICAgICAgICAgICAgbGV0IHJlc3VsdCA9IHBhcmFtc1swXS5uYW1lICsgJzxici8+JzsNCiAgICAgICAgICAgICAgcGFyYW1zLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRWYWx1ZSA9IHBhcnNlRmxvYXQoaXRlbS52YWx1ZSkudG9GaXhlZCgyKS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgICAgICAgICAgICAgcmVzdWx0ICs9IGl0ZW0ubWFya2VyICsgJyAnICsgaXRlbS5zZXJpZXNOYW1lICsgJzogJyArIGZvcm1hdHRlZFZhbHVlICsgJ+S4h+WFgzxici8+JzsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB4QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGRhdGE6IHhBeGlzRGF0YSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgICBpbnRlcnZhbDogMCwgLy8g5pi+56S65omA5pyJ5qCH562+DQogICAgICAgICAgICAgIHJvdGF0ZTogMCwgLy8g5rC05bmz5pi+56S65qCH562+DQogICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+mHkeminSAo5LiH5YWDKScsDQogICAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgICBuYW1lOiAn6LSo6YeP5byC6K6u5o2f5aSx5oiQ5pysJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgZGF0YTogc2VyaWVzRGF0YQ0KICAgICAgICAgIH1dDQogICAgICAgIH07DQoNCiAgICAgICAgdGhpcy5jaGFydHMucXVhbGl0eU9iamVjdGlvbkNoYXJ0LnNldE9wdGlvbihvcHRpb24sIHRydWUpOyAvLyDkvb/nlKh0cnVl5by65Yi25Yi35pawDQogICAgICAgIGNvbnNvbGUubG9nKCdRdWFsaXR5T2JqZWN0aW9uQ2hhcnTmn7Hnirblm77mlbDmja7lt7Lmm7TmlrAnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ1F1YWxpdHlPYmplY3Rpb25DaGFydOWunuS+i+S4jeWtmOWcqOaIluaVsOaNruS4uuepuicpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBnZXRRdWFsaXR5T2JqZWN0aW9uTG9zc0RldGFpbCgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldFF1YWxpdHlPYmplY3Rpb25Mb3NzRGV0YWlsKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdnZXRRdWFsaXR5T2JqZWN0aW9uTG9zc0RldGFpbDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5pu05pawUXVhbGl0eU9iamVjdGlvbkNoYXJ05p+x54q25Zu+DQogICAgICAgICAgdGhpcy51cGRhdGVRdWFsaXR5T2JqZWN0aW9uQ2hhcnQocmVzcG9uc2UuZGF0YSk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+WUXVhbGl0eU9iamVjdGlvbkNoYXJ05pWw5o2u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5Lqn5ZOB6LSo6YeP5byC6K6u5o2f5aSx5piO57uG5pWw5o2u5aSx6LSlJyk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgZ2V0U2NyYXBMb3NzQ2hhcnREZXRhaWxzRGV0YWlsKCkgew0KICAgICAgLy8g5Y+q5pyJ5b2T5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyf6YO95pyJ5YC85pe25omN6K+35rGCDQogICAgICBpZiAoIXRoaXMuY29zdENlbnRlciB8fCAhdGhpcy5hY2NvdW50aW5nUGVyaW9kIHx8ICh0aGlzLmNvbnRhaW5UeXBlICE9PSAyICYmIHRoaXMuY29udGFpblR5cGUgIT09IDEpKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmiJDmnKzkuK3lv4PjgIHkvJrorqHmnJ/miJbotKjph4/miJDmnKznsbvlnovkuLrnqbrvvIzot7Pov4fmlbDmja7or7fmsYInKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIGNvc3RDZW50ZXI6IHRoaXMuY29zdENlbnRlciwNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLmFjY291bnRpbmdQZXJpb2QucmVwbGFjZSgnLScsICcnKSwgLy8g5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgICBjb250YWluVHlwZTogdGhpcy5jb250YWluVHlwZQ0KICAgICAgfTsNCg0KICAgICAgZ2V0U2NyYXBMb3NzQ2hhcnREZXRhaWxzRGV0YWlsKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdnZXRTY3JhcExvc3NDaGFydERldGFpbHNEZXRhaWw6JywgcmVzcG9uc2UpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIC8vIOabtOaWsFNjcmFwTG9zc0NoYXJ05p+x54q25Zu+DQogICAgICAgICAgdGhpcy51cGRhdGVTY3JhcExvc3NDaGFydChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5ZTY3JhcExvc3NDaGFydOaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluS6p+WTgeaKpeW6n+aNn+WkseaYjue7huaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldEV4dGVybmFsQ29zdERldGFpbCgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldEV4dGVybmFsQ29zdERldGFpbChwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnZ2V0RXh0ZXJuYWxDb3N0RGV0YWlsOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICAvLyDmm7TmlrDlpJbpg6jmjZ/lpLHmiJDmnKzmnoTmiJDlm77ooagNCiAgICAgICAgICB0aGlzLnVwZGF0ZUV4dGVybmFsQ29zdERldGFpbENoYXJ0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIC8vIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWklumDqOaNn+WkseaIkOacrOaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWklumDqOaNn+WkseaIkOacrOaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldEludGVybmFsQ29zdERldGFpbCgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldEludGVybmFsQ29zdERldGFpbChwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnZ2V0SW50ZXJuYWxDb3N0RGV0YWlsOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICAvLyDmm7TmlrDlhoXpg6jmjZ/lpLHmiJDmnKzmnoTmiJDlm77ooagNCiAgICAgICAgICB0aGlzLnVwZGF0ZUludGVybmFsQ29zdERldGFpbENoYXJ0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWGhemDqOaNn+WkseaIkOacrOaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOabtOaWsOWGhemDqOaNn+WkseaIkOacrOaehOaIkOWbvuihqA0KICAgIHVwZGF0ZUludGVybmFsQ29zdERldGFpbENoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5pbnRlcm5hbENvc3REZXRhaWxDaGFydCAmJiBkYXRhKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmjqXmlLbliLDnmoTlhoXpg6jmjZ/lpLHmiJDmnKzmlbDmja46JywgZGF0YSk7DQoNCiAgICAgICAgLy8g5pS26ZuG5omA5pyJ5pWw5o2u6aG5DQogICAgICAgIGNvbnN0IGFsbERhdGFJdGVtcyA9IFtdOw0KICAgICAgICBjb25zdCBjb2xvcnMgPSBbJyM5M0M1RkQnLCAnIzg2RUZBQycsICcjRkRFNjhBJywgJyNGQ0E1QTUnLCAnI0M0QjVGRCddOw0KDQogICAgICAgIC8vIOWkhOeQhuWQhOS4quaIkOacrOmhue+8jOaUtumbhuWIsOe7n+S4gOaVsOe7hOS4re+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICBpZiAoZGF0YS5jb250cmFjdGlvbkxvc3MpIHsNCiAgICAgICAgICBPYmplY3QuZW50cmllcyhkYXRhLmNvbnRyYWN0aW9uTG9zcykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7DQogICAgICAgICAgICAvLyDnoa7kv53mlbDlgLzovazmjaLvvIzljIXmi6ww5YC85Lmf6KaB5pi+56S677yM6L2s5o2i5Li65LiH5YWDDQogICAgICAgICAgICBjb25zdCBudW1WYWx1ZSA9ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgYWxsRGF0YUl0ZW1zLnB1c2goeyBuYW1lOiBrZXksIHZhbHVlOiBudW1WYWx1ZSB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGlmIChkYXRhLnJlc2N1ZUNvc3QpIHsNCiAgICAgICAgICBPYmplY3QuZW50cmllcyhkYXRhLnJlc2N1ZUNvc3QpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4gew0KICAgICAgICAgICAgLy8g56Gu5L+d5pWw5YC86L2s5o2i77yM5YyF5ousMOWAvOS5n+imgeaYvuekuu+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICAgICAgY29uc3QgbnVtVmFsdWUgPSAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgIGFsbERhdGFJdGVtcy5wdXNoKHsgbmFtZToga2V5LCB2YWx1ZTogbnVtVmFsdWUgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAoZGF0YS5yZXZpc2lvbkxvc3MpIHsNCiAgICAgICAgICBPYmplY3QuZW50cmllcyhkYXRhLnJldmlzaW9uTG9zcykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7DQogICAgICAgICAgICAvLyDnoa7kv53mlbDlgLzovazmjaLvvIzljIXmi6ww5YC85Lmf6KaB5pi+56S677yM6L2s5o2i5Li65LiH5YWDDQogICAgICAgICAgICBjb25zdCBudW1WYWx1ZSA9ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgYWxsRGF0YUl0ZW1zLnB1c2goeyBuYW1lOiBrZXksIHZhbHVlOiBudW1WYWx1ZSB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGlmIChkYXRhLnNjcmFwTG9zcykgew0KICAgICAgICAgIE9iamVjdC5lbnRyaWVzKGRhdGEuc2NyYXBMb3NzKS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHsNCiAgICAgICAgICAgIC8vIOehruS/neaVsOWAvOi9rOaNou+8jOWMheaLrDDlgLzkuZ/opoHmmL7npLrvvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgICAgIGNvbnN0IG51bVZhbHVlID0gKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMik7DQogICAgICAgICAgICBhbGxEYXRhSXRlbXMucHVzaCh7IG5hbWU6IGtleSwgdmFsdWU6IG51bVZhbHVlIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ+aUtumbhuWIsOeahOaJgOacieaVsOaNrumhue+8iOWMheWQqzDlgLzvvIk6JywgYWxsRGF0YUl0ZW1zKTsNCg0KICAgICAgICAvLyDmjInmlbDlgLzku47pq5jliLDkvY7mjpLluo/vvIgw5YC85Lya5o6S5Zyo6LSf5YC85LmL5YmN77yM5q2j5YC85LmL5ZCO77yJDQogICAgICAgIGFsbERhdGFJdGVtcy5zb3J0KChhLCBiKSA9PiBiLnZhbHVlIC0gYS52YWx1ZSk7DQoNCiAgICAgICAgY29uc29sZS5sb2coJ+aOkuW6j+WQjueahOaVsOaNru+8iOWMheWQqzDlgLzvvIk6JywgYWxsRGF0YUl0ZW1zKTsNCg0KICAgICAgICAvLyDliIbnprvmjpLluo/lkI7nmoTmlbDmja7vvIzlj43ovazpobrluo/kvb/ph5Hpop3lpKfnmoTmmL7npLrlnKjkuIrpnaINCiAgICAgICAgY29uc3QgeUF4aXNEYXRhID0gW107DQogICAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSBbXTsNCg0KICAgICAgICAvLyDlj43ovazmlbDnu4TvvIzkvb/ph5Hpop3lpKfnmoTmmL7npLrlnKjlm77ooajkuIrmlrkNCiAgICAgICAgYWxsRGF0YUl0ZW1zLnJldmVyc2UoKS5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgIHlBeGlzRGF0YS5wdXNoKGl0ZW0ubmFtZSk7DQogICAgICAgICAgc2VyaWVzRGF0YS5wdXNoKHsNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLnZhbHVlLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXSB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KDQogICAgICAgIGNvbnNvbGUubG9nKCd56L205pWw5o2uOicsIHlBeGlzRGF0YSk7DQogICAgICAgIGNvbnNvbGUubG9nKCfns7vliJfmlbDmja7vvIjljIXlkKsw5YC877yJOicsIHNlcmllc0RhdGEpOw0KDQogICAgICAgIC8vIOabtOaWsOWbvuihqOmFjee9rg0KICAgICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgICBkYXRhOiB5QXhpc0RhdGEsDQogICAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgICBuYW1lOiAn6YeR6aKdICjkuIflhYMpJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgZGF0YTogc2VyaWVzRGF0YQ0KICAgICAgICAgIH1dDQogICAgICAgIH07DQoNCiAgICAgICAgdGhpcy5jaGFydHMuaW50ZXJuYWxDb3N0RGV0YWlsQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7DQogICAgICAgIGNvbnNvbGUubG9nKCflhoXpg6jmjZ/lpLHmiJDmnKzmnoTmiJDlm77ooajmlbDmja7lt7Lmm7TmlrDvvIjljIXlkKsw5YC877yM5oyJ5pWw5YC85LuO6auY5Yiw5L2O5o6S5bqP77yJJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOabtOaWsOWklumDqOaNn+WkseaIkOacrOaehOaIkOWbvuihqA0KICAgIHVwZGF0ZUV4dGVybmFsQ29zdERldGFpbENoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5leHRlcm5hbENvc3REZXRhaWxDaGFydCAmJiBkYXRhKSB7DQogICAgICAgIC8vIOaUtumbhuaJgOacieaVsOaNrumhuQ0KICAgICAgICBjb25zdCBhbGxEYXRhSXRlbXMgPSBbXTsNCiAgICAgICAgY29uc3QgY29sb3JzID0gWycjRkNBNUE1JywgJyNGREU2OEEnLCAnIzg2RUZBQycsICcjOTNDNUZEJywgJyNDNEI1RkQnXTsNCg0KICAgICAgICAvLyDlpITnkIblkITkuKrmiJDmnKzpobnvvIzmlLbpm4bliLDnu5/kuIDmlbDnu4TkuK3vvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgaWYgKGRhdGEuY3VzdG9tZXJDbGFpbUNvc3QpIHsNCiAgICAgICAgICBPYmplY3QuZW50cmllcyhkYXRhLmN1c3RvbWVyQ2xhaW1Db3N0KS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHsNCiAgICAgICAgICAgIGFsbERhdGFJdGVtcy5wdXNoKHsgbmFtZToga2V5LCB2YWx1ZTogKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMikgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAoZGF0YS5xdWFsaXR5T2JqZWN0aW9uRmVlQ29zdCkgew0KICAgICAgICAgIE9iamVjdC5lbnRyaWVzKGRhdGEucXVhbGl0eU9iamVjdGlvbkZlZUNvc3QpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4gew0KICAgICAgICAgICAgYWxsRGF0YUl0ZW1zLnB1c2goeyBuYW1lOiBrZXksIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKSB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGlmIChkYXRhLnF1YWxpdHlPYmplY3Rpb25UcmF2ZWxDb3N0KSB7DQogICAgICAgICAgT2JqZWN0LmVudHJpZXMoZGF0YS5xdWFsaXR5T2JqZWN0aW9uVHJhdmVsQ29zdCkuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7DQogICAgICAgICAgICBhbGxEYXRhSXRlbXMucHVzaCh7IG5hbWU6IGtleSwgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKGRhdGEucmV0dXJuTG9zcykgew0KICAgICAgICAgIE9iamVjdC5lbnRyaWVzKGRhdGEucmV0dXJuTG9zcykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7DQogICAgICAgICAgICBhbGxEYXRhSXRlbXMucHVzaCh7IG5hbWU6IGtleSwgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5oyJ5pWw5YC85LuO6auY5Yiw5L2O5o6S5bqPDQogICAgICAgIGFsbERhdGFJdGVtcy5zb3J0KChhLCBiKSA9PiBiLnZhbHVlIC0gYS52YWx1ZSk7DQoNCiAgICAgICAgLy8g5YiG56a75o6S5bqP5ZCO55qE5pWw5o2u77yM5Y+N6L2s6aG65bqP5L2/6YeR6aKd5aSn55qE5pi+56S65Zyo5LiK6Z2iDQogICAgICAgIGNvbnN0IHlBeGlzRGF0YSA9IFtdOw0KICAgICAgICBjb25zdCBzZXJpZXNEYXRhID0gW107DQoNCiAgICAgICAgLy8g5Y+N6L2s5pWw57uE77yM5L2/6YeR6aKd5aSn55qE5pi+56S65Zyo5Zu+6KGo5LiK5pa5DQogICAgICAgIGFsbERhdGFJdGVtcy5yZXZlcnNlKCkuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICB5QXhpc0RhdGEucHVzaChpdGVtLm5hbWUpOw0KICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzW2luZGV4ICUgY29sb3JzLmxlbmd0aF0gfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDmm7TmlrDlm77ooajphY3nva4NCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgZGF0YTogeUF4aXNEYXRhLA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgICAgbmFtZTogJ+mHkeminSAo5LiH5YWDKScsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGRhdGE6IHNlcmllc0RhdGENCiAgICAgICAgICB9XQ0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY2hhcnRzLmV4dGVybmFsQ29zdERldGFpbENoYXJ0LnNldE9wdGlvbihvcHRpb24pOw0KICAgICAgICBjb25zb2xlLmxvZygn5aSW6YOo5o2f5aSx5oiQ5pys5p6E5oiQ5Zu+6KGo5pWw5o2u5bey5pu05paw77yI5bey5oyJ5pWw5YC85LuO6auY5Yiw5L2O5o6S5bqP77yJJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIGdldFF1YWxpdHlDb3N0RGV0YWlsKCkgew0KICAgICAgLy8g5Y+q5pyJ5b2T5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyf6YO95pyJ5YC85pe25omN6K+35rGCDQogICAgICBpZiAoIXRoaXMuY29zdENlbnRlciB8fCAhdGhpcy5hY2NvdW50aW5nUGVyaW9kIHx8ICh0aGlzLmNvbnRhaW5UeXBlICE9PSAyICYmIHRoaXMuY29udGFpblR5cGUgIT09IDEpKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmiJDmnKzkuK3lv4PjgIHkvJrorqHmnJ/miJbotKjph4/miJDmnKznsbvlnovkuLrnqbrvvIzot7Pov4fmlbDmja7or7fmsYInKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIGNvc3RDZW50ZXI6IHRoaXMuY29zdENlbnRlciwNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLmFjY291bnRpbmdQZXJpb2QucmVwbGFjZSgnLScsICcnKSwgLy8g5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgICBjb250YWluVHlwZTogdGhpcy5jb250YWluVHlwZQ0KICAgICAgfTsNCg0KICAgICAgZ2V0UXVhbGl0eUNvc3REZXRhaWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ2dldFF1YWxpdHlDb3N0RGV0YWlsOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLnF1YWxpdHlDb3N0RGF0YSA9IHJlc3BvbnNlLmRhdGEucXVhbGl0eUNvc3REYXRhOw0KICAgICAgICAgIHRoaXMucXVhbGl0eUNvc3REZXRhaWwgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIC8vIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumlvOWbvuaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlui0qOmHj+aIkOacrOaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8v6LSo6YeP5oiQ5pys5Zub5aSn57G75Yir5Y2g5q+UDQogICAgZ2V0TXVsdGlMaW5lQ2hhcnREYXRhKCkgew0KICAgICAgLy8g5Y+q5pyJ5b2T5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyf6YO95pyJ5YC85pe25omN6K+35rGCDQogICAgICBpZiAoIXRoaXMuY29zdENlbnRlciB8fCAhdGhpcy5hY2NvdW50aW5nUGVyaW9kIHx8ICh0aGlzLmNvbnRhaW5UeXBlICE9PSAyICYmIHRoaXMuY29udGFpblR5cGUgIT09IDEpKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmiJDmnKzkuK3lv4PjgIHkvJrorqHmnJ/miJbotKjph4/miJDmnKznsbvlnovkuLrnqbrvvIzot7Pov4fmlbDmja7or7fmsYInKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIGNvc3RDZW50ZXI6IHRoaXMuY29zdENlbnRlciwNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLmFjY291bnRpbmdQZXJpb2QucmVwbGFjZSgnLScsICcnKSwgLy8g5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgICBjb250YWluVHlwZTogdGhpcy5jb250YWluVHlwZQ0KICAgICAgfTsNCg0KICAgICAgZ2V0TXVsdGlMaW5lQ2hhcnREYXRhKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdnZXRNdWx0aUxpbmVDaGFydERhdGE6JywgcmVzcG9uc2UpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMudXBkYXRlTXVsdGlMaW5lQ2hhcnQocmVzcG9uc2UuZGF0YSk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgLy8gY29uc29sZS5lcnJvcign6I635Y+W6aW85Zu+5pWw5o2u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6LSo6YeP5oiQ5pys5pWw5o2u5aSx6LSlJyk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgZ2V0Q29tYm9DaGFydERldGFpbCgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldENvbWJvQ2hhcnREZXRhaWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ2dldENvbWJvQ2hhcnREZXRhaWw6JywgcmVzcG9uc2UpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMudXBkYXRlQ29tYm9DaGFydChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5ZDb21ib0NoYXJ05pWw5o2u5aSx6LSlJyk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g5pu05pawQ29tYm9DaGFydOWbvuihqA0KICAgIHVwZGF0ZUNvbWJvQ2hhcnQoZGF0YSkgew0KICAgICAgaWYgKHRoaXMuY2hhcnRzLmNvbWJvQ2hhcnQgJiYgZGF0YSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5o6l5pS25Yiw55qEQ29tYm9DaGFydOaVsOaNrjonLCBkYXRhKTsNCg0KICAgICAgICAvLyDln7rkuo7kvJrorqHmnJ/nlJ/miJDov5E25Liq5pyI55qE5pyI5Lu95qCH562+5L2c5Li6eOi9tOaVsOaNrg0KICAgICAgICBjb25zdCBtb250aHMgPSB0aGlzLmdlbmVyYXRlQ29tYm9DaGFydE1vbnRoc0J5QWNjb3VudGluZ1BlcmlvZCgpOw0KICAgICAgICBjb25zb2xlLmxvZygn55Sf5oiQ55qE5pyI5Lu95qCH562+OicsIG1vbnRocyk7DQoNCiAgICAgICAgLy8g55Sf5oiQ5a+55bqU55qE5bm05pyI5qC85byP55So5LqO5pWw5o2u5Yy56YWNDQogICAgICAgIGNvbnN0IHllYXJNb250aHMgPSB0aGlzLmdlbmVyYXRlWWVhck1vbnRoc0J5QWNjb3VudGluZ1BlcmlvZCgpOw0KICAgICAgICBjb25zb2xlLmxvZygn55Sf5oiQ55qE5bm05pyI5qC85byPOicsIHllYXJNb250aHMpOw0KDQogICAgICAgIGNvbnN0IGZhaWx1cmVDb3N0RGF0YSA9IFtdOyAgICAgLy8g5aSx6LSl5oiQ5pys5pWw5o2uDQogICAgICAgIGNvbnN0IGNvbnRyb2xsaW5nQ29zdERhdGEgPSBbXTsgLy8g5o6n5Yi25oiQ5pys5pWw5o2uDQoNCiAgICAgICAgLy8g5Li65q+P5Liq5pyI5Lu95o+Q5Y+W5a+55bqU55qE5pWw5YC877yM6L2s5o2i5Li65LiH5YWDDQogICAgICAgIHllYXJNb250aHMuZm9yRWFjaCh5ZWFyTW9udGggPT4gew0KICAgICAgICAgIC8vIOiOt+WPluWksei0peaIkOacrOaVsOaNru+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICAgIGNvbnN0IGZhaWx1cmVWYWx1ZSA9IGRhdGEuZmFpbHVyZUNvc3RNYXAgJiYgZGF0YS5mYWlsdXJlQ29zdE1hcFt5ZWFyTW9udGhdDQogICAgICAgICAgICA/ICgoTnVtYmVyKGRhdGEuZmFpbHVyZUNvc3RNYXBbeWVhck1vbnRoXSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKQ0KICAgICAgICAgICAgOiAwOw0KICAgICAgICAgIGZhaWx1cmVDb3N0RGF0YS5wdXNoKGZhaWx1cmVWYWx1ZSk7DQoNCiAgICAgICAgICAvLyDojrflj5bmjqfliLbmiJDmnKzmlbDmja7vvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgICBjb25zdCBjb250cm9sbGluZ1ZhbHVlID0gZGF0YS5jb250cm9sbGluZ0Nvc3RNYXAgJiYgZGF0YS5jb250cm9sbGluZ0Nvc3RNYXBbeWVhck1vbnRoXQ0KICAgICAgICAgICAgPyAoKE51bWJlcihkYXRhLmNvbnRyb2xsaW5nQ29zdE1hcFt5ZWFyTW9udGhdKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpDQogICAgICAgICAgICA6IDA7DQogICAgICAgICAgY29udHJvbGxpbmdDb3N0RGF0YS5wdXNoKGNvbnRyb2xsaW5nVmFsdWUpOw0KICAgICAgICB9KTsNCg0KICAgICAgICBjb25zb2xlLmxvZygneOi9tOaciOS7veaVsOaNrjonLCBtb250aHMubWFwKG1vbnRoID0+IGAke21vbnRofeaciGApKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+Wksei0peaIkOacrOaVsOaNrjonLCBmYWlsdXJlQ29zdERhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygn5o6n5Yi25oiQ5pys5pWw5o2uOicsIGNvbnRyb2xsaW5nQ29zdERhdGEpOw0KDQogICAgICAgIC8vIOabtOaWsOWbvuihqOmFjee9rg0KICAgICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgICAgLy8g5Zu+5L6L6YWN572uIC0g5qCH5rOo6aKc6Imy5a+55bqU55qE57u05bqmDQogICAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgICBkYXRhOiBbJ+Wksei0peaIkOacrCcsICfmjqfliLbmiJDmnKwnXSwgLy8g5aSx6LSl5oiQ5pysKOe6ouiJsiNGQ0E1QTUp77yM5o6n5Yi25oiQ5pysKOe7v+iJsiM4NkVGQUMpDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjRTVFN0VCJyB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBncmlkOiB7IGxlZnQ6ICczJScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLA0KICAgICAgICAgICAgZGF0YTogbW9udGhzLm1hcChtb250aCA9PiBgJHttb250aH3mnIhgKSwgLy8g6L+RNuS4quaciOeahOaciOS7vQ0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICAgIHJvdGF0ZTogMCwgLy8g5rC05bmz5pi+56S65qCH562+DQogICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+aIkOacrCAo5LiH5YWDKScsDQogICAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgICAgfSwNCg0KICAgICAgICAgIHNlcmllczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBuYW1lOiAn5aSx6LSl5oiQ5pysJywgLy8g57qi6Imy5puy57q/ICNGQ0E1QTUNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICBkYXRhOiBmYWlsdXJlQ29zdERhdGEsDQogICAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5ZCv55So5bmz5ruR5puy57q/DQogICAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGQ0E1QTUnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjRkNBNUE1JyB9LA0KICAgICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBuYW1lOiAn5o6n5Yi25oiQ5pysJywgLy8g57u/6Imy5puy57q/ICM4NkVGQUMNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICBkYXRhOiBjb250cm9sbGluZ0Nvc3REYXRhLA0KICAgICAgICAgICAgICBzbW9vdGg6IHRydWUsIC8vIOWQr+eUqOW5s+a7keabsue6vw0KICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjODZFRkFDJywgd2lkdGg6IDMgfSwNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzg2RUZBQycgfSwNCiAgICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgICAgc3ltYm9sU2l6ZTogNg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLmNoYXJ0cy5jb21ib0NoYXJ0LnNldE9wdGlvbihvcHRpb24pOw0KICAgICAgICBjb25zb2xlLmxvZygnQ29tYm9DaGFydOWbvuihqOaVsOaNruW3suabtOaWsCcpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDnlJ/miJBDb21ib0NoYXJ055qE5pyI5Lu95qCH562+77yI5b2T5YmN5pyI5Lu95ZKM5LmL5YmN55qENeS4quaciO+8iQ0KICAgIGdlbmVyYXRlQ29tYm9DaGFydE1vbnRocygpIHsNCiAgICAgIGNvbnN0IG1vbnRocyA9IFtdOw0KICAgICAgY29uc3QgY3VycmVudERhdGUgPSBuZXcgRGF0ZSgpOw0KDQogICAgICBmb3IgKGxldCBpID0gNTsgaSA+PSAwOyBpLS0pIHsNCiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGN1cnJlbnREYXRlLmdldEZ1bGxZZWFyKCksIGN1cnJlbnREYXRlLmdldE1vbnRoKCkgLSBpLCAxKTsNCiAgICAgICAgY29uc3QgbW9udGggPSBkYXRlLmdldE1vbnRoKCkgKyAxOw0KICAgICAgICBtb250aHMucHVzaChtb250aCk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiBtb250aHM7DQogICAgfSwNCg0KICAgIC8vIOeUn+aIkOWvueW6lOeahOW5tOaciOagvOW8j++8iOW9k+WJjeaciOS7veWSjOS5i+WJjeeahDXkuKrmnIjvvIzlpoIyMDI1MDEsIDIwMjUwMuetie+8iQ0KICAgIGdlbmVyYXRlWWVhck1vbnRocygpIHsNCiAgICAgIGNvbnN0IHllYXJNb250aHMgPSBbXTsNCiAgICAgIGNvbnN0IGN1cnJlbnREYXRlID0gbmV3IERhdGUoKTsNCg0KICAgICAgZm9yIChsZXQgaSA9IDU7IGkgPj0gMDsgaS0tKSB7DQogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShjdXJyZW50RGF0ZS5nZXRGdWxsWWVhcigpLCBjdXJyZW50RGF0ZS5nZXRNb250aCgpIC0gaSwgMSk7DQogICAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZS5nZXRNb250aCgpICsgMTsNCiAgICAgICAgY29uc3QgeWVhck1vbnRoID0gYCR7eWVhcn0ke1N0cmluZyhtb250aCkucGFkU3RhcnQoMiwgJzAnKX1gOw0KICAgICAgICB5ZWFyTW9udGhzLnB1c2goeWVhck1vbnRoKTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHllYXJNb250aHM7DQogICAgfSwNCg0KICAgIC8vIOWfuuS6juS8muiuoeacn+eUn+aIkENvbWJvQ2hhcnTnmoTmnIjku73moIfnrb7vvIjkvJrorqHmnJ/lvZPliY3mnIjku73lkozkuYvliY3nmoQ15Liq5pyI77yJDQogICAgZ2VuZXJhdGVDb21ib0NoYXJ0TW9udGhzQnlBY2NvdW50aW5nUGVyaW9kKCkgew0KICAgICAgY29uc3QgbW9udGhzID0gW107DQoNCiAgICAgIGlmICghdGhpcy5hY2NvdW50aW5nUGVyaW9kKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign5Lya6K6h5pyf5Li656m677yM5L2/55So57O757uf5b2T5YmN5pe26Ze0Jyk7DQogICAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlQ29tYm9DaGFydE1vbnRocygpOw0KICAgICAgfQ0KDQogICAgICAvLyDop6PmnpDkvJrorqHmnJ8gKOagvOW8jzogMjAyNS0wNikNCiAgICAgIGNvbnN0IFt5ZWFyLCBtb250aF0gPSB0aGlzLmFjY291bnRpbmdQZXJpb2Quc3BsaXQoJy0nKS5tYXAoTnVtYmVyKTsNCiAgICAgIGNvbnN0IGFjY291bnRpbmdEYXRlID0gbmV3IERhdGUoeWVhciwgbW9udGggLSAxLCAxKTsgLy8gbW9udGgtMSDlm6DkuLpEYXRl55qE5pyI5Lu95LuOMOW8gOWniw0KDQogICAgICBmb3IgKGxldCBpID0gNTsgaSA+PSAwOyBpLS0pIHsNCiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGFjY291bnRpbmdEYXRlLmdldEZ1bGxZZWFyKCksIGFjY291bnRpbmdEYXRlLmdldE1vbnRoKCkgLSBpLCAxKTsNCiAgICAgICAgY29uc3QgbW9udGhOdW0gPSBkYXRlLmdldE1vbnRoKCkgKyAxOw0KICAgICAgICBtb250aHMucHVzaChtb250aE51bSk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiBtb250aHM7DQogICAgfSwNCg0KICAgIC8vIOWfuuS6juS8muiuoeacn+eUn+aIkOWvueW6lOeahOW5tOaciOagvOW8j++8iOS8muiuoeacn+W9k+WJjeaciOS7veWSjOS5i+WJjeeahDXkuKrmnIjvvIkNCiAgICBnZW5lcmF0ZVllYXJNb250aHNCeUFjY291bnRpbmdQZXJpb2QoKSB7DQogICAgICBjb25zdCB5ZWFyTW9udGhzID0gW107DQoNCiAgICAgIGlmICghdGhpcy5hY2NvdW50aW5nUGVyaW9kKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign5Lya6K6h5pyf5Li656m677yM5L2/55So57O757uf5b2T5YmN5pe26Ze0Jyk7DQogICAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlWWVhck1vbnRocygpOw0KICAgICAgfQ0KDQogICAgICAvLyDop6PmnpDkvJrorqHmnJ8gKOagvOW8jzogMjAyNS0wNikNCiAgICAgIGNvbnN0IFt5ZWFyLCBtb250aF0gPSB0aGlzLmFjY291bnRpbmdQZXJpb2Quc3BsaXQoJy0nKS5tYXAoTnVtYmVyKTsNCiAgICAgIGNvbnN0IGFjY291bnRpbmdEYXRlID0gbmV3IERhdGUoeWVhciwgbW9udGggLSAxLCAxKTsgLy8gbW9udGgtMSDlm6DkuLpEYXRl55qE5pyI5Lu95LuOMOW8gOWniw0KDQogICAgICBmb3IgKGxldCBpID0gNTsgaSA+PSAwOyBpLS0pIHsNCiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGFjY291bnRpbmdEYXRlLmdldEZ1bGxZZWFyKCksIGFjY291bnRpbmdEYXRlLmdldE1vbnRoKCkgLSBpLCAxKTsNCiAgICAgICAgY29uc3QgeWVhck51bSA9IGRhdGUuZ2V0RnVsbFllYXIoKTsNCiAgICAgICAgY29uc3QgbW9udGhOdW0gPSBkYXRlLmdldE1vbnRoKCkgKyAxOw0KICAgICAgICBjb25zdCB5ZWFyTW9udGggPSBgJHt5ZWFyTnVtfSR7U3RyaW5nKG1vbnRoTnVtKS5wYWRTdGFydCgyLCAnMCcpfWA7DQogICAgICAgIHllYXJNb250aHMucHVzaCh5ZWFyTW9udGgpOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4geWVhck1vbnRoczsNCiAgICB9LA0KDQogICAgLy/otKjph4/miJDmnKzlm5vlpKfnsbvliKvljaDmr5QNCiAgICBnZXRQaWVDaGFydERhdGEoKSB7DQogICAgICAvLyDlj6rmnInlvZPmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ/pg73mnInlgLzml7bmiY3or7fmsYINCiAgICAgIGlmICghdGhpcy5jb3N0Q2VudGVyIHx8ICF0aGlzLmFjY291bnRpbmdQZXJpb2QgfHwgKHRoaXMuY29udGFpblR5cGUgIT09IDIgJiYgdGhpcy5jb250YWluVHlwZSAhPT0gMSkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aIkOacrOS4reW/g+OAgeS8muiuoeacn+aIlui0qOmHj+aIkOacrOexu+Wei+S4uuepuu+8jOi3s+i/h+aVsOaNruivt+axgicpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgY29zdENlbnRlcjogdGhpcy5jb3N0Q2VudGVyLA0KICAgICAgICB5ZWFyTW9udGg6IHRoaXMuYWNjb3VudGluZ1BlcmlvZC5yZXBsYWNlKCctJywgJycpLCAvLyDlsIYgMjAyNS0wNiDovazmjaLkuLogMjAyNTA2DQogICAgICAgIGNvbnRhaW5UeXBlOiB0aGlzLmNvbnRhaW5UeXBlDQogICAgICB9Ow0KDQoNCiAgICAgIGdldFBpZUNoYXJ0RGF0YShwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnZ2V0UGllQ2hhcnREYXRhOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLnVwZGF0ZVBpZUNoYXJ0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumlvOWbvuaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlui0qOmHj+aIkOacrOaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDojrflj5bmiJDmnKzkuK3lv4PliJfooagNCiAgICBnZXRDb3N0Q2VudGVyTGlzdCgpIHsNCiAgICAgIHRoaXMuY29zdENlbnRlckxvYWRpbmcgPSB0cnVlOw0KICAgICAgY29zdENlbnRlcmxpc3QoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5jb3N0Q2VudGVyT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGEgfHwgW107DQogICAgICAgIC8vIOWmguaenOacieaVsOaNru+8jOiuvue9rum7mOiupOmAieS4reesrOS4gOS4qg0KICAgICAgICBpZiAodGhpcy5jb3N0Q2VudGVyT3B0aW9ucy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+iOt+WPluaIkOacrOS4reW/g+WIl+ihqDonLCB0aGlzLmNvc3RDZW50ZXJPcHRpb25zKTsNCiAgICAgICAgICB0aGlzLmNvc3RDZW50ZXIgPSB0aGlzLmNvc3RDZW50ZXJPcHRpb25zWzBdLmtleTsNCiAgICAgICAgICAvLyDorr7nva7pu5jorqTlgLzlkI7vvIzkuLvliqjop6blj5HkuIDmrKHmlbDmja7liLfmlrANCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnJlZnJlc2hDaGFydERhdGEoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmiJDmnKzkuK3lv4PliJfooajlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmiJDmnKzkuK3lv4PliJfooajlpLHotKUnKTsNCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmNvc3RDZW50ZXJMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g5pu05paw6aW85Zu+5pWw5o2uDQogICAgdXBkYXRlUGllQ2hhcnQoZGF0YSkgew0KICAgICAgaWYgKHRoaXMuY2hhcnRzLnBpZUNoYXJ0ICYmIGRhdGEpIHsNCiAgICAgICAgLy8g5pu05paw6aW85Zu+55qE5pWw5o2u77yM6L2s5o2i5Li65LiH5YWDDQogICAgICAgIGNvbnN0IG9wdGlvbiA9IHRoaXMuY2hhcnRzLnBpZUNoYXJ0LmdldE9wdGlvbigpOw0KICAgICAgICBpZiAob3B0aW9uICYmIG9wdGlvbi5zZXJpZXMgJiYgb3B0aW9uLnNlcmllc1swXSkgew0KICAgICAgICAgIG9wdGlvbi5zZXJpZXNbMF0uZGF0YSA9IFsNCiAgICAgICAgICAgIHsgdmFsdWU6IChkYXRhLnByZXZlbnRpb25Db3N0IC8gMTAwMDApLnRvRml4ZWQoMiksIG5hbWU6ICfpooTpmLLmiJDmnKwnLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTNDNUZEJyB9IH0sDQogICAgICAgICAgICB7IHZhbHVlOiAoZGF0YS5hcHByYWlzYWxDb3N0IC8gMTAwMDApLnRvRml4ZWQoMiksIG5hbWU6ICfpibTlrprmiJDmnKwnLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjODZFRkFDJyB9IH0sDQogICAgICAgICAgICB7IHZhbHVlOiAoZGF0YS5pbnRlcm5hbENvc3QgLyAxMDAwMCkudG9GaXhlZCgyKSwgbmFtZTogJ+WGhemDqOaNn+WkseaIkOacrCcsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNGREU2OEEnIH0gfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IChkYXRhLmV4dGVybmFsQ29zdCAvIDEwMDAwKS50b0ZpeGVkKDIpLCBuYW1lOiAn5aSW6YOo5o2f5aSx5oiQ5pysJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0ZDQTVBNScgfSB9LA0KICAgICAgICAgIF0sDQogICAgICAgICAgICB0aGlzLmNoYXJ0cy5waWVDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsNCiAgICAgICAgICAvLyBjb25zb2xlLmxvZygn6aW85Zu+5pWw5o2u5bey5pu05pawJyk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5pu05paw5aSa57q/5Zu+5pWw5o2uDQogICAgdXBkYXRlTXVsdGlMaW5lQ2hhcnQoZGF0YSkgew0KICAgICAgaWYgKHRoaXMuY2hhcnRzLm11bHRpTGluZUNoYXJ0ICYmIGRhdGEpIHsNCiAgICAgICAgLy8g5Z+65LqO5Lya6K6h5pyf55Sf5oiQ5pyI5Lu95qCH562+5ZKM5a+55bqU55qE5bm05pyI5pWw5a2XDQogICAgICAgIGNvbnN0IG1vbnRocyA9IHRoaXMuZ2VuZXJhdGVDb21ib0NoYXJ0TW9udGhzQnlBY2NvdW50aW5nUGVyaW9kKCk7DQogICAgICAgIGNvbnN0IHllYXJNb250aHMgPSB0aGlzLmdlbmVyYXRlWWVhck1vbnRoc0J5QWNjb3VudGluZ1BlcmlvZCgpOw0KDQogICAgICAgIC8vIOWkhOeQhuWQhOenjeaIkOacrOaVsOaNru+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICBjb25zdCBwcmV2ZW50aW9uRGF0YSA9IHRoaXMucHJvY2Vzc01hcERhdGEoZGF0YS5wcmV2ZW50aW9uQ29zdE1hcCwgeWVhck1vbnRocywgdHJ1ZSk7DQogICAgICAgIGNvbnN0IGFwcHJhaXNhbERhdGEgPSB0aGlzLnByb2Nlc3NNYXBEYXRhKGRhdGEuYXBwcmFpc2FsQ29zdE1hcCwgeWVhck1vbnRocywgdHJ1ZSk7DQogICAgICAgIGNvbnN0IGludGVybmFsRGF0YSA9IHRoaXMucHJvY2Vzc01hcERhdGEoZGF0YS5pbnRlcm5hbENvc3RNYXAsIHllYXJNb250aHMsIHRydWUpOw0KICAgICAgICBjb25zdCBleHRlcm5hbERhdGEgPSB0aGlzLnByb2Nlc3NNYXBEYXRhKGRhdGEuZXh0ZXJuYWxDb3N0TWFwLCB5ZWFyTW9udGhzLCB0cnVlKTsNCg0KICAgICAgICAvLyDmm7TmlrDlpJrnur/lm77nmoTphY3nva4NCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLA0KICAgICAgICAgICAgZGF0YTogbW9udGhzLm1hcChtb250aCA9PiBgJHttb250aH3mnIhgKSwgLy8g5qC85byP5YyW5Li6IljmnIgiDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6ICfpooTpmLLmiJDmnKwnLA0KICAgICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICAgIGRhdGE6IHByZXZlbnRpb25EYXRhLA0KICAgICAgICAgICAgICBzbW9vdGg6IHRydWUsIC8vIOWQr+eUqOW5s+a7keabsue6vw0KICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjOTNDNUZEJywgd2lkdGg6IDMgfSwNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzkzQzVGRCcgfSwNCiAgICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgICAgc3ltYm9sU2l6ZTogNg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbmFtZTogJ+mJtOWumuaIkOacrCcsDQogICAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgICAgZGF0YTogYXBwcmFpc2FsRGF0YSwNCiAgICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzg2RUZBQycsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM4NkVGQUMnIH0sDQogICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6ICflhoXpg6jmjZ/lpLHmiJDmnKwnLA0KICAgICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICAgIGRhdGE6IGludGVybmFsRGF0YSwNCiAgICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnI0ZERTY4QScsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNGREU2OEEnIH0sDQogICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6ICflpJbpg6jmjZ/lpLHmiJDmnKwnLA0KICAgICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICAgIGRhdGE6IGV4dGVybmFsRGF0YSwNCiAgICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnI0ZDQTVBNScsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNGQ0E1QTUnIH0sDQogICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH07DQoNCiAgICAgICAgdGhpcy5jaGFydHMubXVsdGlMaW5lQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7DQogICAgICAgIGNvbnNvbGUubG9nKCflpJrnur/lm77mlbDmja7lt7Lmm7TmlrAnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5aSE55CGTWFw5pWw5o2u77yM5qC55o2u5bm05pyI5Yy56YWN5a+55bqU55qE5YC8DQogICAgcHJvY2Vzc01hcERhdGEoY29zdE1hcCwgeWVhck1vbnRocywgY29udmVydFRvV2FuWXVhbiA9IGZhbHNlKSB7DQogICAgICBpZiAoIWNvc3RNYXApIHJldHVybiBuZXcgQXJyYXkoeWVhck1vbnRocy5sZW5ndGgpLmZpbGwoMCk7DQoNCiAgICAgIHJldHVybiB5ZWFyTW9udGhzLm1hcCh5ZWFyTW9udGggPT4gew0KICAgICAgICBjb25zdCB2YWx1ZSA9IGNvc3RNYXBbeWVhck1vbnRoXSB8fCAwOw0KICAgICAgICByZXR1cm4gY29udmVydFRvV2FuWXVhbiA/ICh2YWx1ZSAvIDEwMDAwKS50b0ZpeGVkKDIpIDogdmFsdWU7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g55Sf5oiQ5pyI5Lu95qCH562+77yI5b2T5YmN5pyI5Lu95Y+K5YmN5LqU5Liq5pyI5Lu977yJDQogICAgZ2VuZXJhdGVNb250aExhYmVscygpIHsNCiAgICAgIGNvbnN0IG1vbnRocyA9IFtdOw0KICAgICAgY29uc3QgeWVhck1vbnRocyA9IFtdOw0KICAgICAgY29uc3QgY3VycmVudERhdGUgPSBuZXcgRGF0ZSgpOw0KDQogICAgICBmb3IgKGxldCBpID0gNTsgaSA+PSAwOyBpLS0pIHsNCiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGN1cnJlbnREYXRlLmdldEZ1bGxZZWFyKCksIGN1cnJlbnREYXRlLmdldE1vbnRoKCkgLSBpLCAxKTsNCiAgICAgICAgY29uc3QgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTsNCiAgICAgICAgY29uc3QgbW9udGggPSBkYXRlLmdldE1vbnRoKCkgKyAxOw0KDQogICAgICAgIG1vbnRocy5wdXNoKGAke21vbnRofeaciGApOw0KICAgICAgICB5ZWFyTW9udGhzLnB1c2gocGFyc2VJbnQoYCR7eWVhcn0ke1N0cmluZyhtb250aCkucGFkU3RhcnQoMiwgJzAnKX1gKSk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiB7IG1vbnRocywgeWVhck1vbnRocyB9Ow0KICAgIH0sDQoNCiAgICAvLyDliLfmlrDlm77ooajmlbDmja4NCiAgICByZWZyZXNoQ2hhcnREYXRhKCkgew0KICAgICAgLy8g5Y+q5pyJ5b2T5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyf6YO95pyJ5YC85pe25omN5Yi35pawDQogICAgICBpZiAoIXRoaXMuY29zdENlbnRlciB8fCAhdGhpcy5hY2NvdW50aW5nUGVyaW9kIHx8ICh0aGlzLmNvbnRhaW5UeXBlICE9PSAyICYmIHRoaXMuY29udGFpblR5cGUgIT09IDEpKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5nZXRRdWFsaXR5Q29zdERldGFpbCgpOw0KICAgICAgdGhpcy5nZXRQaWVDaGFydERhdGEoKTsNCiAgICAgIHRoaXMuZ2V0TXVsdGlMaW5lQ2hhcnREYXRhKCk7DQogICAgICB0aGlzLmdldEV4dGVybmFsQ29zdERldGFpbCgpOw0KICAgICAgdGhpcy5nZXRJbnRlcm5hbENvc3REZXRhaWwoKTsNCiAgICAgIHRoaXMuZ2V0Q29tYm9DaGFydERldGFpbCgpOw0KICAgICAgdGhpcy5nZXRXYXRlcmZhbGxDaGFydERldGFpbCgpOw0KICAgICAgdGhpcy5nZXRTY3JhcExvc3NDaGFydERldGFpbHNEZXRhaWwoKTsNCiAgICAgIHRoaXMuZ2V0UXVhbGl0eU9iamVjdGlvbkxvc3NEZXRhaWwoKTsNCg0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5YW25LuW5Zu+6KGo55qE5pWw5o2u5Yi35pawDQogICAgICAvLyB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW3suWIh+aNouWIsOaIkOacrOS4reW/gzogJHt0aGlzLmNvc3RDZW50ZXJ9LCDkvJrorqHmnJ86ICR7dGhpcy5hY2NvdW50aW5nUGVyaW9kfWApOw0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+i5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnJlZnJlc2hDaGFydERhdGEoKTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICAvLyDph43nva7kuLrpu5jorqTlgLwNCiAgICAgIGlmICh0aGlzLmNvc3RDZW50ZXJPcHRpb25zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5jb3N0Q2VudGVyID0gdGhpcy5jb3N0Q2VudGVyT3B0aW9uc1swXS5rZXk7DQogICAgICB9DQoNCiAgICAgIC8vIOiOt+WPlum7mOiupOS8muiuoeacnw0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgIGNvbnN0IHllYXIgPSBub3cuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGNvbnN0IG1vbnRoID0gbm93LmdldE1vbnRoKCk7DQogICAgICBjb25zdCBwcmV2TW9udGggPSBtb250aCA9PT0gMCA/IDEyIDogbW9udGg7DQogICAgICBjb25zdCBwcmV2WWVhciA9IG1vbnRoID09PSAwID8geWVhciAtIDEgOiB5ZWFyOw0KICAgICAgdGhpcy5hY2NvdW50aW5nUGVyaW9kID0gYCR7cHJldlllYXJ9LSR7U3RyaW5nKHByZXZNb250aCkucGFkU3RhcnQoMiwgJzAnKX1gOw0KDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+afpeivouadoeS7tuW3sumHjee9ricpOw0KICAgIH0sDQoNCiAgICBpbml0Q2hhcnRzKCkgew0KICAgICAgY29uc3QgVEhFTUUgPSAnZGFyaycNCg0KICAgICAgLy8g5a6a5LmJ5ZWG5Yqh6aOO5reh6Imy57O76Imy5b2p5pa55qGIDQogICAgICB0aGlzLmJ1c2luZXNzQ29sb3JzID0gew0KICAgICAgICBsaWdodDogWycjOTNDNUZEJywgJyM4NkVGQUMnLCAnI0ZERTY4QScsICcjRkNBNUE1JywgJyNDNEI1RkQnLCAnIzdERDNGQycsICcjRjlBOEQ0JywgJyNCRUYyNjQnXSwNCiAgICAgICAgZ3JhZGllbnQ6IFsNCiAgICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICcjM0I4MkY2JyB9LA0KICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJyMxRTQwQUYnIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KDQogICAgICAvLyDliJ3lp4vljJbmiYDmnInlm77ooagNCiAgICAgIHRoaXMuY2hhcnRzLnBpZUNoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMucGllQ2hhcnQsIFRIRU1FKQ0KICAgICAgdGhpcy5jaGFydHMubXVsdGlMaW5lQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy5tdWx0aUxpbmVDaGFydCwgVEhFTUUpDQogICAgICB0aGlzLmNoYXJ0cy5leHRlcm5hbENvc3REZXRhaWxDaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLmV4dGVybmFsQ29zdERldGFpbENoYXJ0LCBUSEVNRSkNCiAgICAgIHRoaXMuY2hhcnRzLmludGVybmFsQ29zdERldGFpbENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuaW50ZXJuYWxDb3N0RGV0YWlsQ2hhcnQsIFRIRU1FKQ0KICAgICAgdGhpcy5jaGFydHMud2F0ZXJmYWxsQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy53YXRlcmZhbGxDaGFydCwgVEhFTUUpDQogICAgICB0aGlzLmNoYXJ0cy5jb21ib0NoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuY29tYm9DaGFydCwgVEhFTUUpDQogICAgICB0aGlzLmNoYXJ0cy5zY3JhcExvc3NDaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnNjcmFwTG9zc0NoYXJ0LCBUSEVNRSkNCiAgICAgIHRoaXMuY2hhcnRzLnF1YWxpdHlPYmplY3Rpb25DaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnF1YWxpdHlPYmplY3Rpb25DaGFydCwgVEhFTUUpDQoNCiAgICAgIC8vIOmFjee9ruaJgOacieWbvuihqA0KICAgICAgdGhpcy5zZXRQaWVDaGFydE9wdGlvbigpDQogICAgICB0aGlzLnNldE11bHRpTGluZUNoYXJ0T3B0aW9uKCkNCiAgICAgIHRoaXMuc2V0RXh0ZXJuYWxDb3N0RGV0YWlsQ2hhcnRPcHRpb24oKQ0KICAgICAgdGhpcy5zZXRJbnRlcm5hbENvc3REZXRhaWxDaGFydE9wdGlvbigpDQogICAgICB0aGlzLnNldFdhdGVyZmFsbENoYXJ0T3B0aW9uKCkNCiAgICAgIHRoaXMuc2V0Q29tYm9DaGFydE9wdGlvbigpDQogICAgICB0aGlzLnNldFNjcmFwTG9zc0NoYXJ0T3B0aW9uKCkNCiAgICAgIHRoaXMuc2V0UXVhbGl0eU9iamVjdGlvbkNoYXJ0T3B0aW9uKCkNCiAgICB9LA0KDQogICAgc2V0UGllQ2hhcnRPcHRpb24oKSB7DQogICAgICB0aGlzLmNoYXJ0cy5waWVDaGFydC5zZXRPcHRpb24oew0KICAgICAgICBjb2xvcjogWycjOTNDNUZEJywgJyM4NkVGQUMnLCAnI0ZERTY4QScsICcjRkNBNUE1J10sDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsDQogICAgICAgICAgZm9ybWF0dGVyOiAocGFyYW1zKSA9PiB7DQogICAgICAgICAgICBjb25zdCB2YWx1ZSA9IHBhcnNlRmxvYXQocGFyYW1zLnZhbHVlKS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgY29uc3QgZm9ybWF0dGVkVmFsdWUgPSB2YWx1ZS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgICAgICAgICByZXR1cm4gYCR7cGFyYW1zLnNlcmllc05hbWV9IDxici8+JHtwYXJhbXMubmFtZX06ICR7Zm9ybWF0dGVkVmFsdWV95LiH5YWDICgke3BhcmFtcy5wZXJjZW50fSUpYDsNCiAgICAgICAgICB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9DQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIHRvcDogJ2JvdHRvbScsDQogICAgICAgICAgbGVmdDogJ2NlbnRlcicsDQogICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI0U1RTdFQicsIGZvbnRTaXplOiAxMiB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBuYW1lOiAn5oiQ5pys57G75YirJywNCiAgICAgICAgICB0eXBlOiAncGllJywNCiAgICAgICAgICByYWRpdXM6ICc2NSUnLA0KICAgICAgICAgIGRhdGE6IFtdLA0KICAgICAgICAgIGVtcGhhc2lzOiB7DQogICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgc2hhZG93Qmx1cjogMTUsDQogICAgICAgICAgICAgIHNoYWRvd09mZnNldFg6IDAsDQogICAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgxNDcsIDE5NywgMjUzLCAwLjYpJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgbGFiZWxMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0gfSwNCiAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgY29sb3I6ICcjRTVFN0VCJywNCiAgICAgICAgICAgIGZvcm1hdHRlcjogKHBhcmFtcykgPT4gew0KICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IHBhcnNlRmxvYXQocGFyYW1zLnZhbHVlKS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRWYWx1ZSA9IHZhbHVlLnRvU3RyaW5nKCkucmVwbGFjZSgvXEIoPz0oXGR7M30pKyg/IVxkKSkvZywgJywnKTsNCiAgICAgICAgICAgICAgcmV0dXJuIGAke3BhcmFtcy5uYW1lfSgke2Zvcm1hdHRlZFZhbHVlfeS4h+WFgylgOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KDQoNCiAgICBzZXRDb21ib0NoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuY29tYm9DaGFydC5zZXRPcHRpb24oew0KICAgICAgICBjb2xvcjogWycjRkNBNUE1JywgJyM4NkVGQUMnXSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnY3Jvc3MnIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogWyflpLHotKXmiJDmnKwnLCAn5o6n5Yi25oiQ5pysJ10sDQogICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI0U1RTdFQicgfQ0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICczJScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLA0KICAgICAgICAgIGRhdGE6IFsnMeaciCcsICcy5pyIJywgJzPmnIgnLCAnNOaciCcsICc15pyIJywgJzbmnIgnXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn5oiQ5pysICjkuIflhYMpJywNCiAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICflpLHotKXmiJDmnKwnLCAvLyDnuqLoibLmm7Lnur8gI0ZDQTVBNQ0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgZGF0YTogWzI4MCwgMjYwLCAyNDAsIDIyMCwgMjAwLCAxODBdLA0KICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGQ0E1QTUnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0ZDQTVBNScgfSwNCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5o6n5Yi25oiQ5pysJywgLy8g57u/6Imy5puy57q/ICM4NkVGQUMNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIGRhdGE6IFsxMjAsIDEyNSwgMTMwLCAxMzUsIDE0MCwgMTQ1XSwNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5ZCv55So5bmz5ruR5puy57q/DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjODZFRkFDJywgd2lkdGg6IDMgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM4NkVGQUMnIH0sDQogICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgc3ltYm9sU2l6ZTogNg0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgc2V0TXVsdGlMaW5lQ2hhcnRPcHRpb24oKSB7DQogICAgICB0aGlzLmNoYXJ0cy5tdWx0aUxpbmVDaGFydC5zZXRPcHRpb24oew0KICAgICAgICBjb2xvcjogWycjOTNDNUZEJywgJyM4NkVGQUMnLCAnI0ZERTY4QScsICcjRkNBNUE1J10sDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogWyfpooTpmLLmiJDmnKwnLCAn6Ym05a6a5oiQ5pysJywgJ+WGhemDqOaNn+WkseaIkOacrCcsICflpJbpg6jmjZ/lpLHmiJDmnKwnXSwNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjRTVFN0VCJyB9DQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsgbGVmdDogJzMlJywgcmlnaHQ6ICc0JScsIGJvdHRvbTogJzMlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBib3VuZGFyeUdhcDogZmFsc2UsDQogICAgICAgICAgZGF0YTogWycx5pyIJywgJzLmnIgnLCAnM+aciCcsICc05pyIJywgJzXmnIgnLCAnNuaciCddLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgIHJvdGF0ZTogMCwgLy8g5rC05bmz5pi+56S65qCH562+DQogICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgIG5hbWU6ICfmiJDmnKwgKOS4h+WFgyknLA0KICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9LA0KICAgICAgICAgIHNwbGl0TGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjMzc0MTUxJyB9IH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+mihOmYsuaIkOacrCcsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhOiBbODAsIDgyLCA4NSwgODgsIDkwLCA5NV0sDQogICAgICAgICAgICBzbW9vdGg6IHRydWUsIC8vIOWQr+eUqOW5s+a7keabsue6vw0KICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzkzQzVGRCcsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTNDNUZEJyB9LA0KICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfpibTlrprmiJDmnKwnLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgZGF0YTogWzEyMCwgMTIyLCAxMjUsIDEyOCwgMTMwLCAxMzVdLA0KICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyM4NkVGQUMnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzg2RUZBQycgfSwNCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5YaF6YOo5o2f5aSx5oiQ5pysJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIGRhdGE6IFs0NTAsIDQzMCwgNDEwLCAzODAsIDM1MCwgMzIwXSwNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5ZCv55So5bmz5ruR5puy57q/DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjRkRFNjhBJywgd2lkdGg6IDMgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNGREU2OEEnIH0sDQogICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgc3ltYm9sU2l6ZTogNg0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+WklumDqOaNn+WkseaIkOacrCcsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhOiBbMzUwLCAzNDAsIDMxMCwgMjkwLCAyNjAsIDIzMF0sDQogICAgICAgICAgICBzbW9vdGg6IHRydWUsIC8vIOWQr+eUqOW5s+a7keabsue6vw0KICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnI0ZDQTVBNScsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjRkNBNUE1JyB9LA0KICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0pDQogICAgfSwNCg0KDQoNCg0KDQogICAgc2V0UGFyZXRvQ2hhcnRPcHRpb24oKSB7DQogICAgICB0aGlzLmNoYXJ0cy5wYXJldG9DaGFydC5zZXRPcHRpb24oew0KICAgICAgICBjb2xvcjogWycjOTNDNUZEJywgJyNGREU2OEEnXSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnY3Jvc3MnIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogeyByaWdodDogJzIwJScgfSwNCiAgICAgICAgeEF4aXM6IFt7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbJ+S6p+WTgeaKpeW6nycsICfkuqflk4HmlLnliKQnLCAn6K6+5aSH5pWF6ZqcJywgJ+W3peiJuuW6n+aWmScsICflhbbku5YnXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGludGVydmFsOiAwLA0KICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgfV0sDQogICAgICAgIHlBeGlzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6ICfmjZ/lpLHph5Hpop0o5YWDKScsDQogICAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+e0r+iuoeWNoOavlCcsDQogICAgICAgICAgICBtaW46IDAsDQogICAgICAgICAgICBtYXg6IDEwMCwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICd7dmFsdWV9ICUnLA0KICAgICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfmjZ/lpLHph5Hpop0nLA0KICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICBkYXRhOiBbMjgwLCAxMTAsIDM1LCAyMCwgNV0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTNDNUZEJyB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn57Sv6K6h5Y2g5q+UJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHlBeGlzSW5kZXg6IDEsDQogICAgICAgICAgICBkYXRhOiBbNjIuMiwgODYuNywgOTQuNCwgOTguOSwgMTAwXSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGREU2OEEnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0ZERTY4QScgfSwNCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA4DQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBzZXRFeHRlcm5hbENvc3REZXRhaWxDaGFydE9wdGlvbigpIHsNCiAgICAgIHRoaXMuY2hhcnRzLmV4dGVybmFsQ29zdERldGFpbENoYXJ0LnNldE9wdGlvbih7DQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgYXhpc1BvaW50ZXI6IHsgdHlwZTogJ3NoYWRvdycgfSwNCiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDMwLCA0MSwgNTksIDAuOSknLA0KICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzkzQzVGRCcsDQogICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI2ZmZicgfSwNCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIChwYXJhbXMpIHsNCiAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPic7DQogICAgICAgICAgICBwYXJhbXMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRWYWx1ZSA9IHBhcnNlRmxvYXQoaXRlbS52YWx1ZSkudG9GaXhlZCgyKS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgICAgICAgICAgIHJlc3VsdCArPSBpdGVtLm1hcmtlciArICcg6YeR6aKdOiAnICsgZm9ybWF0dGVkVmFsdWUgKyAn5LiH5YWDPGJyLz4nOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICByZXR1cm4gcmVzdWx0Ow0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogeyBsZWZ0OiAnNSUnLCByaWdodDogJzE1JScsIHRvcDogJzEyJScsIGJvdHRvbTogJzEyJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkemine+8iOS4h+WFg++8iScsDQogICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQoNCiAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldEludGVybmFsQ29zdERldGFpbENoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuaW50ZXJuYWxDb3N0RGV0YWlsQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgew0KICAgICAgICAgICAgbGV0IHJlc3VsdCA9IHBhcmFtc1swXS5uYW1lICsgJzxici8+JzsNCiAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFZhbHVlID0gcGFyc2VGbG9hdChpdGVtLnZhbHVlKS50b0ZpeGVkKDIpLnRvU3RyaW5nKCkucmVwbGFjZSgvXEIoPz0oXGR7M30pKyg/IVxkKSkvZywgJywnKTsNCiAgICAgICAgICAgICAgcmVzdWx0ICs9IGl0ZW0ubWFya2VyICsgJyDph5Hpop06ICcgKyBmb3JtYXR0ZWRWYWx1ZSArICfkuIflhYM8YnIvPic7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHJldHVybiByZXN1bHQ7DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICc1JScsIHJpZ2h0OiAnMTUlJywgdG9wOiAnMTIlJywgYm90dG9tOiAnMTIlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn6YeR6aKd77yI5LiH5YWD77yJJywNCiAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldFdhdGVyZmFsbENoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMud2F0ZXJmYWxsQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICczJScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogWyfliJ3lp4vmiJDmnKwnLCAn5L+u56OoJywgJ+efq+ebtCcsICfmjqLkvKQnLCAn54Ot5aSE55CGJywgJ+aAu+aIkOacrCddLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgaW50ZXJ2YWw6IDAsDQogICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLCAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicNCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn6L6F5YqpJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgc3RhY2s6ICfmgLvph48nLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgwLDAsMCwwKScsDQogICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAncmdiYSgwLDAsMCwwKScsDQogICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAwDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDAsMCwwLDApJw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YTogWzAsIDAsIDUwLCA4MCwgMTA1LCAwXQ0KICAgICAgICAgIH0sDQogICAgICAgIF0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldFNjcmFwTG9zc0NoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuc2NyYXBMb3NzQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICczJScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogW10sDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+aKpeW6n+aNn+WkseaIkOacrCcsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldFF1YWxpdHlPYmplY3Rpb25DaGFydE9wdGlvbigpIHsNCiAgICAgIHRoaXMuY2hhcnRzLnF1YWxpdHlPYmplY3Rpb25DaGFydC5zZXRPcHRpb24oew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgIGF4aXNQb2ludGVyOiB7IHR5cGU6ICdzaGFkb3cnIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0sDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsgbGVmdDogJzMlJywgcmlnaHQ6ICc0JScsIGJvdHRvbTogJzMlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICBpbnRlcnZhbDogMCwgLy8g5pi+56S65omA5pyJ5qCH562+DQogICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn6YeR6aKdICjlhYMpJywNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBuYW1lOiAn6LSo6YeP5byC6K6u5o2f5aSx5oiQ5pysJywNCiAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICBkYXRhOiBbXQ0KICAgICAgICB9XQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgc2V0RHVhbFlDaGFydE9wdGlvbigpIHsNCiAgICAgIHRoaXMuY2hhcnRzLmR1YWxZQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgY29sb3I6IFsnIzkzQzVGRCcsICcjRkRFNjhBJ10sDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgYXhpc1BvaW50ZXI6IHsgdHlwZTogJ2Nyb3NzJyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9DQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIGRhdGE6IFsn5Lqn6YePKOWQqCknLCAn5ZCo6ZKi5oiQ5pysKOWFgyknXSwNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjRTVFN0VCJyB9DQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiBbew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogWycx5pyIJywgJzLmnIgnLCAnM+aciCcsICc05pyIJywgJzXmnIgnLCAnNuaciCddLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgIHJvdGF0ZTogMCwgLy8g5rC05bmz5pi+56S65qCH562+DQogICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgfV0sDQogICAgICAgIHlBeGlzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6ICfkuqfph48o5ZCoKScsDQogICAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+WQqOmSouaIkOacrCjlhYMpJywNCiAgICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+S6p+mHjyjlkKgpJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgZGF0YTogWzgwMDAwLCA4MjAwMCwgODUwMDAsIDgzMDAwLCA4ODAwMCwgOTAwMDBdLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzkzQzVGRCcgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+WQqOmSouaIkOacrCjlhYMpJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHlBeGlzSW5kZXg6IDEsDQogICAgICAgICAgICBkYXRhOiBbMTMuMSwgMTIuOCwgMTIuNSwgMTIuNiwgMTIuMiwgMTIuMF0sDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjRkRFNjhBJywgd2lkdGg6IDMgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNGREU2OEEnIH0sDQogICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgc3ltYm9sU2l6ZTogOA0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgcmVzaXplQ2hhcnRzKCkgew0KICAgICAgT2JqZWN0LnZhbHVlcyh0aGlzLmNoYXJ0cykuZm9yRWFjaChjaGFydCA9PiB7DQogICAgICAgIGlmIChjaGFydCkgew0KICAgICAgICAgIGNoYXJ0LnJlc2l6ZSgpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/dashboard", "sourcesContent": ["<template>\r\n  <div class=\"quality-cost-dashboard\">\r\n    <header class=\"header\">\r\n      <div class=\"header-wrapper\">\r\n        <h1>兴澄特钢质量成本看板</h1>\r\n        <!-- 标题右下角筛选区域 -->\r\n        <div class=\"header-filters\">\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">成本中心：</span>\r\n            <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\"\r\n              size=\"small\">\r\n              <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">会计期：</span>\r\n            <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"请选择年月\" format=\"yyyy-MM\"\r\n              value-format=\"yyyy-MM\" style=\"width: 130px;\" size=\"small\">\r\n            </el-date-picker>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">质量成本类型：</span>\r\n            <el-select v-model=\"containType\" placeholder=\"请选择质量成本类型\" style=\"width: 130px;\" size=\"small\">\r\n              <el-option label=\"含不列入项\" :value=\"2\"></el-option>\r\n              <el-option label=\"不含列入项\" :value=\"1\"></el-option>\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <p>数据更新时间: {{ updateTime }}</p> -->\r\n    </header>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第四类：核心绩效指标（KPI）看板 -->\r\n      <div class=\"kpi-grid\">\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">{{ costCenter === 'JYXCTZG' ? '销量' : '产量' }}</div>\r\n          <div class=\"value\">{{ formatTonnage(containType === 2 ? qualityCostData.allcTon : qualityCostData.costTon) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costTonUpPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costTonUpPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costTonUpPercent)\">{{ qualityCostDetail.costTonUpPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">总金额</div>\r\n          <div class=\"value\">{{ formatAmount(containType === 2 ? qualityCostData.allcEx : qualityCostData.costEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costExPercent)\">{{ qualityCostDetail.costExPercent }} vs\r\n              上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">吨钢成本</div>\r\n          <div class=\"value\">{{ formatUnitCost(containType === 2 ? qualityCostData.allcPerEx : qualityCostData.costPerEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costPerExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costPerExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costPerExPercent)\">{{ qualityCostDetail.costPerExPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 第2行：质量成本四大类别占比（占1/2宽度）+ 四大质量成本趋势（占1/2宽度） -->\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>1. 质量成本四大类别占比</h3>\r\n        <div ref=\"pieChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>2. 四大质量成本趋势</h3>\r\n        <div ref=\"multiLineChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：外部损失成本构成图表 + 内部损失成本构成图表 - 每个占据行宽的50% -->\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>3. 外部损失成本构成</h3>\r\n        <div ref=\"externalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>4. 内部损失成本构成</h3>\r\n        <div ref=\"internalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：产品挽救处理成本分析图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>5. 产品挽救处理成本分析</h3>\r\n        <div ref=\"waterfallChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第4行：产品报废损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>6. 产品报废损失明细</h3>\r\n        <div ref=\"scrapLossChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第5行：产品质量异议损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>7. 产品质量异议损失明细</h3>\r\n        <div ref=\"qualityObjectionChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第7行：控制成本 vs 失败成本对比（占满整行） -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>8. \"控制成本\" vs \"失败成本\" 对比</h3>\r\n        <div ref=\"comboChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { getPieChartData, getMultiLineChartData, getQualityCostDetail, getExternalCostDetail, getInternalCostDetail, getComboChartDetail,getWaterfallChartDetail,getScrapLossChartDetailsDetail,getQualityObjectionLossDetail } from \"@/api/qualityCost/dashboard\";\r\n\r\nexport default {\r\n  name: 'QualityCostDashboard',\r\n  data() {\r\n    // 获取默认会计期（上个月）\r\n    const getDefaultYearMonth = () => {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    };\r\n\r\n    return {\r\n      updateTime: '2023-10-27 10:00',\r\n      charts: {},\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: getDefaultYearMonth(),\r\n      // 质量成本类型，默认值为1（不含列入项）\r\n      containType: 1,\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      qualityCostDetail: {},\r\n      qualityCostData: {}\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        console.log('成本中心变化:', this.costCenter);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        console.log('会计期变化:', this.accountingPeriod);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听质量成本类型变化\r\n    containType: {\r\n      handler() {\r\n        console.log('质量成本类型变化:', this.containType);\r\n        this.refreshChartData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n    //质量成本四大类别占比\r\n\r\n    this.initCharts();\r\n    this.resizeObserver = new ResizeObserver(() => {\r\n      this.resizeCharts()\r\n    })\r\n    this.resizeObserver.observe(this.$el)\r\n    window.addEventListener('resize', this.resizeCharts)\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁所有图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      if (chart) {\r\n        chart.dispose()\r\n      }\r\n    })\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect()\r\n    }\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n  },\r\n  methods: {\r\n    // 判断百分比是否为负数\r\n    isNegativePercentage(percentage) {\r\n      if (!percentage) return false;\r\n      return percentage.toString().startsWith('-');\r\n    },\r\n\r\n    // 根据百分比正负值返回对应的CSS类\r\n    getPercentageClass(percentage) {\r\n      if (!percentage) return 'neutral';\r\n      return this.isNegativePercentage(percentage) ? 'negative' : 'positive';\r\n    },\r\n\r\n    // 格式化数字，最多保留两位小数\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0';\r\n      }\r\n      // 使用toFixed(2)保留两位小数，然后用parseFloat去掉末尾的0\r\n      return parseFloat(number.toFixed(2)).toString();\r\n    },\r\n\r\n    // 添加千分位分隔符\r\n    addThousandSeparator(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    },\r\n\r\n    // 格式化产量/销量为万吨单位\r\n    formatTonnage(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万吨';\r\n      }\r\n      // 转换为万吨并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万吨`;\r\n    },\r\n\r\n    // 格式化总金额为万元单位\r\n    formatAmount(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万元';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万元';\r\n      }\r\n      // 转换为万元并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万元`;\r\n    },\r\n\r\n    // 格式化吨钢成本为元/吨单位\r\n    formatUnitCost(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0元/吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0元/吨';\r\n      }\r\n      // 保留两位小数并添加单位，添加千分位分隔符\r\n      const result = number.toFixed(2);\r\n      return `${this.addThousandSeparator(result)}元/吨`;\r\n    },\r\n\r\n\r\n    getWaterfallChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getWaterfallChartDetail(params).then(response => {\r\n        console.log('getWaterfallChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新WaterfallChart柱状图\r\n          this.updateWaterfallChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取WaterfallChart数据失败:', error);\r\n        this.$message.error('获取WaterfallChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新WaterfallChart柱状图\r\n    updateWaterfallChart(data) {\r\n      if (this.charts.waterfallChart && data) {\r\n        console.log('接收到的WaterfallChart数据:', data);\r\n\r\n        // 处理rescueProject数据\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n\r\n        let dataItems = [];\r\n\r\n        if (data.rescueProject) {\r\n          // 将rescueProject对象转换为数组，转换为万元\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,    // 第一项为维度名称\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)  // 第二项为对应维度的值，转换为万元\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '挽救处理成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.waterfallChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('WaterfallChart柱状图数据已更新');\r\n      } else {\r\n        console.error('WaterfallChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新ScrapLossChart柱状图\r\n    updateScrapLossChart(data) {\r\n      if (this.charts.scrapLossChart && data) {\r\n        console.log('接收到的ScrapLossChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理报废损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.scrapLossMap) {\r\n          // 情况1: 使用scrapLossMap数据（根据实际API返回的数据结构）\r\n          console.log('使用scrapLossMap数据');\r\n          dataItems = Object.entries(data.scrapLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLoss) {\r\n          // 情况3: 使用scrapLoss数据\r\n          console.log('使用scrapLoss数据');\r\n          dataItems = Object.entries(data.scrapLoss).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLossProject) {\r\n          // 情况4: 使用scrapLossProject数据\r\n          console.log('使用scrapLossProject数据');\r\n          dataItems = Object.entries(data.scrapLossProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况5: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '报废损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.scrapLossChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('ScrapLossChart柱状图数据已更新');\r\n      } else {\r\n        console.error('ScrapLossChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新QualityObjectionChart柱状图\r\n    updateQualityObjectionChart(data) {\r\n      if (this.charts.qualityObjectionChart && data) {\r\n        console.log('接收到的QualityObjectionChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理质量异议损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.qualityObjectionLossMap) {\r\n          // 情况1: 使用qualityObjectionLossMap数据\r\n          console.log('使用qualityObjectionLossMap数据');\r\n          dataItems = Object.entries(data.qualityObjectionLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况3: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '质量异议损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.qualityObjectionChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('QualityObjectionChart柱状图数据已更新');\r\n      } else {\r\n        console.error('QualityObjectionChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    getQualityObjectionLossDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityObjectionLossDetail(params).then(response => {\r\n        console.log('getQualityObjectionLossDetail:', response);\r\n        if (response.data) {\r\n          // 更新QualityObjectionChart柱状图\r\n          this.updateQualityObjectionChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取QualityObjectionChart数据失败:', error);\r\n        this.$message.error('获取产品质量异议损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getScrapLossChartDetailsDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getScrapLossChartDetailsDetail(params).then(response => {\r\n        console.log('getScrapLossChartDetailsDetail:', response);\r\n        if (response.data) {\r\n          // 更新ScrapLossChart柱状图\r\n          this.updateScrapLossChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取ScrapLossChart数据失败:', error);\r\n        this.$message.error('获取产品报废损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getExternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getExternalCostDetail(params).then(response => {\r\n        console.log('getExternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新外部损失成本构成图表\r\n          this.updateExternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取外部损失成本数据失败:', error);\r\n        this.$message.error('获取外部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    getInternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getInternalCostDetail(params).then(response => {\r\n        console.log('getInternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新内部损失成本构成图表\r\n          this.updateInternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取内部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新内部损失成本构成图表\r\n    updateInternalCostDetailChart(data) {\r\n      if (this.charts.internalCostDetailChart && data) {\r\n        console.log('接收到的内部损失成本数据:', data);\r\n\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.contractionLoss) {\r\n          Object.entries(data.contractionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.rescueCost) {\r\n          Object.entries(data.rescueCost).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.revisionLoss) {\r\n          Object.entries(data.revisionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.scrapLoss) {\r\n          Object.entries(data.scrapLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        console.log('收集到的所有数据项（包含0值）:', allDataItems);\r\n\r\n        // 按数值从高到低排序（0值会排在负值之前，正值之后）\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        console.log('排序后的数据（包含0值）:', allDataItems);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        console.log('y轴数据:', yAxisData);\r\n        console.log('系列数据（包含0值）:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.internalCostDetailChart.setOption(option);\r\n        console.log('内部损失成本构成图表数据已更新（包含0值，按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    // 更新外部损失成本构成图表\r\n    updateExternalCostDetailChart(data) {\r\n      if (this.charts.externalCostDetailChart && data) {\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#FCA5A5', '#FDE68A', '#86EFAC', '#93C5FD', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.customerClaimCost) {\r\n          Object.entries(data.customerClaimCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionFeeCost) {\r\n          Object.entries(data.qualityObjectionFeeCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionTravelCost) {\r\n          Object.entries(data.qualityObjectionTravelCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.returnLoss) {\r\n          Object.entries(data.returnLoss).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        // 按数值从高到低排序\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.externalCostDetailChart.setOption(option);\r\n        console.log('外部损失成本构成图表数据已更新（已按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    getQualityCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityCostDetail(params).then(response => {\r\n        console.log('getQualityCostDetail:', response);\r\n        if (response.data) {\r\n          this.qualityCostData = response.data.qualityCostData;\r\n          this.qualityCostDetail = response.data;\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getMultiLineChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getMultiLineChartData(params).then(response => {\r\n        console.log('getMultiLineChartData:', response);\r\n        if (response.data) {\r\n          this.updateMultiLineChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    getComboChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getComboChartDetail(params).then(response => {\r\n        console.log('getComboChartDetail:', response);\r\n        if (response.data) {\r\n          this.updateComboChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取ComboChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新ComboChart图表\r\n    updateComboChart(data) {\r\n      if (this.charts.comboChart && data) {\r\n        console.log('接收到的ComboChart数据:', data);\r\n\r\n        // 基于会计期生成近6个月的月份标签作为x轴数据\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        console.log('生成的月份标签:', months);\r\n\r\n        // 生成对应的年月格式用于数据匹配\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n        console.log('生成的年月格式:', yearMonths);\r\n\r\n        const failureCostData = [];     // 失败成本数据\r\n        const controllingCostData = []; // 控制成本数据\r\n\r\n        // 为每个月份提取对应的数值，转换为万元\r\n        yearMonths.forEach(yearMonth => {\r\n          // 获取失败成本数据，转换为万元\r\n          const failureValue = data.failureCostMap && data.failureCostMap[yearMonth]\r\n            ? ((Number(data.failureCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          failureCostData.push(failureValue);\r\n\r\n          // 获取控制成本数据，转换为万元\r\n          const controllingValue = data.controllingCostMap && data.controllingCostMap[yearMonth]\r\n            ? ((Number(data.controllingCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          controllingCostData.push(controllingValue);\r\n        });\r\n\r\n        console.log('x轴月份数据:', months.map(month => `${month}月`));\r\n        console.log('失败成本数据:', failureCostData);\r\n        console.log('控制成本数据:', controllingCostData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          // 图例配置 - 标注颜色对应的维度\r\n          legend: {\r\n            data: ['失败成本', '控制成本'], // 失败成本(红色#FCA5A5)，控制成本(绿色#86EFAC)\r\n            textStyle: { color: '#E5E7EB' }\r\n          },\r\n          grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 近6个月的月份\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '成本 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n\r\n          series: [\r\n            {\r\n              name: '失败成本', // 红色曲线 #FCA5A5\r\n              type: 'line',\r\n              data: failureCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '控制成本', // 绿色曲线 #86EFAC\r\n              type: 'line',\r\n              data: controllingCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.comboChart.setOption(option);\r\n        console.log('ComboChart图表数据已更新');\r\n      }\r\n    },\r\n\r\n    // 生成ComboChart的月份标签（当前月份和之前的5个月）\r\n    generateComboChartMonths() {\r\n      const months = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const month = date.getMonth() + 1;\r\n        months.push(month);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 生成对应的年月格式（当前月份和之前的5个月，如202501, 202502等）\r\n    generateYearMonths() {\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n        const yearMonth = `${year}${String(month).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    // 基于会计期生成ComboChart的月份标签（会计期当前月份和之前的5个月）\r\n    generateComboChartMonthsByAccountingPeriod() {\r\n      const months = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateComboChartMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const monthNum = date.getMonth() + 1;\r\n        months.push(monthNum);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 基于会计期生成对应的年月格式（会计期当前月份和之前的5个月）\r\n    generateYearMonthsByAccountingPeriod() {\r\n      const yearMonths = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateYearMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const yearNum = date.getFullYear();\r\n        const monthNum = date.getMonth() + 1;\r\n        const yearMonth = `${yearNum}${String(monthNum).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getPieChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n\r\n      getPieChartData(params).then(response => {\r\n        console.log('getPieChartData:', response);\r\n        if (response.data) {\r\n          this.updatePieChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          console.log('获取成本中心列表:', this.costCenterOptions);\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据刷新\r\n          this.$nextTick(() => {\r\n            this.refreshChartData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n\r\n    // 更新饼图数据\r\n    updatePieChart(data) {\r\n      if (this.charts.pieChart && data) {\r\n        // 更新饼图的数据，转换为万元\r\n        const option = this.charts.pieChart.getOption();\r\n        if (option && option.series && option.series[0]) {\r\n          option.series[0].data = [\r\n            { value: (data.preventionCost / 10000).toFixed(2), name: '预防成本', itemStyle: { color: '#93C5FD' } },\r\n            { value: (data.appraisalCost / 10000).toFixed(2), name: '鉴定成本', itemStyle: { color: '#86EFAC' } },\r\n            { value: (data.internalCost / 10000).toFixed(2), name: '内部损失成本', itemStyle: { color: '#FDE68A' } },\r\n            { value: (data.externalCost / 10000).toFixed(2), name: '外部损失成本', itemStyle: { color: '#FCA5A5' } },\r\n          ],\r\n            this.charts.pieChart.setOption(option);\r\n          // console.log('饼图数据已更新');\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新多线图数据\r\n    updateMultiLineChart(data) {\r\n      if (this.charts.multiLineChart && data) {\r\n        // 基于会计期生成月份标签和对应的年月数字\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n\r\n        // 处理各种成本数据，转换为万元\r\n        const preventionData = this.processMapData(data.preventionCostMap, yearMonths, true);\r\n        const appraisalData = this.processMapData(data.appraisalCostMap, yearMonths, true);\r\n        const internalData = this.processMapData(data.internalCostMap, yearMonths, true);\r\n        const externalData = this.processMapData(data.externalCostMap, yearMonths, true);\r\n\r\n        // 更新多线图的配置\r\n        const option = {\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 格式化为\"X月\"\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [\r\n            {\r\n              name: '预防成本',\r\n              type: 'line',\r\n              data: preventionData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#93C5FD', width: 3 },\r\n              itemStyle: { color: '#93C5FD' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '鉴定成本',\r\n              type: 'line',\r\n              data: appraisalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '内部损失成本',\r\n              type: 'line',\r\n              data: internalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FDE68A', width: 3 },\r\n              itemStyle: { color: '#FDE68A' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '外部损失成本',\r\n              type: 'line',\r\n              data: externalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.multiLineChart.setOption(option);\r\n        console.log('多线图数据已更新');\r\n      }\r\n    },\r\n\r\n    // 处理Map数据，根据年月匹配对应的值\r\n    processMapData(costMap, yearMonths, convertToWanYuan = false) {\r\n      if (!costMap) return new Array(yearMonths.length).fill(0);\r\n\r\n      return yearMonths.map(yearMonth => {\r\n        const value = costMap[yearMonth] || 0;\r\n        return convertToWanYuan ? (value / 10000).toFixed(2) : value;\r\n      });\r\n    },\r\n\r\n    // 生成月份标签（当前月份及前五个月份）\r\n    generateMonthLabels() {\r\n      const months = [];\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n\r\n        months.push(`${month}月`);\r\n        yearMonths.push(parseInt(`${year}${String(month).padStart(2, '0')}`));\r\n      }\r\n\r\n      return { months, yearMonths };\r\n    },\r\n\r\n    // 刷新图表数据\r\n    refreshChartData() {\r\n      // 只有当成本中心和会计期都有值时才刷新\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        return;\r\n      }\r\n\r\n      this.getQualityCostDetail();\r\n      this.getPieChartData();\r\n      this.getMultiLineChartData();\r\n      this.getExternalCostDetail();\r\n      this.getInternalCostDetail();\r\n      this.getComboChartDetail();\r\n      this.getWaterfallChartDetail();\r\n      this.getScrapLossChartDetailsDetail();\r\n      this.getQualityObjectionLossDetail();\r\n\r\n      // 这里可以添加其他图表的数据刷新\r\n      // this.$message.success(`已切换到成本中心: ${this.costCenter}, 会计期: ${this.accountingPeriod}`);\r\n    },\r\n\r\n    /** 查询按钮操作 */\r\n    handleQuery() {\r\n      this.refreshChartData();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      // 重置为默认值\r\n      if (this.costCenterOptions.length > 0) {\r\n        this.costCenter = this.costCenterOptions[0].key;\r\n      }\r\n\r\n      // 获取默认会计期\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth();\r\n      const prevMonth = month === 0 ? 12 : month;\r\n      const prevYear = month === 0 ? year - 1 : year;\r\n      this.accountingPeriod = `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n\r\n      this.$message.success('查询条件已重置');\r\n    },\r\n\r\n    initCharts() {\r\n      const THEME = 'dark'\r\n\r\n      // 定义商务风淡色系色彩方案\r\n      this.businessColors = {\r\n        light: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#7DD3FC', '#F9A8D4', '#BEF264'],\r\n        gradient: [\r\n          { offset: 0, color: '#3B82F6' },\r\n          { offset: 1, color: '#1E40AF' }\r\n        ]\r\n      }\r\n\r\n      // 初始化所有图表\r\n      this.charts.pieChart = echarts.init(this.$refs.pieChart, THEME)\r\n      this.charts.multiLineChart = echarts.init(this.$refs.multiLineChart, THEME)\r\n      this.charts.externalCostDetailChart = echarts.init(this.$refs.externalCostDetailChart, THEME)\r\n      this.charts.internalCostDetailChart = echarts.init(this.$refs.internalCostDetailChart, THEME)\r\n      this.charts.waterfallChart = echarts.init(this.$refs.waterfallChart, THEME)\r\n      this.charts.comboChart = echarts.init(this.$refs.comboChart, THEME)\r\n      this.charts.scrapLossChart = echarts.init(this.$refs.scrapLossChart, THEME)\r\n      this.charts.qualityObjectionChart = echarts.init(this.$refs.qualityObjectionChart, THEME)\r\n\r\n      // 配置所有图表\r\n      this.setPieChartOption()\r\n      this.setMultiLineChartOption()\r\n      this.setExternalCostDetailChartOption()\r\n      this.setInternalCostDetailChartOption()\r\n      this.setWaterfallChartOption()\r\n      this.setComboChartOption()\r\n      this.setScrapLossChartOption()\r\n      this.setQualityObjectionChartOption()\r\n    },\r\n\r\n    setPieChartOption() {\r\n      this.charts.pieChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: (params) => {\r\n            const value = parseFloat(params.value).toFixed(2);\r\n            const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n            return `${params.seriesName} <br/>${params.name}: ${formattedValue}万元 (${params.percent}%)`;\r\n          },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          top: 'bottom',\r\n          left: 'center',\r\n          textStyle: { color: '#E5E7EB', fontSize: 12 }\r\n        },\r\n        series: [{\r\n          name: '成本类别',\r\n          type: 'pie',\r\n          radius: '65%',\r\n          data: [],\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 15,\r\n              shadowOffsetX: 0,\r\n              shadowColor: 'rgba(147, 197, 253, 0.6)'\r\n            }\r\n          },\r\n          labelLine: { lineStyle: { color: '#9CA3AF' } },\r\n          label: {\r\n            color: '#E5E7EB',\r\n            formatter: (params) => {\r\n              const value = parseFloat(params.value).toFixed(2);\r\n              const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              return `${params.name}(${formattedValue}万元)`;\r\n            }\r\n          }\r\n        }]\r\n      })\r\n    },\r\n\r\n\r\n\r\n    setComboChartOption() {\r\n      this.charts.comboChart.setOption({\r\n        color: ['#FCA5A5', '#86EFAC'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['失败成本', '控制成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '失败成本', // 红色曲线 #FCA5A5\r\n            type: 'line',\r\n            data: [280, 260, 240, 220, 200, 180],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '控制成本', // 绿色曲线 #86EFAC\r\n            type: 'line',\r\n            data: [120, 125, 130, 135, 140, 145],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setMultiLineChartOption() {\r\n      this.charts.multiLineChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['预防成本', '鉴定成本', '内部损失成本', '外部损失成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '预防成本',\r\n            type: 'line',\r\n            data: [80, 82, 85, 88, 90, 95],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#93C5FD', width: 3 },\r\n            itemStyle: { color: '#93C5FD' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '鉴定成本',\r\n            type: 'line',\r\n            data: [120, 122, 125, 128, 130, 135],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '内部损失成本',\r\n            type: 'line',\r\n            data: [450, 430, 410, 380, 350, 320],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '外部损失成本',\r\n            type: 'line',\r\n            data: [350, 340, 310, 290, 260, 230],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    setParetoChartOption() {\r\n      this.charts.paretoChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        grid: { right: '20%' },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['产品报废', '产品改判', '设备故障', '工艺废料', '其他'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '损失金额(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '累计占比',\r\n            min: 0,\r\n            max: 100,\r\n            axisLabel: {\r\n              formatter: '{value} %',\r\n              color: '#9CA3AF'\r\n            },\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '损失金额',\r\n            type: 'bar',\r\n            data: [280, 110, 35, 20, 5],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '累计占比',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [62.2, 86.7, 94.4, 98.9, 100],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setExternalCostDetailChartOption() {\r\n      this.charts.externalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setInternalCostDetailChartOption() {\r\n      this.charts.internalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setWaterfallChartOption() {\r\n      this.charts.waterfallChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['初始成本', '修磨', '矫直', '探伤', '热处理', '总成本'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '辅助',\r\n            type: 'bar',\r\n            stack: '总量',\r\n            itemStyle: {\r\n              color: 'rgba(0,0,0,0)',\r\n              borderColor: 'rgba(0,0,0,0)',\r\n              borderWidth: 0\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: 'rgba(0,0,0,0)'\r\n              }\r\n            },\r\n            data: [0, 0, 50, 80, 105, 0]\r\n          },\r\n        ]\r\n      })\r\n    },\r\n\r\n    setScrapLossChartOption() {\r\n      this.charts.scrapLossChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '报废损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setQualityObjectionChartOption() {\r\n      this.charts.qualityObjectionChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '质量异议损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setDualYChartOption() {\r\n      this.charts.dualYChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['产量(吨)', '吨钢成本(元)'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '产量(吨)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '吨钢成本(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '产量(吨)',\r\n            type: 'bar',\r\n            data: [80000, 82000, 85000, 83000, 88000, 90000],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '吨钢成本(元)',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [13.1, 12.8, 12.5, 12.6, 12.2, 12.0],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    resizeCharts() {\r\n      Object.values(this.charts).forEach(chart => {\r\n        if (chart) {\r\n          chart.resize()\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.quality-cost-dashboard {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\r\n  background-color: #111827;\r\n  /* 深色背景 */\r\n  color: #d1d5db;\r\n  /* 浅色文字 */\r\n  margin: 0;\r\n  padding: 24px;\r\n  min-height: 100vh;\r\n}\r\n\r\n.header {\r\n  margin-bottom: 24px;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.header-wrapper {\r\n  display: inline-block;\r\n  position: relative;\r\n}\r\n\r\n.header h1 {\r\n  font-size: 28px;\r\n  color: #f9fafb;\r\n  /* 白色标题 */\r\n  font-weight: 600;\r\n  margin: 0;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.header p {\r\n  font-size: 16px;\r\n  color: #9ca3af;\r\n  /* 中灰色文字 */\r\n  margin: 8px 0 0 0;\r\n}\r\n\r\n.header-filters {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex-wrap: nowrap;\r\n  justify-content: flex-start;\r\n  margin-top: 12px;\r\n  margin-left: 950px;\r\n  /* 向左对齐 */\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.filter-item .label {\r\n  color: #d1d5db;\r\n  /* 浅色标签文字 */\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右上角筛选区域的样式 */\r\n.header-filters .el-input__inner,\r\n.header-filters .el-select .el-input__inner {\r\n  background-color: #111827; /* 与页面背景一致的深色 */\r\n  border-color: #374151;\r\n  color: #ffffff; /* 白色字体 */\r\n}\r\n\r\n.header-filters .el-input__inner:focus,\r\n.header-filters .el-select .el-input__inner:focus {\r\n  border-color: #93c5fd;\r\n  background-color: #111827; /* 聚焦时保持背景色 */\r\n}\r\n\r\n.header-filters .el-select-dropdown {\r\n  background-color: #111827; /* 下拉菜单背景与页面一致 */\r\n  border-color: #374151;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item {\r\n  color: #ffffff; /* 下拉选项白色字体 */\r\n  background-color: #111827;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item:hover {\r\n  background-color: #1f2937; /* 悬浮时稍微亮一点 */\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item.selected {\r\n  background-color: #374151; /* 选中项背景 */\r\n  color: #ffffff;\r\n}\r\n\r\n/* 下拉框箭头颜色 */\r\n.header-filters .el-select .el-input__suffix {\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select .el-select__caret {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 占位符文字颜色 */\r\n.header-filters .el-input__inner::placeholder {\r\n  color: #9ca3af;\r\n}\r\n\r\n/* 清除按钮颜色 */\r\n.header-filters .el-select .el-select__clear {\r\n  color: #9ca3af;\r\n}\r\n\r\n.header-filters .el-select .el-select__clear:hover {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n  .header-filters {\r\n    flex-wrap: wrap;\r\n    gap: 8px;\r\n    margin-left: 0;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header {\r\n    text-align: center;\r\n  }\r\n\r\n  .header-filters {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-left: 0;\r\n    /* 在小屏幕上取消左边距 */\r\n  }\r\n\r\n  .filter-item {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr); /* 改为四列布局，支持1/4和3/4分配 */\r\n  gap: 24px;\r\n}\r\n\r\n/* 响应式设计：在小屏幕上改为单列 */\r\n@media (max-width: 1200px) {\r\n  .dashboard-grid {\r\n    grid-template-columns: 1fr; /* 小屏幕时改为单列 */\r\n  }\r\n\r\n  /* 小屏幕时重置所有grid-column样式 */\r\n  .dashboard-grid .chart-container {\r\n    grid-column: 1 !important;\r\n  }\r\n}\r\n\r\n.chart-container,\r\n.kpi-card {\r\n  background-color: #1f2937;\r\n  /* 深色卡片背景 */\r\n  border-radius: 8px;\r\n  border: 1px solid #374151;\r\n  /* 边框 */\r\n  box-shadow: none;\r\n  padding: 24px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-container {\r\n  height: 400px;\r\n}\r\n\r\n/* 大图表样式 - 用于两两排列的图表 */\r\n.chart-container.large-chart {\r\n  height: 500px; /* 增加高度 */\r\n  min-height: 500px;\r\n}\r\n\r\n.chart-container h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #f9fafb;\r\n  /* 白色卡片标题 */\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  flex-grow: 1;\r\n}\r\n\r\n.kpi-grid {\r\n  grid-column: 1 / -1;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 24px;\r\n}\r\n\r\n.kpi-card {\r\n  justify-content: space-between;\r\n}\r\n\r\n.kpi-card .title {\r\n  font-size: 14px;\r\n  color: #9ca3af;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.kpi-card .value {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #f9fafb;\r\n}\r\n\r\n.kpi-card .comparison {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.kpi-card .comparison .arrow {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin-right: 4px;\r\n  stroke-width: 2.5px;\r\n}\r\n\r\n.kpi-card .comparison .positive {\r\n  color: #34d399;\r\n  /* 亮绿色 */\r\n}\r\n\r\n.kpi-card .comparison .negative {\r\n  color: #f87171;\r\n  /* 亮红色 */\r\n}\r\n</style>\r\n"]}]}