<template>
  <div class="app-container">
    <!-- 顶部筛选区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="会计期" prop="yearMonth">
        <el-date-picker
          v-model="queryParams.yearMonth"
          type="month"
          placeholder="请选择年月"
          format="yyyy-MM"
          value-format="yyyy-MM"
          size="small"
          style="width: 200px;">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="成本类型" prop="costType" v-if="showCostTypeSelect">
        <el-select
          v-model="queryParams.costType"
          placeholder="请选择成本类型"
          clearable
          size="small"
          style="width: 200px;">
          <el-option
            v-for="option in costTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport" :loading="loading">导出</el-button>
      </el-form-item>
    </el-form>

    <!-- 质量成本表格 -->
    <div class="table-container">
      <div class="table-header">
        <h3 class="table-title">各分厂{{ currentCostTypeTitle }}金额汇总</h3>
        <div class="table-unit-label">单位：元</div>
      </div>
      <div class="table-scroll-container">
        <vxe-table
          v-loading="loading"
          :data="manualTypeList"
          border
          :row-class-name="getRowClassName"
          :span-method="objectSpanMethod"
          :max-height="tableMaxHeight"
          :scroll-y="{enabled: true}"
          :scroll-x="{enabled: true}"
          header-align="center"
          class="cost-summary-table">
          <vxe-column
            title="成本类别"
            field="costType"
            align="center"
            width="100"
            fixed="left" />
          <vxe-column
            title="科目"
            field="typeName"
            align="center"
            width="200"
            fixed="left" />
          <vxe-column
            v-for="col in manualArray"
            :key="col"
            :title="col"
            :field="col"
            align="center"
            width="120">
            <template #default="{ row }">
              <div class="table-cell-content">
                <span v-if="row[col] && typeof row[col] === 'object'">
                  {{ formatNumber(row[col].costEx) }}
                </span>
                <span v-else>
                  {{ formatNumber(row[col]) }}
                </span>
              </div>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
  </div>
</template>

<script>
import { costCenterlist, listAllQualityCostDetail, exportFactoryCostSummary } from "@/api/qualityCost/qualityCostDetail";

export default {
  name: "factoryCostSummary",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 成本中心选项
      costCenterOptions: [],
      // 查询参数
      queryParams: {
        costCenterCname: null,
        yearMonth: null,
        costType: ''
      },
      // 表格相关数据
      manualArray: [],
      manualTypeList: [],
      currentCostTypeTitle: '',
      showCostTypeSelect: true,
      // 成本类型选项
      costTypeOptions: [
        { label: '全部', value: '' },
        { label: '预防成本', value: '预防成本' },
        { label: '鉴定成本', value: '鉴定成本' },
        { label: '内部损失成本', value: '内部损失成本' },
        { label: '外部损失成本', value: '外部损失成本' }
      ],
      // 表格头部样式
      tableHeaderStyle: {
        background: '#f5f7fa',
        color: '#303133'
      },
      // 表格最大高度
      tableMaxHeight: 800,
      // 类型名称到代码的映射
      typeNameToCodeMap: new Map([
        ['质量管理费', 'A1'],
        ['质量管理差旅费', 'A11'],
        ['质量管理会议费', 'A12'],
        ['质量管理其他费用', 'A3'],
        ['质量培训费', 'A2'],
        ['质量评审费', 'A3'],
        ['质量管理人员工资及附加', 'A4'],
        ['试验检验费（物料消耗）', 'B1'],
        ['第二、三方检测费', 'B2'],
        ['质量检测设备的购置费用', 'B31'],
        ['质量检测设备的维护费用', 'A32'],
        ['质量检测设备的折旧费用', 'B33'],
        ['质量检测人员工资及附加', 'B4'],
        ['产品报废损失', 'C1'],
        ['产品改判损失', 'C2'],
        ['产品脱合同损失', 'C3'],
        ['产品挽救处理项', 'C4'],
        ['质量异议退货损失', 'D1'],
        ['客户索赔费', 'D2'],
        ['质量异议运费', 'D3'],
        ['质量异议差旅费', 'D4']
      ])
    };
  },
  created() {
    this.initializeComponent();
  },
  mounted() {
    this.calculateTableHeight();
    window.addEventListener('resize', this.calculateTableHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight);
  },
  methods: {
    /** 初始化组件 */
    initializeComponent() {
      this.queryParams.yearMonth = this.getDefaultYearMonth();
      this.initializeCostType();
      this.getCostCenterList();
      this.getList();
    },

    /** 初始化成本类型 */
    initializeCostType() {
      const routeCostType = this.$route.query.costType || this.$route.params?.costType;
      if (routeCostType) {
        this.queryParams.costType = String(routeCostType).replace(/^['\"]|['\"]$/g, '');
        this.showCostTypeSelect = false;
      } else {
        this.queryParams.costType = '';
        this.showCostTypeSelect = true;
      }
    },

    /** 获取成本中心列表 */
    getCostCenterList() {
      costCenterlist().then(response => {
        const options = response.data || [];
        // 过滤掉value为"特板热处理分厂"的元素
        this.costCenterOptions = options.filter(item => item.value !== '特板热处理分厂');
        console.log('costCenterOptions:', this.costCenterOptions);
      }).catch(() => {
        this.$message.error('获取成本中心列表失败');
      });
    },

    /** 表格单元格合并方法 */
    objectSpanMethod({ row, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // "成本类别"列
        const costType = row.costType;
        const costTypeRows = this.manualTypeList.filter(item => item.costType === costType);
        const firstIndex = this.manualTypeList.findIndex(item => item.costType === costType);
        if (rowIndex === firstIndex) {
          return {
            rowspan: costTypeRows.length,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },

    /** 格式化数字 */
    formatNumber(num) {
      if (num === null || num === undefined || num === '' || Number(num) === 0) {
        return '-';
      }
      return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    /** 查询质量成本列表 */
    getList() {
      if (!this.queryParams.yearMonth) {
        this.$message.warning('请选择会计期');
        return;
      }

      this.loading = true;
      const formatYearMonth = this.queryParams.yearMonth.replace('-', '');
      const qualityCostDetail = {
        costCenterCname: this.queryParams.costCenterCname,
        yearMonth: formatYearMonth,
        costType: this.queryParams.costType
      };

      listAllQualityCostDetail(qualityCostDetail).then(response => {
        const rawData = response.data || [];
        this.currentCostTypeTitle = this.queryParams.costType || '';

        // 根据成本类型处理数据
        this.processDataByCostType(rawData);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.$message.error('查询失败');
      });
    },

    /** 根据成本类型处理数据 */
    processDataByCostType(rawData) {
      const costType = this.queryParams.costType;

      if (costType === '') {
        this.processAllCostTypes(rawData);
      } else if (costType === '预防成本') {
        this.processPreventionCost(rawData);
      } else if (costType === '鉴定成本') {
        this.processAppraisalCost(rawData);
      } else if (costType === '内部损失成本') {
        this.processInternalFailureCost(rawData);
      } else if (costType === '外部损失成本') {
        this.processExternalFailureCost(rawData);
      }
    },

    /** 处理全部成本类型数据 */
    processAllCostTypes(rawData) {
      this.manualTypeList = this.getAllCostTypesList();
      this.initializeManualTypeList();
      this.fillDataToManualTypeList(rawData);
      this.processTotalRows(rawData, 'Z');
      this.cleanupZeroValues();
      this.applyTypeNameMapping();
    },

    /** 获取全部成本类型列表 */
    getAllCostTypesList() {
      return [
        { typeName: '质量管理费', costType: "预防成本" },
        { typeName: '质量管理差旅费', costType: "预防成本" },
        { typeName: '质量管理会议费', costType: "预防成本" },
        { typeName: '质量管理其他费用', costType: "预防成本" },
        { typeName: '质量培训费', costType: "预防成本" },
        { typeName: '质量评审费', costType: "预防成本" },
        { typeName: '质量管理人员工资及附加', costType: "预防成本" },
        { typeName: '试验检验费（物料消耗）', costType: "鉴定成本" },
        { typeName: '第二、三方检测费', costType: "鉴定成本" },
        { typeName: '质量检测设备的购置、维护、折旧费用', costType: "鉴定成本" },
        { typeName: '质量检测设备的购置费用', costType: "鉴定成本" },
        { typeName: '质量检测设备的维护费用', costType: "鉴定成本" },
        { typeName: '质量检测设备的折旧费用', costType: "鉴定成本" },
        { typeName: '质量检测人员工资及附加', costType: "鉴定成本" },
        { typeName: '产品报废损失', costType: "内部损失成本" },
        { typeName: '产品报废量（普通类）', costType: "内部损失成本" },
        { typeName: '产品报废量（高钼钢）', costType: "内部损失成本" },
        { typeName: '产品报废量（高Ni钢）', costType: "内部损失成本" },
        { typeName: '产品报废量（高镍钼钢）', costType: "内部损失成本" },
        { typeName: '产品改判损失', costType: "内部损失成本" },
        { typeName: '产品脱合同损失', costType: "内部损失成本" },
        { typeName: '产品挽救处理项', costType: "内部损失成本" },
        { typeName: '质量异议退货损失', costType: "外部损失成本" },
        { typeName: '质量异议报废量（普通类）', costType: "外部损失成本" },
        { typeName: '质量异议报废量（高钼钢）', costType: "外部损失成本" },
        { typeName: '质量异议报废量（高Ni钢）', costType: "外部损失成本" },
        { typeName: '质量异议报废量（高镍钼钢）', costType: "外部损失成本" },
        { typeName: '客户索赔费', costType: "外部损失成本" },
        { typeName: '质量异议运费', costType: "外部损失成本" },
        { typeName: '质量异议差旅费', costType: "外部损失成本" },
        { typeName: '总金额', costType: "总计" },
        { typeName: '产量', costType: "总计" },
        { typeName: '吨钢成本', costType: "总计" }
      ];
    },

    /** 初始化手动类型列表 */
    initializeManualTypeList() {
      // 为每项动态添加所有成本中心字段，初始值为 null
      this.manualTypeList.forEach(typeItem => {
        this.costCenterOptions.forEach(center => {
          typeItem[center.value] = null;
        });
      });

      // 生成手动数组
      this.manualArray = [];
      if (this.manualTypeList.length > 0) {
        Object.keys(this.manualTypeList[0]).forEach(key => {
          if (key !== 'typeName' && key !== 'costType') {
            this.manualArray.push(key);
          }
        });
      }
    },

    /** 填充数据到手动类型列表 */
    fillDataToManualTypeList(rawData) {
      rawData.forEach(dataItem => {
        const manualItem = this.manualTypeList.find(typeItem => typeItem.typeName === dataItem.typeName);
        if (manualItem && dataItem.costCenterCname && manualItem.hasOwnProperty(dataItem.costCenterCname)) {
          manualItem[dataItem.costCenterCname] = {
            costEx: dataItem.allcEx,
            costTon: dataItem.allcTon,
            costPerEx: dataItem.allcPerEx
          };
        }
      });
    },

    /** 处理总计行数据 */
    processTotalRows(rawData, typeCode) {
      const tonTotalRow = this.manualTypeList.find(item => item.typeName === '产量');
      const priceTotalRow = this.manualTypeList.find(item => item.typeName === '吨钢成本');
      const amountTotalRow = this.manualTypeList.find(item => item.typeName === '总金额');
      const totalRows = rawData.filter(item => item.typeCode === typeCode);

      if (tonTotalRow || priceTotalRow || amountTotalRow) {
        this.manualArray.forEach(centerKey => {
          if (centerKey === 'typeName') return;
          const totalData = totalRows.find(z => z.costCenterCname === centerKey);
          if (tonTotalRow) tonTotalRow[centerKey] = totalData ? totalData.allcTon : null;
          if (priceTotalRow) priceTotalRow[centerKey] = totalData ? totalData.allcPerEx : null;
          if (amountTotalRow) amountTotalRow[centerKey] = totalData ? totalData.allcEx : null;
        });
      }
    },

    /** 清理零值 */
    cleanupZeroValues() {
      this.manualTypeList.forEach(typeItem => {
        this.manualArray.forEach(key => {
          if (typeItem[key] === 0) {
            typeItem[key] = null;
          }
        });
      });
    },

    /** 处理预防成本数据 */
    processPreventionCost(rawData) {
      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('A'));
      this.manualTypeList = [
        { typeName: '质量管理费', costType: "预防成本" },
        { typeName: '质量管理差旅费', costType: "预防成本" },
        { typeName: '质量管理会议费', costType: "预防成本" },
        { typeName: '质量管理其他费用', costType: "预防成本" },
        { typeName: '质量培训费', costType: "预防成本" },
        { typeName: '质量评审费', costType: "预防成本" },
        { typeName: '质量管理人员工资及附加', costType: "预防成本" },
        { typeName: '总金额', costType: "总计" },
        { typeName: '产量', costType: "总计" },
        { typeName: '吨钢成本', costType: "总计" }
      ];
      this.initializeManualTypeList();
      this.fillDataToManualTypeList(filteredRawData);
      this.processTotalRows(filteredRawData, 'A');
      this.cleanupZeroValues();
      this.applyTypeNameMapping();
    },

    /** 处理鉴定成本数据 */
    processAppraisalCost(rawData) {
      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('B'));
      this.manualTypeList = [
        { typeName: '试验检验费（物料消耗）', costType: "鉴定成本" },
        { typeName: '第二、三方检测费', costType: "鉴定成本" },
        { typeName: '质量检测设备的购置、维护、折旧费用', costType: "鉴定成本" },
        { typeName: '质量检测设备的购置费用', costType: "鉴定成本" },
        { typeName: '质量检测设备的维护费用', costType: "鉴定成本" },
        { typeName: '质量检测设备的折旧费用', costType: "鉴定成本" },
        { typeName: '质量检测人员工资及附加', costType: "鉴定成本" },
        { typeName: '总金额', costType: "总计" },
        { typeName: '产量', costType: "总计" },
        { typeName: '吨钢成本', costType: "总计" }
      ];
      this.initializeManualTypeList();
      this.fillDataToManualTypeList(filteredRawData);
      this.processTotalRows(filteredRawData, 'B');
      this.cleanupZeroValues();
      this.applyTypeNameMapping();
    },

    /** 处理内部损失成本数据 */
    processInternalFailureCost(rawData) {
      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('C'));
      this.manualTypeList = [
        { typeName: '产品报废损失', costType: "内部损失成本" },
        { typeName: '产品报废量（普通类）', costType: "内部损失成本" },
        { typeName: '产品报废量（高钼钢）', costType: "内部损失成本" },
        { typeName: '产品报废量（高Ni钢）', costType: "内部损失成本" },
        { typeName: '产品报废量（高镍钼钢）', costType: "内部损失成本" },
        { typeName: '产品改判损失', costType: "内部损失成本" },
        { typeName: '产品脱合同损失', costType: "内部损失成本" },
        { typeName: '产品挽救处理项', costType: "内部损失成本" },
        { typeName: '总金额', costType: "总计" },
        { typeName: '产量', costType: "总计" },
        { typeName: '吨钢成本', costType: "总计" }
      ];
      this.initializeManualTypeList();
      this.fillDataToManualTypeList(filteredRawData);
      this.processTotalRows(filteredRawData, 'C');
      this.cleanupZeroValues();
      this.applyTypeNameMapping();
    },

    /** 处理外部损失成本数据 */
    processExternalFailureCost(rawData) {
      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('D'));
      this.manualTypeList = [
        { typeName: '质量异议退货损失', costType: "外部损失成本" },
        { typeName: '质量异议报废量（普通类）', costType: "外部损失成本" },
        { typeName: '质量异议报废量（高钼钢）', costType: "外部损失成本" },
        { typeName: '质量异议报废量（高Ni钢）', costType: "外部损失成本" },
        { typeName: '质量异议报废量（高镍钼钢）', costType: "外部损失成本" },
        { typeName: '客户索赔费', costType: "外部损失成本" },
        { typeName: '质量异议运费', costType: "外部损失成本" },
        { typeName: '质量异议差旅费', costType: "外部损失成本" },
        { typeName: '总金额', costType: "总计" },
        { typeName: '产量', costType: "总计" },
        { typeName: '吨钢成本', costType: "总计" }
      ];
      this.initializeManualTypeList();
      this.fillDataToManualTypeList(filteredRawData);
      this.processTotalRows(filteredRawData, 'D');
      this.cleanupZeroValues();
      this.applyTypeNameMapping();
    },

    /** 应用类型名称映射 */
    applyTypeNameMapping() {
      const renameMap = {
        "质量管理费": "一、质量管理费",
        "质量管理差旅费": "1.差旅费",
        "质量管理会议费": "2.会议费",
        "质量管理其他费用": "3.其他费用",
        "质量培训费": "二、质量培训费",
        "质量评审费": "三、质量评审费",
        "质量管理人员工资及附加": "四、质量管理人员工资及附加",
        "试验检验费（物料消耗）": "一、试验检验费",
        "第二、三方检测费": "二、外部检测费",
        "质量检测设备的购置、维护、折旧费用": "三、质量检测设备费用",
        "质量检测设备的购置费用": "1.购置费用",
        "质量检测设备的维护费用": "2.维护费用",
        "质量检测设备的折旧费用": "3.折旧费用",
        "质量检测人员工资及附加": "四、质量检测人员工资及附加",
        "产品报废损失": "一、产品报废损失",
        "产品报废量（普通类）": "1.产品报废量（普通类）",
        "产品报废量（高钼钢）": "2.产品报废量（高钼钢）",
        "产品报废量（高Ni钢）": "3.产品报废量（高Ni钢）",
        "产品报废量（高镍钼钢）": "4.产品报废量（高镍钼钢）",
        "产品改判损失": "二、产品改判损失",
        "产品脱合同损失": "三、产品脱合同损失",
        "产品挽救处理项": "四、产品挽救处理项",
        "质量异议退货损失": "一、质量异议退货损失",
        "质量异议报废量（普通类）": "1.质量异议报废量（普通类）",
        "质量异议报废量（高钼钢）": "2.质量异议报废量（高钼钢）",
        "质量异议报废量（高Ni钢）": "3.质量异议报废量（高Ni钢）",
        "质量异议报废量（高镍钼钢）": "4.质量异议报废量（高镍钼钢）",
        "客户索赔费": "二、客户索赔费",
        "质量异议运费": "三、质量异议运费",
        "质量异议差旅费": "四、质量异议差旅费"
      };

      this.manualTypeList.forEach(item => {
        if (renameMap[item.typeName]) {
          item.typeName = renameMap[item.typeName];
        }
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.yearMonth = this.getDefaultYearMonth();
      this.queryParams.costType = '';
      this.manualTypeList = [];
    },

    /** 获取默认会计期 */
   getDefaultYearMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1; // 1-12
      const day = now.getDate();
      const hour = now.getHours();

      // 如果今天是本月25号8点前（含25号7:59），则用上个月
      if (day < 28 || (day === 28 && hour < 1)) {
        // 处理1月时的跨年
        const prevMonth = month === 1 ? 12 : month - 1;
        const prevYear = month === 1 ? year - 1 : year;
        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;
      } else {
        return `${year}-${String(month).padStart(2, '0')}`;
      }
    },

    /** 获取行样式类名 */
    getRowClassName({ row }) {
      return row.isSummary ? 'summary-row' : '';
    },

    /** 计算表格高度 */
    calculateTableHeight() {
      this.$nextTick(() => {
        const windowHeight = window.innerHeight;
        // 减去页面头部、筛选区域、标题等高度，大约180px
        const availableHeight = windowHeight - 180;
        // 设置表格最大高度，最小500px，最大不超过可用高度的85%
        this.tableMaxHeight = Math.max(500, Math.min(800, availableHeight * 0.85));
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      console.log('=== 导出按钮被点击 ===');
      console.log('manualTypeList:', this.manualTypeList);
      console.log('manualTypeList长度:', this.manualTypeList ? this.manualTypeList.length : 'undefined');
      console.log('queryParams:', this.queryParams);

      // 检查是否有数据
      if (!this.manualTypeList || this.manualTypeList.length === 0) {
        this.$message.warning('暂无数据可导出，请先查询数据');
        return;
      }

      // 检查会计期是否存在
      if (!this.queryParams.yearMonth) {
        this.$message.warning('请先选择会计期');
        return;
      }

      // 构建请求数据，确保日期格式与查询数据时保持一致
      const requestData = {
        costCenterCname: this.queryParams.costCenterCname,
        yearMonth: this.queryParams.yearMonth.replace('-', ''), // 将 2025-06 转换为 202506
        costType: this.queryParams.costType,
        // 传递处理后的表格数据
        manualTypeList: this.manualTypeList,
        manualArray: this.manualArray,
        currentCostTypeTitle: this.currentCostTypeTitle
      };

      console.log('请求数据:', requestData);

      this.$confirm('是否确认导出工厂成本汇总表数据?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        console.log('用户确认导出');
        this.loading = true;
        return exportFactoryCostSummary(requestData);
      }).then(response => {
        console.log('导出响应:', response);
        this.download(response.msg);
        this.loading = false;
      }).catch(error => {
        console.error('导出失败:', error);
        this.loading = false;
        this.$message.error('导出失败：' + (error.message || '未知错误'));
      });
    }
  },
  watch: {
    $route() {
      this.initializeCostType();
      this.getCostCenterList();
      this.getList();
    }
  }
};
</script>

<style scoped>
</style>

<style>
/* 非scoped样式，确保能够覆盖vxe-table的默认样式 */

/* 强制覆盖所有滚动条样式 - 15px粗细，淡蓝色主题 */
.cost-summary-table ::-webkit-scrollbar {
  width: 15px !important;
  height: 15px !important;
}

.cost-summary-table ::-webkit-scrollbar-track {
  background: #e3f2fd !important;
  border-radius: 7px !important;
}

.cost-summary-table ::-webkit-scrollbar-thumb {
  background: #64b5f6 !important;
  border-radius: 7px !important;
  border: none !important;
}

.cost-summary-table ::-webkit-scrollbar-thumb:hover {
  background: #42a5f5 !important;
}

.cost-summary-table ::-webkit-scrollbar-thumb:active {
  background: #2196f3 !important;
}

.cost-summary-table ::-webkit-scrollbar-corner {
  background: #e3f2fd !important;
}

/* 更具体的选择器，针对vxe-table */
.cost-summary-table .vxe-table ::-webkit-scrollbar {
  width: 15px !important;
  height: 15px !important;
}

.cost-summary-table .vxe-table ::-webkit-scrollbar-track {
  background: #e3f2fd !important;
  border-radius: 7px !important;
}

.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb {
  background: #64b5f6 !important;
  border-radius: 7px !important;
  border: none !important;
}

.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb:hover {
  background: #42a5f5 !important;
}

.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb:active {
  background: #2196f3 !important;
}

/* Firefox 支持 */
.cost-summary-table .vxe-table {
  scrollbar-width: auto !important;
  scrollbar-color: #64b5f6 #e3f2fd !important;
}
</style>
/* 页面容器样式 */
.app-container {
  position: relative;
  min-height: 100vh;
  padding-bottom: 20px;
}

/* 表格容器样式 */
.table-container {
  margin-top: 20px;
}

/* 表格标题头部容器 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin: 20px 0;
}

.table-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin: 0;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

.table-scroll-container {
  width: calc(100vw - 280px);
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* 表格单位标签 */
.table-unit-label {
  position: fixed;
  top: 20px;
  right: 30px;
  font-size: 14px;
  color: #606266;
  font-weight: normal;
  white-space: nowrap;
  z-index: 1000;
  background-color: #fff;
  padding: 4px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格样式 */
.cost-summary-table {
  min-width: 800px;
  width: 100%;
}

.table-cell-content {
  padding: 10px 0;
}

/* 汇总行样式 */
.cost-summary-table ::v-deep .vxe-table--body .summary-row {
  background-color: #f8f9fa;
  font-weight: bold;
}

.cost-summary-table ::v-deep .vxe-table--body .summary-row .vxe-body--column {
  background-color: #f8f9fa !important;
}

/* vxe-table 滚动条样式优化 - 统一15px粗细，淡蓝色主题 */
/* 使用更高优先级的选择器 */
.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar,
.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar,
.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,
.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar {
  width: 15px !important;   /* 纵向滚动条宽度 - 调整为原来的三倍 */
  height: 15px !important;  /* 横向滚动条高度 - 调整为原来的三倍 */
}

.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-track,
.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-track,
.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-track,
.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-track {
  background: #e3f2fd !important;  /* 滚动条轨道背景色 - 淡蓝色背景 */
  border-radius: 7px !important;   /* 轨道圆角，调整为滚动条粗细的一半 */
}

.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb,
.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb,
.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb,
.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb {
  background: #64b5f6 !important;  /* 滚动条滑块颜色 - 淡蓝色 */
  border-radius: 7px !important;   /* 滑块圆角，调整为滚动条粗细的一半 */
  border: none !important;         /* 移除边框 */
}

.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,
.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,
.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,
.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #42a5f5 !important;  /* 鼠标悬停时的颜色 - 稍深的蓝色 */
}

.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,
.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,
.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,
.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active {
  background: #2196f3 !important;  /* 点击时的颜色 - 更深的蓝色 */
}

.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-corner,
.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-corner,
.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-corner,
.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-corner {
  background: #e3f2fd !important;  /* 滚动条交汇处的背景色 - 淡蓝色背景 */
}

/* 全局滚动条样式 - 确保覆盖所有可能的vxe-table滚动条 */
.cost-summary-table ::-webkit-scrollbar {
  width: 15px !important;
  height: 15px !important;
}

.cost-summary-table ::-webkit-scrollbar-track {
  background: #e3f2fd !important;
  border-radius: 7px !important;
}

.cost-summary-table ::-webkit-scrollbar-thumb {
  background: #64b5f6 !important;
  border-radius: 7px !important;
  border: none !important;
}

.cost-summary-table ::-webkit-scrollbar-thumb:hover {
  background: #42a5f5 !important;
}

.cost-summary-table ::-webkit-scrollbar-thumb:active {
  background: #2196f3 !important;
}

.cost-summary-table ::-webkit-scrollbar-corner {
  background: #e3f2fd !important;
}

/* vxe-table 固定列滚动条样式 - 保持与主表格一致的15px粗细，淡蓝色主题 */
/* 由于上面的全局样式已经覆盖了所有滚动条，这里保留作为备用 */
.cost-summary-table >>> .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,
.cost-summary-table >>> .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,
.cost-summary-table ::v-deep .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,
.cost-summary-table ::v-deep .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper::-webkit-scrollbar {
  width: 15px !important;   /* 固定列纵向滚动条宽度 - 调整为15px */
  height: 15px !important;  /* 固定列横向滚动条高度 - 调整为15px */
}

/* Firefox 滚动条样式 - 淡蓝色主题，较粗滚动条 */
.cost-summary-table ::v-deep .vxe-table--body-wrapper {
  scrollbar-width: auto;              /* Firefox滚动条粗细：auto(较粗) | thin(细) | none(隐藏) */
  scrollbar-color: #64b5f6 #e3f2fd;   /* Firefox滚动条颜色：滑块颜色(淡蓝) 轨道颜色(浅蓝) */
}

/* vxe-table 表格边框和样式 */
.cost-summary-table ::v-deep .vxe-table {
  border: 1px solid #ebeef5;
}

.cost-summary-table ::v-deep .vxe-table--border-line {
  border-color: #ebeef5;
}

/* 表头样式 */
.cost-summary-table ::v-deep .vxe-table--header-wrapper .vxe-table--header {
  background-color: #f5f7fa;
}

.cost-summary-table ::v-deep .vxe-table--header .vxe-header--column {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 500;
}
