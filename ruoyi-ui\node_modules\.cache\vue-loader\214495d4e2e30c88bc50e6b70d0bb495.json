{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\regradeDetail\\index.vue?vue&type=style&index=0&id=02a4eb2a&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\regradeDetail\\index.vue", "mtime": 1754372909833}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucmVncmFkZS1kZXRhaWwtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQoudGFibGUtdGl0bGUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi50YWJsZS10aXRsZSBoMiB7DQogIG1hcmdpbjogMDsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi50YWJsZS1oZWFkZXItaW5mbyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZzogMTBweCAwOw0KICBnYXA6IDI0cHg7DQp9DQoNCi5oZWFkZXItaXRlbSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5oZWFkZXItaXRlbSAubGFiZWwgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KfQ0KDQouaGVhZGVyLWl0ZW0gLnZhbHVlIHsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi5oZWFkZXItaXRlbTpmaXJzdC1jaGlsZCAubGFiZWwgew0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQouc2VhcmNoLWJhci1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIHBhZGRpbmc6IDEwcHggMDsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouYnV0dG9uLWdyb3VwIHsNCiAgbWFyZ2luLWxlZnQ6IGF1dG87DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogNHB4Ow0KfQ0KDQoubWFpbi10YWJsZSB7DQogIG1hcmdpbi1ib3R0b206IDA7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQouc3VidG90YWwtc2VjdGlvbiB7DQogIG1hcmdpbi10b3A6IC0xcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQovKiDooajmoLzmoLflvI/lrprliLYgKi8NCi5yZWdyYWRlLWRldGFpbC10YWJsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgdGFibGUtbGF5b3V0OiBmaXhlZCAhaW1wb3J0YW50Ow0KICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KfQ0KDQoucmVncmFkZS1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXItd3JhcHBlcikgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQoucmVncmFkZS1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXIgdGgpIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBwYWRkaW5nOiAxMnB4IDA7DQogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQp9DQoNCi5yZWdyYWRlLWRldGFpbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdGQpIHsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgcGFkZGluZzogMTJweCA4cHg7DQp9DQoNCi5yZWdyYWRlLWRldGFpbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdHI6bnRoLWNoaWxkKG9kZCkpIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTsNCn0NCg0KLnJlZ3JhZGUtZGV0YWlsLXRhYmxlIDpkZWVwKC5lbC10YWJsZV9fYm9keSB0cjpob3Zlcikgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmOWZmOw0KfQ0KDQovKiDlsI/orqHooajmoLzmoLflvI8gKi8NCi5zdWJ0b3RhbC10YWJsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgdGFibGUtbGF5b3V0OiBmaXhlZCAhaW1wb3J0YW50Ow0KICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KfQ0KDQouc3VidG90YWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXIpIHsNCiAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQouc3VidG90YWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXItd3JhcHBlcikgew0KICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5zdWJ0b3RhbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdHIpIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZjsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5zdWJ0b3RhbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdGQpIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZiAhaW1wb3J0YW50Ow0KICBwYWRkaW5nOiAxMnB4IDhweDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCg0KLyog56Gu5L+d6KGo5qC85a655Zmo5a+56b2QICovDQoubWFpbi10YWJsZSwNCi5zdWJ0b3RhbC1zZWN0aW9uIHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5tYWluLXRhYmxlIC5lbC10YWJsZSwNCi5zdWJ0b3RhbC1zZWN0aW9uIC5lbC10YWJsZSB7DQogIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOagh+etvuagt+W8jyAqLw0KLmVsLXRhZyB7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLyog5ZON5bqU5byP6K6+6K6hICovDQpAbWVkaWEgKG1heC13aWR0aDogMTQwMHB4KSB7DQogIC50YWJsZS1oZWFkZXItaW5mbyB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDEwcHg7DQogICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogIH0NCg0KICAucmVncmFkZS1kZXRhaWwtdGFibGUgew0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgfQ0KDQogIC5yZWdyYWRlLWRldGFpbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdGQpIHsNCiAgICBwYWRkaW5nOiA4cHggMDsNCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLnJlZ3JhZGUtZGV0YWlsLWNvbnRhaW5lciB7DQogICAgcGFkZGluZzogMTBweDsNCiAgfQ0KDQogIC5tYWluLXRhYmxlIHsNCiAgICBvdmVyZmxvdy14OiBhdXRvOw0KICB9DQp9DQoNCi8qIOaQnOe0ouWMuuWfn+agt+W8jyAqLw0KLmhlYWRlci1pdGVtIC5lbC1pbnB1dCB7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KfQ0KDQouaGVhZGVyLWl0ZW0gLmVsLWJ1dHRvbiB7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KfQ0KDQouaGVhZGVyLWl0ZW0gLmVsLWJ1dHRvbjpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLXJpZ2h0OiAwOw0KfQ0KDQovKiAzNjDmtY/op4jlmajlhbzlrrnmgKfkv67lpI0gKi8NCi5yZWdyYWRlLWRldGFpbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2hlYWRlciksDQoucmVncmFkZS1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19ib2R5KSB7DQogIHRhYmxlLWxheW91dDogZml4ZWQ7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQoucmVncmFkZS1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXIgY29sZ3JvdXApLA0KLnJlZ3JhZGUtZGV0YWlsLXRhYmxlIDpkZWVwKC5lbC10YWJsZV9fYm9keSBjb2xncm91cCkgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnN1YnRvdGFsLXRhYmxlIDpkZWVwKC5lbC10YWJsZV9fYm9keSkgew0KICB0YWJsZS1sYXlvdXQ6IGZpeGVkOw0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnN1YnRvdGFsLXRhYmxlIDpkZWVwKC5lbC10YWJsZV9fYm9keSBjb2xncm91cCkgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLyog5by65Yi26KGo5qC85YiX5a695bqm5ZCM5q2lICovDQoucmVncmFkZS1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXIgdGgpLA0KLnJlZ3JhZGUtZGV0YWlsLXRhYmxlIDpkZWVwKC5lbC10YWJsZV9fYm9keSB0ZCkgew0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCn0NCg0KLyog5L+u5aSN6L655qGG5a+56b2Q6Zeu6aKYICovDQoucmVncmFkZS1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlKSwNCi5zdWJ0b3RhbC10YWJsZSA6ZGVlcCguZWwtdGFibGUpIHsNCiAgYm9yZGVyLWNvbGxhcHNlOiBzZXBhcmF0ZTsNCiAgYm9yZGVyLXNwYWNpbmc6IDA7DQp9DQoNCi5yZWdyYWRlLWRldGFpbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2hlYWRlciB0aCksDQoucmVncmFkZS1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19ib2R5IHRkKSwNCi5zdWJ0b3RhbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdGQpIHsNCiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2ViZWVmNTsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi5yZWdyYWRlLWRldGFpbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2hlYWRlciB0aDpsYXN0LWNoaWxkKSwNCi5yZWdyYWRlLWRldGFpbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdGQ6bGFzdC1jaGlsZCksDQouc3VidG90YWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19ib2R5IHRkOmxhc3QtY2hpbGQpIHsNCiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2ViZWVmNTsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAscA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/regradeDetail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"regrade-detail-container\">\r\n      <!-- 表格标题 -->\r\n      <div class=\"table-title\">\r\n        <h2>兴澄特钢质量成本表-产品改判损失</h2>\r\n      </div>\r\n\r\n      <!-- 表格头部信息 -->\r\n      <div class=\"table-header-info\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">成本中心名称：</span>\r\n          <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\">\r\n            <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">会计期：</span>\r\n          <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"2025-06\" format=\"yyyy-MM\"\r\n            value-format=\"yyyy-MM\" style=\"width: 150px;\">\r\n          </el-date-picker>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">是否计划内：</span>\r\n          <el-select v-model=\"planFlag\" placeholder=\"全部\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in planFlagOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"search-bar-row\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判前钢种：</span>\r\n          <el-input v-model=\"searchParams.oldSgSign\" placeholder=\"请输入改判前钢种\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判前标准：</span>\r\n          <el-input v-model=\"searchParams.oldSgStd\" placeholder=\"请输入改判前标准\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判后钢种：</span>\r\n          <el-input v-model=\"searchParams.newSgSign\" placeholder=\"请输入改判后钢种\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判后标准：</span>\r\n          <el-input v-model=\"searchParams.newSgStd\" placeholder=\"请输入改判后标准\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">改判原因：</span>\r\n          <el-input v-model=\"searchParams.reason\" placeholder=\"请输入改判原因\" style=\"width: 170px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">搜索模式：</span>\r\n          <el-select v-model=\"searchParams.searchMode\" placeholder=\"请选择搜索模式\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in searchModeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item button-group\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\" size=\"small\">搜索</el-button>\r\n          <el-button @click=\"handleReset\" size=\"small\">重置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主表格 -->\r\n      <div class=\"main-table\">\r\n        <el-table :data=\"tableData\" border style=\"width: 100%; table-layout: fixed;\" class=\"regrade-detail-table\" v-loading=\"tableLoading\"\r\n          element-loading-text=\"加载中...\">\r\n          <el-table-column prop=\"oldSgSign\" label=\"改判前钢种\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"oldSgStd\" label=\"改判前标准\" align=\"center\" width=\"150\" />\r\n          <el-table-column prop=\"newSgSign\" label=\"改判后钢种\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"newSgStd\" label=\"改判后标准\" align=\"center\" width=\"150\" />\r\n          <el-table-column prop=\"crShp\" label=\"截面\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"thick\" label=\"厚度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.thick !== null && scope.row.thick !== undefined\">\r\n                {{ scope.row.thick }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"width\" label=\"宽度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.width !== null && scope.row.width !== undefined\">\r\n                {{ scope.row.width }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"len\" label=\"长度\" align=\"center\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.len !== null && scope.row.len !== undefined\">\r\n                {{ scope.row.len }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costPerTon\" label=\"售价差（元/吨）\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costPerTon !== null && scope.row.costPerTon !== undefined\">\r\n                {{ formatNumber(scope.row.costPerTon) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costTon\" label=\"吨位\" align=\"right\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costTon !== null && scope.row.costTon !== undefined\">\r\n                {{ formatNumber(scope.row.costTon, 2) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"costEx\" label=\"损失金额（元）\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costEx !== null && scope.row.costEx !== undefined\">\r\n                {{ formatNumber(scope.row.costEx) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"currStock\" label=\"当前库存\" align=\"center\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.currStock !== null && scope.row.currStock !== undefined && scope.row.currStock !== ''\">\r\n                {{ convertStockArea(scope.row.currStock) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"reason\" label=\"改判原因\" align=\"center\" min-width=\"120\" />\r\n          <el-table-column prop=\"planFlag\" label=\"是否计划内\" align=\"center\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"getPlanFlagTagType(scope.row.planFlag)\">\r\n                {{ getPlanFlagValue(scope.row.planFlag) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 小计行 -->\r\n      <div class=\"subtotal-section\">\r\n        <el-table :data=\"subtotalData\" border style=\"width: 100%; table-layout: fixed;\" class=\"subtotal-table\" :show-header=\"false\"\r\n          :span-method=\"subtotalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" width=\"150\"/>\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" width=\"120\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" width=\"150\"/>\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" width=\"80\" />\r\n          <el-table-column prop=\"empty8\" label=\"\" align=\"center\" width=\"130\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalPriceDifference\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalPriceDifference) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\" width=\"130\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty9\" label=\"\" align=\"center\" width=\"100\" />\r\n          <el-table-column prop=\"empty10\" label=\"\" align=\"center\" min-width=\"120\" />\r\n          <el-table-column prop=\"empty11\" label=\"\" align=\"center\" width=\"100\" />\r\n        </el-table>\r\n      </div>\r\n      <pagination :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"fetchTableData\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { listAllRegradeDetail, getSum } from \"@/api/qualityCost/regradeDetail\";\r\n\r\nexport default {\r\n  name: \"RegradeDetail\",\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: this.getDefaultYearMonth(),\r\n      // 新增：是否计划内筛选\r\n      planFlag: '1',\r\n      planFlagOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '是', value: '1' },\r\n        { label: '否', value: '0' }\r\n      ],\r\n      // 表格数据\r\n      tableData: [],\r\n      // 表格加载状态\r\n      tableLoading: false,\r\n      // 成本中心列表\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      sumData: {},\r\n      total: 0,\r\n      // 新增：搜索参数\r\n      searchParams: {\r\n        oldSgSign: '',\r\n        oldSgStd: '',\r\n        newSgSign: '',\r\n        newSgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      },\r\n      searchModeOptions: [\r\n        { label: '模糊搜索', value: '模糊搜索' },\r\n        { label: '精确搜索', value: '精确搜索' }\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算小计数据\r\n    subtotalData() {\r\n      if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品改判损失小计\",\r\n          totalTonnage: 0,\r\n          totalPriceDifference: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.sumData.costTon;\r\n\r\n      const totalPriceDifference = this.sumData.costPerTon;\r\n\r\n      const totalAmount = this.sumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品改判损失小计\",\r\n        totalTonnage: totalTonnage,\r\n        totalPriceDifference: totalPriceDifference,\r\n        totalAmount: totalAmount\r\n      }];\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 新增：监听是否计划内变化\r\n    planFlag: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n  },\r\n  methods: {\r\n     /** 获取默认会计期 */\r\n      getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    // 库区数据转换\r\n    convertStockArea(stockArea) {\r\n      if (!stockArea) return '';\r\n\r\n      const areaStr = stockArea.toString().toUpperCase();\r\n\r\n      if (areaStr === 'P') {\r\n        return '坯料';\r\n      } else if (areaStr === 'M') {\r\n        return '在制品';\r\n      } else if (areaStr === 'C') {\r\n        return '成品';\r\n      } else {\r\n        return stockArea; // 如果不是P、M、C，返回原值\r\n      }\r\n    },\r\n\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据获取\r\n          this.$nextTick(() => {\r\n            this.fetchTableData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n\r\n    // 获取表格数据\r\n    fetchTableData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod) {\r\n        this.tableData = [];\r\n        return;\r\n      }\r\n\r\n      this.tableLoading = true;\r\n\r\n      // 当选择\"江阴兴澄特种钢铁\"时，查询所有数据（不传costCenter参数）\r\n      let costCenterParam = this.costCenter;\r\n      const selectedOption = this.costCenterOptions.find(item => item.key === this.costCenter);\r\n      if (selectedOption && selectedOption.label === '公司') {\r\n        costCenterParam = ''; // 设置为空字符串，查询所有数据\r\n      }\r\n\r\n      this.queryParams.costCenter = costCenterParam;\r\n      this.queryParams.yearMonth = this.accountingPeriod.replace('-', '');\r\n      this.queryParams.planFlag = this.planFlag;\r\n      this.queryParams.searchMode = this.searchParams.searchMode; // 添加搜索模式参数\r\n\r\n      // 合并搜索参数到查询参数\r\n      Object.assign(this.queryParams, this.searchParams);\r\n\r\n      console.log('查询参数:', this.queryParams);\r\n\r\n      listAllRegradeDetail(this.queryParams).then(response => {\r\n        //this.tableData = (response.rows || []).filter(item => item.costEx !== null && item.costEx !== undefined && item.costEx !== 0);\r\n        this.tableData = response.rows || [];\r\n        this.total = response.total || 0;\r\n      }).catch(error => {\r\n        console.error('获取改判损失数据失败:', error);\r\n        this.$message.error('获取改判损失数据失败');\r\n        this.tableData = [];\r\n        this.total = 0;\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getSum(this.queryParams).then(response => {\r\n        this.sumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.sumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n    },\r\n    // 处理计划内标志值\r\n    getPlanFlagValue(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return '否';\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return '是';\r\n      }\r\n      return '未知'; // 既不是0也不是1时显示未知\r\n    },\r\n    // 获取计划内标志标签类型\r\n    getPlanFlagTagType(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return 'danger'; // 绿色\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return 'success'; // 红色\r\n      }\r\n      return 'warning'; // 黄色（未知状态）\r\n    },\r\n    // 获取当前年月\r\n    getCurrentMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      return `${year}-${month}`;\r\n    },\r\n    // 格式化数字显示\r\n    formatNumber(value, decimals = 2) {\r\n      if (value === null || value === undefined) return '';\r\n      return Number(value).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: decimals,\r\n        maximumFractionDigits: decimals\r\n      });\r\n    },\r\n    subtotalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前8列为小计标签\r\n      if (columnIndex >= 0 && columnIndex <= 8) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 9\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 搜索按钮点击事件\r\n    handleSearch() {\r\n      this.queryParams.pageNum = 1; // 搜索时重置页码\r\n      this.fetchTableData();\r\n    },\r\n    // 重置按钮点击事件\r\n    handleReset() {\r\n      this.searchParams = {\r\n        oldSgSign: '',\r\n        oldSgStd: '',\r\n        newSgSign: '',\r\n        newSgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      };\r\n      this.queryParams.pageNum = 1; // 重置页码\r\n      this.fetchTableData();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.regrade-detail-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.table-header-info {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.header-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-item .label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .value {\r\n  color: #303133;\r\n}\r\n\r\n.header-item:first-child .label {\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.search-bar-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 12px;\r\n}\r\n\r\n.button-group {\r\n  margin-left: auto;\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.main-table {\r\n  margin-bottom: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.subtotal-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n/* 表格样式定制 */\r\n.regrade-detail-table {\r\n  font-size: 14px;\r\n  table-layout: fixed !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header-wrapper) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th) {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n  padding: 12px 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body td) {\r\n  box-sizing: border-box;\r\n  padding: 12px 8px;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body tr:nth-child(odd)) {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__body tr:hover) {\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n/* 小计表格样式 */\r\n.subtotal-table {\r\n  font-size: 14px;\r\n  table-layout: fixed !important;\r\n  width: 100% !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body tr) {\r\n  background-color: #f0f9ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body td) {\r\n  background-color: #f0f9ff !important;\r\n  padding: 12px 8px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 确保表格容器对齐 */\r\n.main-table,\r\n.subtotal-section {\r\n  width: 100%;\r\n}\r\n\r\n.main-table .el-table,\r\n.subtotal-section .el-table {\r\n  width: 100% !important;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1400px) {\r\n  .table-header-info {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .regrade-detail-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .regrade-detail-table :deep(.el-table__body td) {\r\n    padding: 8px 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .regrade-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .main-table {\r\n    overflow-x: auto;\r\n  }\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.header-item .el-input {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n/* 360浏览器兼容性修复 */\r\n.regrade-detail-table :deep(.el-table__header),\r\n.regrade-detail-table :deep(.el-table__body) {\r\n  table-layout: fixed;\r\n  width: 100%;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header colgroup),\r\n.regrade-detail-table :deep(.el-table__body colgroup) {\r\n  width: 100%;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body) {\r\n  table-layout: fixed;\r\n  width: 100%;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body colgroup) {\r\n  width: 100%;\r\n}\r\n\r\n/* 强制表格列宽度同步 */\r\n.regrade-detail-table :deep(.el-table__header th),\r\n.regrade-detail-table :deep(.el-table__body td) {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 修复边框对齐问题 */\r\n.regrade-detail-table :deep(.el-table),\r\n.subtotal-table :deep(.el-table) {\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th),\r\n.regrade-detail-table :deep(.el-table__body td),\r\n.subtotal-table :deep(.el-table__body td) {\r\n  border-right: 1px solid #ebeef5;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.regrade-detail-table :deep(.el-table__header th:last-child),\r\n.regrade-detail-table :deep(.el-table__body td:last-child),\r\n.subtotal-table :deep(.el-table__body td:last-child) {\r\n  border-right: 1px solid #ebeef5;\r\n}\r\n</style>\r\n"]}]}