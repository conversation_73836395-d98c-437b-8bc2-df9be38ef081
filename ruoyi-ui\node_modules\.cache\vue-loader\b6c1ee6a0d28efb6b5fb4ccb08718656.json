{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\factoryCostSummary\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\factoryCostSummary\\index.vue", "mtime": 1754372909832}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/factoryCostSummary", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 顶部筛选区域 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"会计期\" prop=\"yearMonth\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.yearMonth\"\r\n          type=\"month\"\r\n          placeholder=\"请选择年月\"\r\n          format=\"yyyy-MM\"\r\n          value-format=\"yyyy-MM\"\r\n          size=\"small\"\r\n          style=\"width: 200px;\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"成本类型\" prop=\"costType\" v-if=\"showCostTypeSelect\">\r\n        <el-select\r\n          v-model=\"queryParams.costType\"\r\n          placeholder=\"请选择成本类型\"\r\n          clearable\r\n          size=\"small\"\r\n          style=\"width: 200px;\">\r\n          <el-option\r\n            v-for=\"option in costTypeOptions\"\r\n            :key=\"option.value\"\r\n            :label=\"option.label\"\r\n            :value=\"option.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"loading\">导出</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 质量成本表格 -->\r\n    <div class=\"table-container\">\r\n      <div class=\"table-header\">\r\n        <h3 class=\"table-title\">各分厂{{ currentCostTypeTitle }}金额汇总</h3>\r\n        <div class=\"table-unit-label\">单位：元</div>\r\n      </div>\r\n      <div class=\"table-scroll-container\">\r\n        <vxe-table\r\n          v-loading=\"loading\"\r\n          :data=\"manualTypeList\"\r\n          border\r\n          :row-class-name=\"getRowClassName\"\r\n          :span-method=\"objectSpanMethod\"\r\n          :max-height=\"tableMaxHeight\"\r\n          :scroll-y=\"{enabled: true}\"\r\n          :scroll-x=\"{enabled: true}\"\r\n          header-align=\"center\"\r\n          class=\"cost-summary-table\">\r\n          <vxe-column\r\n            title=\"成本类别\"\r\n            field=\"costType\"\r\n            align=\"center\"\r\n            width=\"100\"\r\n            fixed=\"left\" />\r\n          <vxe-column\r\n            title=\"科目\"\r\n            field=\"typeName\"\r\n            align=\"center\"\r\n            width=\"200\"\r\n            fixed=\"left\" />\r\n          <vxe-column\r\n            v-for=\"col in manualArray\"\r\n            :key=\"col\"\r\n            :title=\"col\"\r\n            :field=\"col\"\r\n            align=\"center\"\r\n            width=\"120\">\r\n            <template #default=\"{ row }\">\r\n              <div class=\"table-cell-content\">\r\n                <span v-if=\"row[col] && typeof row[col] === 'object'\">\r\n                  {{ formatNumber(row[col].costEx) }}\r\n                </span>\r\n                <span v-else>\r\n                  {{ formatNumber(row[col]) }}\r\n                </span>\r\n              </div>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { costCenterlist, listAllQualityCostDetail, exportFactoryCostSummary } from \"@/api/qualityCost/qualityCostDetail\";\r\n\r\nexport default {\r\n  name: \"factoryCostSummary\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        costCenterCname: null,\r\n        yearMonth: null,\r\n        costType: ''\r\n      },\r\n      // 表格相关数据\r\n      manualArray: [],\r\n      manualTypeList: [],\r\n      currentCostTypeTitle: '',\r\n      showCostTypeSelect: true,\r\n      // 成本类型选项\r\n      costTypeOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '预防成本', value: '预防成本' },\r\n        { label: '鉴定成本', value: '鉴定成本' },\r\n        { label: '内部损失成本', value: '内部损失成本' },\r\n        { label: '外部损失成本', value: '外部损失成本' }\r\n      ],\r\n      // 表格头部样式\r\n      tableHeaderStyle: {\r\n        background: '#f5f7fa',\r\n        color: '#303133'\r\n      },\r\n      // 表格最大高度\r\n      tableMaxHeight: 800,\r\n      // 类型名称到代码的映射\r\n      typeNameToCodeMap: new Map([\r\n        ['质量管理费', 'A1'],\r\n        ['质量管理差旅费', 'A11'],\r\n        ['质量管理会议费', 'A12'],\r\n        ['质量管理其他费用', 'A3'],\r\n        ['质量培训费', 'A2'],\r\n        ['质量评审费', 'A3'],\r\n        ['质量管理人员工资及附加', 'A4'],\r\n        ['试验检验费（物料消耗）', 'B1'],\r\n        ['第二、三方检测费', 'B2'],\r\n        ['质量检测设备的购置费用', 'B31'],\r\n        ['质量检测设备的维护费用', 'A32'],\r\n        ['质量检测设备的折旧费用', 'B33'],\r\n        ['质量检测人员工资及附加', 'B4'],\r\n        ['产品报废损失', 'C1'],\r\n        ['产品改判损失', 'C2'],\r\n        ['产品脱合同损失', 'C3'],\r\n        ['产品挽救处理项', 'C4'],\r\n        ['质量异议退货损失', 'D1'],\r\n        ['客户索赔费', 'D2'],\r\n        ['质量异议运费', 'D3'],\r\n        ['质量异议差旅费', 'D4']\r\n      ])\r\n    };\r\n  },\r\n  created() {\r\n    this.initializeComponent();\r\n  },\r\n  mounted() {\r\n    this.calculateTableHeight();\r\n    window.addEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  methods: {\r\n    /** 初始化组件 */\r\n    initializeComponent() {\r\n      this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n      this.initializeCostType();\r\n      this.getCostCenterList();\r\n      this.getList();\r\n    },\r\n\r\n    /** 初始化成本类型 */\r\n    initializeCostType() {\r\n      const routeCostType = this.$route.query.costType || this.$route.params?.costType;\r\n      if (routeCostType) {\r\n        this.queryParams.costType = String(routeCostType).replace(/^['\\\"]|['\\\"]$/g, '');\r\n        this.showCostTypeSelect = false;\r\n      } else {\r\n        this.queryParams.costType = '';\r\n        this.showCostTypeSelect = true;\r\n      }\r\n    },\r\n\r\n    /** 获取成本中心列表 */\r\n    getCostCenterList() {\r\n      costCenterlist().then(response => {\r\n        const options = response.data || [];\r\n        // 过滤掉value为\"特板热处理分厂\"的元素\r\n        this.costCenterOptions = options.filter(item => item.value !== '特板热处理分厂');\r\n        console.log('costCenterOptions:', this.costCenterOptions);\r\n      }).catch(() => {\r\n        this.$message.error('获取成本中心列表失败');\r\n      });\r\n    },\r\n\r\n    /** 表格单元格合并方法 */\r\n    objectSpanMethod({ row, rowIndex, columnIndex }) {\r\n      if (columnIndex === 0) { // \"成本类别\"列\r\n        const costType = row.costType;\r\n        const costTypeRows = this.manualTypeList.filter(item => item.costType === costType);\r\n        const firstIndex = this.manualTypeList.findIndex(item => item.costType === costType);\r\n        if (rowIndex === firstIndex) {\r\n          return {\r\n            rowspan: costTypeRows.length,\r\n            colspan: 1\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 格式化数字 */\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '' || Number(num) === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 查询质量成本列表 */\r\n    getList() {\r\n      if (!this.queryParams.yearMonth) {\r\n        this.$message.warning('请选择会计期');\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      const formatYearMonth = this.queryParams.yearMonth.replace('-', '');\r\n      const qualityCostDetail = {\r\n        costCenterCname: this.queryParams.costCenterCname,\r\n        yearMonth: formatYearMonth,\r\n        costType: this.queryParams.costType\r\n      };\r\n\r\n      listAllQualityCostDetail(qualityCostDetail).then(response => {\r\n        const rawData = response.data || [];\r\n        this.currentCostTypeTitle = this.queryParams.costType || '';\r\n\r\n        // 根据成本类型处理数据\r\n        this.processDataByCostType(rawData);\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n        this.$message.error('查询失败');\r\n      });\r\n    },\r\n\r\n    /** 根据成本类型处理数据 */\r\n    processDataByCostType(rawData) {\r\n      const costType = this.queryParams.costType;\r\n\r\n      if (costType === '') {\r\n        this.processAllCostTypes(rawData);\r\n      } else if (costType === '预防成本') {\r\n        this.processPreventionCost(rawData);\r\n      } else if (costType === '鉴定成本') {\r\n        this.processAppraisalCost(rawData);\r\n      } else if (costType === '内部损失成本') {\r\n        this.processInternalFailureCost(rawData);\r\n      } else if (costType === '外部损失成本') {\r\n        this.processExternalFailureCost(rawData);\r\n      }\r\n    },\r\n\r\n    /** 处理全部成本类型数据 */\r\n    processAllCostTypes(rawData) {\r\n      this.manualTypeList = this.getAllCostTypesList();\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(rawData);\r\n      this.processTotalRows(rawData, 'Z');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 获取全部成本类型列表 */\r\n    getAllCostTypesList() {\r\n      return [\r\n        { typeName: '质量管理费', costType: \"预防成本\" },\r\n        { typeName: '质量管理差旅费', costType: \"预防成本\" },\r\n        { typeName: '质量管理会议费', costType: \"预防成本\" },\r\n        { typeName: '质量管理其他费用', costType: \"预防成本\" },\r\n        { typeName: '质量培训费', costType: \"预防成本\" },\r\n        { typeName: '质量评审费', costType: \"预防成本\" },\r\n        { typeName: '质量管理人员工资及附加', costType: \"预防成本\" },\r\n        { typeName: '试验检验费（物料消耗）', costType: \"鉴定成本\" },\r\n        { typeName: '第二、三方检测费', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的购置、维护、折旧费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的购置费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的维护费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的折旧费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测人员工资及附加', costType: \"鉴定成本\" },\r\n        { typeName: '产品报废损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（普通类）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高钼钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高Ni钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高镍钼钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品改判损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品脱合同损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品挽救处理项', costType: \"内部损失成本\" },\r\n        { typeName: '质量异议退货损失', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（普通类）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高钼钢）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高Ni钢）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高镍钼钢）', costType: \"外部损失成本\" },\r\n        { typeName: '客户索赔费', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议运费', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议差旅费', costType: \"外部损失成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n    },\r\n\r\n    /** 初始化手动类型列表 */\r\n    initializeManualTypeList() {\r\n      // 为每项动态添加所有成本中心字段，初始值为 null\r\n      this.manualTypeList.forEach(typeItem => {\r\n        this.costCenterOptions.forEach(center => {\r\n          typeItem[center.value] = null;\r\n        });\r\n      });\r\n\r\n      // 生成手动数组\r\n      this.manualArray = [];\r\n      if (this.manualTypeList.length > 0) {\r\n        Object.keys(this.manualTypeList[0]).forEach(key => {\r\n          if (key !== 'typeName' && key !== 'costType') {\r\n            this.manualArray.push(key);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 填充数据到手动类型列表 */\r\n    fillDataToManualTypeList(rawData) {\r\n      rawData.forEach(dataItem => {\r\n        const manualItem = this.manualTypeList.find(typeItem => typeItem.typeName === dataItem.typeName);\r\n        if (manualItem && dataItem.costCenterCname && manualItem.hasOwnProperty(dataItem.costCenterCname)) {\r\n          manualItem[dataItem.costCenterCname] = {\r\n            costEx: dataItem.allcEx,\r\n            costTon: dataItem.allcTon,\r\n            costPerEx: dataItem.allcPerEx\r\n          };\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 处理总计行数据 */\r\n    processTotalRows(rawData, typeCode) {\r\n      const tonTotalRow = this.manualTypeList.find(item => item.typeName === '产量');\r\n      const priceTotalRow = this.manualTypeList.find(item => item.typeName === '吨钢成本');\r\n      const amountTotalRow = this.manualTypeList.find(item => item.typeName === '总金额');\r\n      const totalRows = rawData.filter(item => item.typeCode === typeCode);\r\n\r\n      if (tonTotalRow || priceTotalRow || amountTotalRow) {\r\n        this.manualArray.forEach(centerKey => {\r\n          if (centerKey === 'typeName') return;\r\n          const totalData = totalRows.find(z => z.costCenterCname === centerKey);\r\n          if (tonTotalRow) tonTotalRow[centerKey] = totalData ? totalData.allcTon : null;\r\n          if (priceTotalRow) priceTotalRow[centerKey] = totalData ? totalData.allcPerEx : null;\r\n          if (amountTotalRow) amountTotalRow[centerKey] = totalData ? totalData.allcEx : null;\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 清理零值 */\r\n    cleanupZeroValues() {\r\n      this.manualTypeList.forEach(typeItem => {\r\n        this.manualArray.forEach(key => {\r\n          if (typeItem[key] === 0) {\r\n            typeItem[key] = null;\r\n          }\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 处理预防成本数据 */\r\n    processPreventionCost(rawData) {\r\n      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('A'));\r\n      this.manualTypeList = [\r\n        { typeName: '质量管理费', costType: \"预防成本\" },\r\n        { typeName: '质量管理差旅费', costType: \"预防成本\" },\r\n        { typeName: '质量管理会议费', costType: \"预防成本\" },\r\n        { typeName: '质量管理其他费用', costType: \"预防成本\" },\r\n        { typeName: '质量培训费', costType: \"预防成本\" },\r\n        { typeName: '质量评审费', costType: \"预防成本\" },\r\n        { typeName: '质量管理人员工资及附加', costType: \"预防成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(filteredRawData);\r\n      this.processTotalRows(filteredRawData, 'A');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 处理鉴定成本数据 */\r\n    processAppraisalCost(rawData) {\r\n      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('B'));\r\n      this.manualTypeList = [\r\n        { typeName: '试验检验费（物料消耗）', costType: \"鉴定成本\" },\r\n        { typeName: '第二、三方检测费', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的购置、维护、折旧费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的购置费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的维护费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的折旧费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测人员工资及附加', costType: \"鉴定成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(filteredRawData);\r\n      this.processTotalRows(filteredRawData, 'B');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 处理内部损失成本数据 */\r\n    processInternalFailureCost(rawData) {\r\n      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('C'));\r\n      this.manualTypeList = [\r\n        { typeName: '产品报废损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（普通类）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高钼钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高Ni钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高镍钼钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品改判损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品脱合同损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品挽救处理项', costType: \"内部损失成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(filteredRawData);\r\n      this.processTotalRows(filteredRawData, 'C');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 处理外部损失成本数据 */\r\n    processExternalFailureCost(rawData) {\r\n      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('D'));\r\n      this.manualTypeList = [\r\n        { typeName: '质量异议退货损失', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（普通类）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高钼钢）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高Ni钢）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高镍钼钢）', costType: \"外部损失成本\" },\r\n        { typeName: '客户索赔费', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议运费', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议差旅费', costType: \"外部损失成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(filteredRawData);\r\n      this.processTotalRows(filteredRawData, 'D');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 应用类型名称映射 */\r\n    applyTypeNameMapping() {\r\n      const renameMap = {\r\n        \"质量管理费\": \"一、质量管理费\",\r\n        \"质量管理差旅费\": \"1.差旅费\",\r\n        \"质量管理会议费\": \"2.会议费\",\r\n        \"质量管理其他费用\": \"3.其他费用\",\r\n        \"质量培训费\": \"二、质量培训费\",\r\n        \"质量评审费\": \"三、质量评审费\",\r\n        \"质量管理人员工资及附加\": \"四、质量管理人员工资及附加\",\r\n        \"试验检验费（物料消耗）\": \"一、试验检验费\",\r\n        \"第二、三方检测费\": \"二、外部检测费\",\r\n        \"质量检测设备的购置、维护、折旧费用\": \"三、质量检测设备费用\",\r\n        \"质量检测设备的购置费用\": \"1.购置费用\",\r\n        \"质量检测设备的维护费用\": \"2.维护费用\",\r\n        \"质量检测设备的折旧费用\": \"3.折旧费用\",\r\n        \"质量检测人员工资及附加\": \"四、质量检测人员工资及附加\",\r\n        \"产品报废损失\": \"一、产品报废损失\",\r\n        \"产品报废量（普通类）\": \"1.产品报废量（普通类）\",\r\n        \"产品报废量（高钼钢）\": \"2.产品报废量（高钼钢）\",\r\n        \"产品报废量（高Ni钢）\": \"3.产品报废量（高Ni钢）\",\r\n        \"产品报废量（高镍钼钢）\": \"4.产品报废量（高镍钼钢）\",\r\n        \"产品改判损失\": \"二、产品改判损失\",\r\n        \"产品脱合同损失\": \"三、产品脱合同损失\",\r\n        \"产品挽救处理项\": \"四、产品挽救处理项\",\r\n        \"质量异议退货损失\": \"一、质量异议退货损失\",\r\n        \"质量异议报废量（普通类）\": \"1.质量异议报废量（普通类）\",\r\n        \"质量异议报废量（高钼钢）\": \"2.质量异议报废量（高钼钢）\",\r\n        \"质量异议报废量（高Ni钢）\": \"3.质量异议报废量（高Ni钢）\",\r\n        \"质量异议报废量（高镍钼钢）\": \"4.质量异议报废量（高镍钼钢）\",\r\n        \"客户索赔费\": \"二、客户索赔费\",\r\n        \"质量异议运费\": \"三、质量异议运费\",\r\n        \"质量异议差旅费\": \"四、质量异议差旅费\"\r\n      };\r\n\r\n      this.manualTypeList.forEach(item => {\r\n        if (renameMap[item.typeName]) {\r\n          item.typeName = renameMap[item.typeName];\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n      this.queryParams.costType = '';\r\n      this.manualTypeList = [];\r\n    },\r\n\r\n    /** 获取默认会计期 */\r\n   getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    /** 获取行样式类名 */\r\n    getRowClassName({ row }) {\r\n      return row.isSummary ? 'summary-row' : '';\r\n    },\r\n\r\n    /** 计算表格高度 */\r\n    calculateTableHeight() {\r\n      this.$nextTick(() => {\r\n        const windowHeight = window.innerHeight;\r\n        // 减去页面头部、筛选区域、标题等高度，大约180px\r\n        const availableHeight = windowHeight - 180;\r\n        // 设置表格最大高度，最小500px，最大不超过可用高度的85%\r\n        this.tableMaxHeight = Math.max(500, Math.min(800, availableHeight * 0.85));\r\n      });\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      console.log('=== 导出按钮被点击 ===');\r\n      console.log('manualTypeList:', this.manualTypeList);\r\n      console.log('manualTypeList长度:', this.manualTypeList ? this.manualTypeList.length : 'undefined');\r\n      console.log('queryParams:', this.queryParams);\r\n\r\n      // 检查是否有数据\r\n      if (!this.manualTypeList || this.manualTypeList.length === 0) {\r\n        this.$message.warning('暂无数据可导出，请先查询数据');\r\n        return;\r\n      }\r\n\r\n      // 检查会计期是否存在\r\n      if (!this.queryParams.yearMonth) {\r\n        this.$message.warning('请先选择会计期');\r\n        return;\r\n      }\r\n\r\n      // 构建请求数据，确保日期格式与查询数据时保持一致\r\n      const requestData = {\r\n        costCenterCname: this.queryParams.costCenterCname,\r\n        yearMonth: this.queryParams.yearMonth.replace('-', ''), // 将 2025-06 转换为 202506\r\n        costType: this.queryParams.costType,\r\n        // 传递处理后的表格数据\r\n        manualTypeList: this.manualTypeList,\r\n        manualArray: this.manualArray,\r\n        currentCostTypeTitle: this.currentCostTypeTitle\r\n      };\r\n\r\n      console.log('请求数据:', requestData);\r\n\r\n      this.$confirm('是否确认导出工厂成本汇总表数据?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        console.log('用户确认导出');\r\n        this.loading = true;\r\n        return exportFactoryCostSummary(requestData);\r\n      }).then(response => {\r\n        console.log('导出响应:', response);\r\n        this.download(response.msg);\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('导出失败:', error);\r\n        this.loading = false;\r\n        this.$message.error('导出失败：' + (error.message || '未知错误'));\r\n      });\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.initializeCostType();\r\n      this.getCostCenterList();\r\n      this.getList();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n</style>\r\n\r\n<style>\r\n/* 非scoped样式，确保能够覆盖vxe-table的默认样式 */\r\n\r\n/* 强制覆盖所有滚动条样式 - 15px粗细，淡蓝色主题 */\r\n.cost-summary-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;\r\n}\r\n\r\n/* 更具体的选择器，针对vxe-table */\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n/* Firefox 支持 */\r\n.cost-summary-table .vxe-table {\r\n  scrollbar-width: auto !important;\r\n  scrollbar-color: #64b5f6 #e3f2fd !important;\r\n}\r\n</style>\r\n/* 页面容器样式 */\r\n.app-container {\r\n  position: relative;\r\n  min-height: 100vh;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 表格标题头部容器 */\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  position: relative;\r\n  margin: 20px 0;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  margin: 0;\r\n  position: absolute;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  white-space: nowrap;\r\n}\r\n\r\n.table-scroll-container {\r\n  width: calc(100vw - 280px);\r\n  position: relative;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格单位标签 */\r\n.table-unit-label {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 30px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: normal;\r\n  white-space: nowrap;\r\n  z-index: 1000;\r\n  background-color: #fff;\r\n  padding: 4px 12px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 表格样式 */\r\n.cost-summary-table {\r\n  min-width: 800px;\r\n  width: 100%;\r\n}\r\n\r\n.table-cell-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 汇总行样式 */\r\n.cost-summary-table ::v-deep .vxe-table--body .summary-row {\r\n  background-color: #f8f9fa;\r\n  font-weight: bold;\r\n}\r\n\r\n.cost-summary-table ::v-deep .vxe-table--body .summary-row .vxe-body--column {\r\n  background-color: #f8f9fa !important;\r\n}\r\n\r\n/* vxe-table 滚动条样式优化 - 统一15px粗细，淡蓝色主题 */\r\n/* 使用更高优先级的选择器 */\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar {\r\n  width: 15px !important;   /* 纵向滚动条宽度 - 调整为原来的三倍 */\r\n  height: 15px !important;  /* 横向滚动条高度 - 调整为原来的三倍 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;  /* 滚动条轨道背景色 - 淡蓝色背景 */\r\n  border-radius: 7px !important;   /* 轨道圆角，调整为滚动条粗细的一半 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;  /* 滚动条滑块颜色 - 淡蓝色 */\r\n  border-radius: 7px !important;   /* 滑块圆角，调整为滚动条粗细的一半 */\r\n  border: none !important;         /* 移除边框 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;  /* 鼠标悬停时的颜色 - 稍深的蓝色 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;  /* 点击时的颜色 - 更深的蓝色 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-corner,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-corner,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-corner,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;  /* 滚动条交汇处的背景色 - 淡蓝色背景 */\r\n}\r\n\r\n/* 全局滚动条样式 - 确保覆盖所有可能的vxe-table滚动条 */\r\n.cost-summary-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;\r\n}\r\n\r\n/* vxe-table 固定列滚动条样式 - 保持与主表格一致的15px粗细，淡蓝色主题 */\r\n/* 由于上面的全局样式已经覆盖了所有滚动条，这里保留作为备用 */\r\n.cost-summary-table >>> .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table >>> .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table ::v-deep .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table ::v-deep .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper::-webkit-scrollbar {\r\n  width: 15px !important;   /* 固定列纵向滚动条宽度 - 调整为15px */\r\n  height: 15px !important;  /* 固定列横向滚动条高度 - 调整为15px */\r\n}\r\n\r\n/* Firefox 滚动条样式 - 淡蓝色主题，较粗滚动条 */\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper {\r\n  scrollbar-width: auto;              /* Firefox滚动条粗细：auto(较粗) | thin(细) | none(隐藏) */\r\n  scrollbar-color: #64b5f6 #e3f2fd;   /* Firefox滚动条颜色：滑块颜色(淡蓝) 轨道颜色(浅蓝) */\r\n}\r\n\r\n/* vxe-table 表格边框和样式 */\r\n.cost-summary-table ::v-deep .vxe-table {\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.cost-summary-table ::v-deep .vxe-table--border-line {\r\n  border-color: #ebeef5;\r\n}\r\n\r\n/* 表头样式 */\r\n.cost-summary-table ::v-deep .vxe-table--header-wrapper .vxe-table--header {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.cost-summary-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n"]}]}