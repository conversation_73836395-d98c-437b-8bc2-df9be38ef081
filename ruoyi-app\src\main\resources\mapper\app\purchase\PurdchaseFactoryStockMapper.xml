<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.purchase.mapper.PurdchaseFactoryStockMapper">
    
    <resultMap type="PurdchaseFactoryStock" id="PurdchaseFactoryStockResult">
        <result property="dep"    column="DEP"    />
        <result property="depName"    column="DEP_NAME"    />
        <result property="class1"    column="CLASS1"    />
        <result property="invQty"    column="INV_QTY"    />
        <result property="stockMoney"    column="STOCK_MONEY"    />
        <result property="kgDate"    column="KG_DATE"    />
    </resultMap>

    <sql id="selectPurdchaseFactoryStockVo">
        select DEP, DEP_NAME, CLASS1, INV_QTY, STOCK_MONEY, KG_DATE from TDM_JP_STOCK_HIS
    </sql>

    <select id="selectPurdchaseFactoryStockList" parameterType="PurdchaseFactoryStock" resultMap="PurdchaseFactoryStockResult">
        <include refid="selectPurdchaseFactoryStockVo"/>
        <where>  
            <if test="dep != null  and dep != ''"> and DEP = #{dep}</if>
            <if test="depName != null  and depName != ''"> and DEP_NAME = #{depName}</if>
            <if test="class1 != null  and class1 != ''"> and CLASS1 = #{class1}</if>
            <if test="invQty != null "> and INV_QTY = #{invQty}</if>
            <if test="stockMoney != null "> and STOCK_MONEY = #{stockMoney}</if>
            <if test="kgDate != null  and kgDate != ''"> and KG_DATE = #{kgDate}</if>
        </where>
    </select>

    <select id="selectPurdchaseFactoryStockListMonthly" parameterType="PurdchaseFactoryStock" resultMap="PurdchaseFactoryStockResult">
        <include refid="selectPurdchaseFactoryStockVo"/>
        where KG_DATE &gt;= #{kgDate} and DEP_NAME = #{depName}
        order by KG_DATE 
    </select>

    <select id="selectPurdchaseFactoryStockAllListMonthly" parameterType="PurdchaseFactoryStock" resultMap="PurdchaseFactoryStockResult">
        select KG_DATE, CLASS1, sum(INV_QTY) as INV_QTY, sum(STOCK_MONEY) as STOCK_MONEY
        from TDM_JP_STOCK_HIS
        where KG_DATE &gt;= #{kgDate} 
        group by KG_DATE, CLASS1
    </select>

    <select id="selectDepNameList" resultType="String">
        select distinct DEP_NAME from TDM_JP_STOCK_HIS
    </select>
    
    <select id="selectPurdchaseFactoryStockById" parameterType="String" resultMap="PurdchaseFactoryStockResult">
        <include refid="selectPurdchaseFactoryStockVo"/>
        where DEP = #{dep}
    </select>
        
    <insert id="insertPurdchaseFactoryStock" parameterType="PurdchaseFactoryStock">
        insert into TDM_JP_STOCK_HIS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dep != null">DEP,</if>
            <if test="depName != null">DEP_NAME,</if>
            <if test="class1 != null">CLASS1,</if>
            <if test="invQty != null">INV_QTY,</if>
            <if test="stockMoney != null">STOCK_MONEY,</if>
            <if test="kgDate != null">KG_DATE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dep != null">#{dep},</if>
            <if test="depName != null">#{depName},</if>
            <if test="class1 != null">#{class1},</if>
            <if test="invQty != null">#{invQty},</if>
            <if test="stockMoney != null">#{stockMoney},</if>
            <if test="kgDate != null">#{kgDate},</if>
         </trim>
    </insert>

    <update id="updatePurdchaseFactoryStock" parameterType="PurdchaseFactoryStock">
        update TDM_JP_STOCK_HIS
        <trim prefix="SET" suffixOverrides=",">
            <if test="depName != null">DEP_NAME = #{depName},</if>
            <if test="class1 != null">CLASS1 = #{class1},</if>
            <if test="invQty != null">INV_QTY = #{invQty},</if>
            <if test="stockMoney != null">STOCK_MONEY = #{stockMoney},</if>
            <if test="kgDate != null">KG_DATE = #{kgDate},</if>
        </trim>
        where DEP = #{dep}
    </update>

    <delete id="deletePurdchaseFactoryStockById" parameterType="String">
        delete from TDM_JP_STOCK_HIS where DEP = #{dep}
    </delete>

    <delete id="deletePurdchaseFactoryStockByIds" parameterType="String">
        delete from TDM_JP_STOCK_HIS where DEP in 
        <foreach item="dep" collection="array" open="(" separator="," close=")">
            #{dep}
        </foreach>
    </delete>
    
</mapper>