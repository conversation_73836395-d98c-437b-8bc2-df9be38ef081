{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\factoryCostSummary\\index.vue?vue&type=style&index=1&id=4f1e864f&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\factoryCostSummary\\index.vue", "mtime": 1754372909832}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qIOmdnnNjb3BlZOagt+W8j++8jOehruS/neiDveWkn+imhueblnZ4ZS10YWJsZeeahOm7mOiupOagt+W8jyAqLw0KDQovKiDlvLrliLbopobnm5bmiYDmnInmu5rliqjmnaHmoLflvI8gLSAxNXB457KX57uG77yM5reh6JOd6Imy5Li76aKYICovDQouY29zdC1zdW1tYXJ5LXRhYmxlIDo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogMTVweCAhaW1wb3J0YW50Ow0KICBoZWlnaHQ6IDE1cHggIWltcG9ydGFudDsNCn0NCg0KLmNvc3Qtc3VtbWFyeS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZDogI2UzZjJmZCAhaW1wb3J0YW50Ow0KICBib3JkZXItcmFkaXVzOiA3cHggIWltcG9ydGFudDsNCn0NCg0KLmNvc3Qtc3VtbWFyeS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZDogIzY0YjVmNiAhaW1wb3J0YW50Ow0KICBib3JkZXItcmFkaXVzOiA3cHggIWltcG9ydGFudDsNCiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5jb3N0LXN1bW1hcnktdGFibGUgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQ6ICM0MmE1ZjUgIWltcG9ydGFudDsNCn0NCg0KLmNvc3Qtc3VtbWFyeS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmFjdGl2ZSB7DQogIGJhY2tncm91bmQ6ICMyMTk2ZjMgIWltcG9ydGFudDsNCn0NCg0KLmNvc3Qtc3VtbWFyeS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyLWNvcm5lciB7DQogIGJhY2tncm91bmQ6ICNlM2YyZmQgIWltcG9ydGFudDsNCn0NCg0KLyog5pu05YW35L2T55qE6YCJ5oup5Zmo77yM6ZKI5a+5dnhlLXRhYmxlICovDQouY29zdC1zdW1tYXJ5LXRhYmxlIC52eGUtdGFibGUgOjotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiAxNXB4ICFpbXBvcnRhbnQ7DQogIGhlaWdodDogMTVweCAhaW1wb3J0YW50Ow0KfQ0KDQouY29zdC1zdW1tYXJ5LXRhYmxlIC52eGUtdGFibGUgOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIGJhY2tncm91bmQ6ICNlM2YyZmQgIWltcG9ydGFudDsNCiAgYm9yZGVyLXJhZGl1czogN3B4ICFpbXBvcnRhbnQ7DQp9DQoNCi5jb3N0LXN1bW1hcnktdGFibGUgLnZ4ZS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZDogIzY0YjVmNiAhaW1wb3J0YW50Ow0KICBib3JkZXItcmFkaXVzOiA3cHggIWltcG9ydGFudDsNCiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5jb3N0LXN1bW1hcnktdGFibGUgLnZ4ZS10YWJsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogIzQyYTVmNSAhaW1wb3J0YW50Ow0KfQ0KDQouY29zdC1zdW1tYXJ5LXRhYmxlIC52eGUtdGFibGUgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjphY3RpdmUgew0KICBiYWNrZ3JvdW5kOiAjMjE5NmYzICFpbXBvcnRhbnQ7DQp9DQoNCi8qIEZpcmVmb3gg5pSv5oyBICovDQouY29zdC1zdW1tYXJ5LXRhYmxlIC52eGUtdGFibGUgew0KICBzY3JvbGxiYXItd2lkdGg6IGF1dG8gIWltcG9ydGFudDsNCiAgc2Nyb2xsYmFyLWNvbG9yOiAjNjRiNWY2ICNlM2YyZmQgIWltcG9ydGFudDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsnBA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/factoryCostSummary", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 顶部筛选区域 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"会计期\" prop=\"yearMonth\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.yearMonth\"\r\n          type=\"month\"\r\n          placeholder=\"请选择年月\"\r\n          format=\"yyyy-MM\"\r\n          value-format=\"yyyy-MM\"\r\n          size=\"small\"\r\n          style=\"width: 200px;\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"成本类型\" prop=\"costType\" v-if=\"showCostTypeSelect\">\r\n        <el-select\r\n          v-model=\"queryParams.costType\"\r\n          placeholder=\"请选择成本类型\"\r\n          clearable\r\n          size=\"small\"\r\n          style=\"width: 200px;\">\r\n          <el-option\r\n            v-for=\"option in costTypeOptions\"\r\n            :key=\"option.value\"\r\n            :label=\"option.label\"\r\n            :value=\"option.value\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"loading\">导出</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 质量成本表格 -->\r\n    <div class=\"table-container\">\r\n      <div class=\"table-header\">\r\n        <h3 class=\"table-title\">各分厂{{ currentCostTypeTitle }}金额汇总</h3>\r\n        <div class=\"table-unit-label\">单位：元</div>\r\n      </div>\r\n      <div class=\"table-scroll-container\">\r\n        <vxe-table\r\n          v-loading=\"loading\"\r\n          :data=\"manualTypeList\"\r\n          border\r\n          :row-class-name=\"getRowClassName\"\r\n          :span-method=\"objectSpanMethod\"\r\n          :max-height=\"tableMaxHeight\"\r\n          :scroll-y=\"{enabled: true}\"\r\n          :scroll-x=\"{enabled: true}\"\r\n          header-align=\"center\"\r\n          class=\"cost-summary-table\">\r\n          <vxe-column\r\n            title=\"成本类别\"\r\n            field=\"costType\"\r\n            align=\"center\"\r\n            width=\"100\"\r\n            fixed=\"left\" />\r\n          <vxe-column\r\n            title=\"科目\"\r\n            field=\"typeName\"\r\n            align=\"center\"\r\n            width=\"200\"\r\n            fixed=\"left\" />\r\n          <vxe-column\r\n            v-for=\"col in manualArray\"\r\n            :key=\"col\"\r\n            :title=\"col\"\r\n            :field=\"col\"\r\n            align=\"center\"\r\n            width=\"120\">\r\n            <template #default=\"{ row }\">\r\n              <div class=\"table-cell-content\">\r\n                <span v-if=\"row[col] && typeof row[col] === 'object'\">\r\n                  {{ formatNumber(row[col].costEx) }}\r\n                </span>\r\n                <span v-else>\r\n                  {{ formatNumber(row[col]) }}\r\n                </span>\r\n              </div>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { costCenterlist, listAllQualityCostDetail, exportFactoryCostSummary } from \"@/api/qualityCost/qualityCostDetail\";\r\n\r\nexport default {\r\n  name: \"factoryCostSummary\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        costCenterCname: null,\r\n        yearMonth: null,\r\n        costType: ''\r\n      },\r\n      // 表格相关数据\r\n      manualArray: [],\r\n      manualTypeList: [],\r\n      currentCostTypeTitle: '',\r\n      showCostTypeSelect: true,\r\n      // 成本类型选项\r\n      costTypeOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '预防成本', value: '预防成本' },\r\n        { label: '鉴定成本', value: '鉴定成本' },\r\n        { label: '内部损失成本', value: '内部损失成本' },\r\n        { label: '外部损失成本', value: '外部损失成本' }\r\n      ],\r\n      // 表格头部样式\r\n      tableHeaderStyle: {\r\n        background: '#f5f7fa',\r\n        color: '#303133'\r\n      },\r\n      // 表格最大高度\r\n      tableMaxHeight: 800,\r\n      // 类型名称到代码的映射\r\n      typeNameToCodeMap: new Map([\r\n        ['质量管理费', 'A1'],\r\n        ['质量管理差旅费', 'A11'],\r\n        ['质量管理会议费', 'A12'],\r\n        ['质量管理其他费用', 'A3'],\r\n        ['质量培训费', 'A2'],\r\n        ['质量评审费', 'A3'],\r\n        ['质量管理人员工资及附加', 'A4'],\r\n        ['试验检验费（物料消耗）', 'B1'],\r\n        ['第二、三方检测费', 'B2'],\r\n        ['质量检测设备的购置费用', 'B31'],\r\n        ['质量检测设备的维护费用', 'A32'],\r\n        ['质量检测设备的折旧费用', 'B33'],\r\n        ['质量检测人员工资及附加', 'B4'],\r\n        ['产品报废损失', 'C1'],\r\n        ['产品改判损失', 'C2'],\r\n        ['产品脱合同损失', 'C3'],\r\n        ['产品挽救处理项', 'C4'],\r\n        ['质量异议退货损失', 'D1'],\r\n        ['客户索赔费', 'D2'],\r\n        ['质量异议运费', 'D3'],\r\n        ['质量异议差旅费', 'D4']\r\n      ])\r\n    };\r\n  },\r\n  created() {\r\n    this.initializeComponent();\r\n  },\r\n  mounted() {\r\n    this.calculateTableHeight();\r\n    window.addEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  methods: {\r\n    /** 初始化组件 */\r\n    initializeComponent() {\r\n      this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n      this.initializeCostType();\r\n      this.getCostCenterList();\r\n      this.getList();\r\n    },\r\n\r\n    /** 初始化成本类型 */\r\n    initializeCostType() {\r\n      const routeCostType = this.$route.query.costType || this.$route.params?.costType;\r\n      if (routeCostType) {\r\n        this.queryParams.costType = String(routeCostType).replace(/^['\\\"]|['\\\"]$/g, '');\r\n        this.showCostTypeSelect = false;\r\n      } else {\r\n        this.queryParams.costType = '';\r\n        this.showCostTypeSelect = true;\r\n      }\r\n    },\r\n\r\n    /** 获取成本中心列表 */\r\n    getCostCenterList() {\r\n      costCenterlist().then(response => {\r\n        const options = response.data || [];\r\n        // 过滤掉value为\"特板热处理分厂\"的元素\r\n        this.costCenterOptions = options.filter(item => item.value !== '特板热处理分厂');\r\n        console.log('costCenterOptions:', this.costCenterOptions);\r\n      }).catch(() => {\r\n        this.$message.error('获取成本中心列表失败');\r\n      });\r\n    },\r\n\r\n    /** 表格单元格合并方法 */\r\n    objectSpanMethod({ row, rowIndex, columnIndex }) {\r\n      if (columnIndex === 0) { // \"成本类别\"列\r\n        const costType = row.costType;\r\n        const costTypeRows = this.manualTypeList.filter(item => item.costType === costType);\r\n        const firstIndex = this.manualTypeList.findIndex(item => item.costType === costType);\r\n        if (rowIndex === firstIndex) {\r\n          return {\r\n            rowspan: costTypeRows.length,\r\n            colspan: 1\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 格式化数字 */\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '' || Number(num) === 0) {\r\n        return '-';\r\n      }\r\n      return Number(num).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 查询质量成本列表 */\r\n    getList() {\r\n      if (!this.queryParams.yearMonth) {\r\n        this.$message.warning('请选择会计期');\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      const formatYearMonth = this.queryParams.yearMonth.replace('-', '');\r\n      const qualityCostDetail = {\r\n        costCenterCname: this.queryParams.costCenterCname,\r\n        yearMonth: formatYearMonth,\r\n        costType: this.queryParams.costType\r\n      };\r\n\r\n      listAllQualityCostDetail(qualityCostDetail).then(response => {\r\n        const rawData = response.data || [];\r\n        this.currentCostTypeTitle = this.queryParams.costType || '';\r\n\r\n        // 根据成本类型处理数据\r\n        this.processDataByCostType(rawData);\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n        this.$message.error('查询失败');\r\n      });\r\n    },\r\n\r\n    /** 根据成本类型处理数据 */\r\n    processDataByCostType(rawData) {\r\n      const costType = this.queryParams.costType;\r\n\r\n      if (costType === '') {\r\n        this.processAllCostTypes(rawData);\r\n      } else if (costType === '预防成本') {\r\n        this.processPreventionCost(rawData);\r\n      } else if (costType === '鉴定成本') {\r\n        this.processAppraisalCost(rawData);\r\n      } else if (costType === '内部损失成本') {\r\n        this.processInternalFailureCost(rawData);\r\n      } else if (costType === '外部损失成本') {\r\n        this.processExternalFailureCost(rawData);\r\n      }\r\n    },\r\n\r\n    /** 处理全部成本类型数据 */\r\n    processAllCostTypes(rawData) {\r\n      this.manualTypeList = this.getAllCostTypesList();\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(rawData);\r\n      this.processTotalRows(rawData, 'Z');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 获取全部成本类型列表 */\r\n    getAllCostTypesList() {\r\n      return [\r\n        { typeName: '质量管理费', costType: \"预防成本\" },\r\n        { typeName: '质量管理差旅费', costType: \"预防成本\" },\r\n        { typeName: '质量管理会议费', costType: \"预防成本\" },\r\n        { typeName: '质量管理其他费用', costType: \"预防成本\" },\r\n        { typeName: '质量培训费', costType: \"预防成本\" },\r\n        { typeName: '质量评审费', costType: \"预防成本\" },\r\n        { typeName: '质量管理人员工资及附加', costType: \"预防成本\" },\r\n        { typeName: '试验检验费（物料消耗）', costType: \"鉴定成本\" },\r\n        { typeName: '第二、三方检测费', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的购置、维护、折旧费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的购置费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的维护费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的折旧费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测人员工资及附加', costType: \"鉴定成本\" },\r\n        { typeName: '产品报废损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（普通类）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高钼钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高Ni钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高镍钼钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品改判损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品脱合同损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品挽救处理项', costType: \"内部损失成本\" },\r\n        { typeName: '质量异议退货损失', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（普通类）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高钼钢）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高Ni钢）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高镍钼钢）', costType: \"外部损失成本\" },\r\n        { typeName: '客户索赔费', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议运费', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议差旅费', costType: \"外部损失成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n    },\r\n\r\n    /** 初始化手动类型列表 */\r\n    initializeManualTypeList() {\r\n      // 为每项动态添加所有成本中心字段，初始值为 null\r\n      this.manualTypeList.forEach(typeItem => {\r\n        this.costCenterOptions.forEach(center => {\r\n          typeItem[center.value] = null;\r\n        });\r\n      });\r\n\r\n      // 生成手动数组\r\n      this.manualArray = [];\r\n      if (this.manualTypeList.length > 0) {\r\n        Object.keys(this.manualTypeList[0]).forEach(key => {\r\n          if (key !== 'typeName' && key !== 'costType') {\r\n            this.manualArray.push(key);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 填充数据到手动类型列表 */\r\n    fillDataToManualTypeList(rawData) {\r\n      rawData.forEach(dataItem => {\r\n        const manualItem = this.manualTypeList.find(typeItem => typeItem.typeName === dataItem.typeName);\r\n        if (manualItem && dataItem.costCenterCname && manualItem.hasOwnProperty(dataItem.costCenterCname)) {\r\n          manualItem[dataItem.costCenterCname] = {\r\n            costEx: dataItem.allcEx,\r\n            costTon: dataItem.allcTon,\r\n            costPerEx: dataItem.allcPerEx\r\n          };\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 处理总计行数据 */\r\n    processTotalRows(rawData, typeCode) {\r\n      const tonTotalRow = this.manualTypeList.find(item => item.typeName === '产量');\r\n      const priceTotalRow = this.manualTypeList.find(item => item.typeName === '吨钢成本');\r\n      const amountTotalRow = this.manualTypeList.find(item => item.typeName === '总金额');\r\n      const totalRows = rawData.filter(item => item.typeCode === typeCode);\r\n\r\n      if (tonTotalRow || priceTotalRow || amountTotalRow) {\r\n        this.manualArray.forEach(centerKey => {\r\n          if (centerKey === 'typeName') return;\r\n          const totalData = totalRows.find(z => z.costCenterCname === centerKey);\r\n          if (tonTotalRow) tonTotalRow[centerKey] = totalData ? totalData.allcTon : null;\r\n          if (priceTotalRow) priceTotalRow[centerKey] = totalData ? totalData.allcPerEx : null;\r\n          if (amountTotalRow) amountTotalRow[centerKey] = totalData ? totalData.allcEx : null;\r\n        });\r\n      }\r\n    },\r\n\r\n    /** 清理零值 */\r\n    cleanupZeroValues() {\r\n      this.manualTypeList.forEach(typeItem => {\r\n        this.manualArray.forEach(key => {\r\n          if (typeItem[key] === 0) {\r\n            typeItem[key] = null;\r\n          }\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 处理预防成本数据 */\r\n    processPreventionCost(rawData) {\r\n      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('A'));\r\n      this.manualTypeList = [\r\n        { typeName: '质量管理费', costType: \"预防成本\" },\r\n        { typeName: '质量管理差旅费', costType: \"预防成本\" },\r\n        { typeName: '质量管理会议费', costType: \"预防成本\" },\r\n        { typeName: '质量管理其他费用', costType: \"预防成本\" },\r\n        { typeName: '质量培训费', costType: \"预防成本\" },\r\n        { typeName: '质量评审费', costType: \"预防成本\" },\r\n        { typeName: '质量管理人员工资及附加', costType: \"预防成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(filteredRawData);\r\n      this.processTotalRows(filteredRawData, 'A');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 处理鉴定成本数据 */\r\n    processAppraisalCost(rawData) {\r\n      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('B'));\r\n      this.manualTypeList = [\r\n        { typeName: '试验检验费（物料消耗）', costType: \"鉴定成本\" },\r\n        { typeName: '第二、三方检测费', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的购置、维护、折旧费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的购置费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的维护费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测设备的折旧费用', costType: \"鉴定成本\" },\r\n        { typeName: '质量检测人员工资及附加', costType: \"鉴定成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(filteredRawData);\r\n      this.processTotalRows(filteredRawData, 'B');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 处理内部损失成本数据 */\r\n    processInternalFailureCost(rawData) {\r\n      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('C'));\r\n      this.manualTypeList = [\r\n        { typeName: '产品报废损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（普通类）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高钼钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高Ni钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品报废量（高镍钼钢）', costType: \"内部损失成本\" },\r\n        { typeName: '产品改判损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品脱合同损失', costType: \"内部损失成本\" },\r\n        { typeName: '产品挽救处理项', costType: \"内部损失成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(filteredRawData);\r\n      this.processTotalRows(filteredRawData, 'C');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 处理外部损失成本数据 */\r\n    processExternalFailureCost(rawData) {\r\n      const filteredRawData = rawData.filter(item => item.typeCode && item.typeCode.startsWith('D'));\r\n      this.manualTypeList = [\r\n        { typeName: '质量异议退货损失', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（普通类）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高钼钢）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高Ni钢）', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议报废量（高镍钼钢）', costType: \"外部损失成本\" },\r\n        { typeName: '客户索赔费', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议运费', costType: \"外部损失成本\" },\r\n        { typeName: '质量异议差旅费', costType: \"外部损失成本\" },\r\n        { typeName: '总金额', costType: \"总计\" },\r\n        { typeName: '产量', costType: \"总计\" },\r\n        { typeName: '吨钢成本', costType: \"总计\" }\r\n      ];\r\n      this.initializeManualTypeList();\r\n      this.fillDataToManualTypeList(filteredRawData);\r\n      this.processTotalRows(filteredRawData, 'D');\r\n      this.cleanupZeroValues();\r\n      this.applyTypeNameMapping();\r\n    },\r\n\r\n    /** 应用类型名称映射 */\r\n    applyTypeNameMapping() {\r\n      const renameMap = {\r\n        \"质量管理费\": \"一、质量管理费\",\r\n        \"质量管理差旅费\": \"1.差旅费\",\r\n        \"质量管理会议费\": \"2.会议费\",\r\n        \"质量管理其他费用\": \"3.其他费用\",\r\n        \"质量培训费\": \"二、质量培训费\",\r\n        \"质量评审费\": \"三、质量评审费\",\r\n        \"质量管理人员工资及附加\": \"四、质量管理人员工资及附加\",\r\n        \"试验检验费（物料消耗）\": \"一、试验检验费\",\r\n        \"第二、三方检测费\": \"二、外部检测费\",\r\n        \"质量检测设备的购置、维护、折旧费用\": \"三、质量检测设备费用\",\r\n        \"质量检测设备的购置费用\": \"1.购置费用\",\r\n        \"质量检测设备的维护费用\": \"2.维护费用\",\r\n        \"质量检测设备的折旧费用\": \"3.折旧费用\",\r\n        \"质量检测人员工资及附加\": \"四、质量检测人员工资及附加\",\r\n        \"产品报废损失\": \"一、产品报废损失\",\r\n        \"产品报废量（普通类）\": \"1.产品报废量（普通类）\",\r\n        \"产品报废量（高钼钢）\": \"2.产品报废量（高钼钢）\",\r\n        \"产品报废量（高Ni钢）\": \"3.产品报废量（高Ni钢）\",\r\n        \"产品报废量（高镍钼钢）\": \"4.产品报废量（高镍钼钢）\",\r\n        \"产品改判损失\": \"二、产品改判损失\",\r\n        \"产品脱合同损失\": \"三、产品脱合同损失\",\r\n        \"产品挽救处理项\": \"四、产品挽救处理项\",\r\n        \"质量异议退货损失\": \"一、质量异议退货损失\",\r\n        \"质量异议报废量（普通类）\": \"1.质量异议报废量（普通类）\",\r\n        \"质量异议报废量（高钼钢）\": \"2.质量异议报废量（高钼钢）\",\r\n        \"质量异议报废量（高Ni钢）\": \"3.质量异议报废量（高Ni钢）\",\r\n        \"质量异议报废量（高镍钼钢）\": \"4.质量异议报废量（高镍钼钢）\",\r\n        \"客户索赔费\": \"二、客户索赔费\",\r\n        \"质量异议运费\": \"三、质量异议运费\",\r\n        \"质量异议差旅费\": \"四、质量异议差旅费\"\r\n      };\r\n\r\n      this.manualTypeList.forEach(item => {\r\n        if (renameMap[item.typeName]) {\r\n          item.typeName = renameMap[item.typeName];\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.yearMonth = this.getDefaultYearMonth();\r\n      this.queryParams.costType = '';\r\n      this.manualTypeList = [];\r\n    },\r\n\r\n    /** 获取默认会计期 */\r\n   getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    /** 获取行样式类名 */\r\n    getRowClassName({ row }) {\r\n      return row.isSummary ? 'summary-row' : '';\r\n    },\r\n\r\n    /** 计算表格高度 */\r\n    calculateTableHeight() {\r\n      this.$nextTick(() => {\r\n        const windowHeight = window.innerHeight;\r\n        // 减去页面头部、筛选区域、标题等高度，大约180px\r\n        const availableHeight = windowHeight - 180;\r\n        // 设置表格最大高度，最小500px，最大不超过可用高度的85%\r\n        this.tableMaxHeight = Math.max(500, Math.min(800, availableHeight * 0.85));\r\n      });\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      console.log('=== 导出按钮被点击 ===');\r\n      console.log('manualTypeList:', this.manualTypeList);\r\n      console.log('manualTypeList长度:', this.manualTypeList ? this.manualTypeList.length : 'undefined');\r\n      console.log('queryParams:', this.queryParams);\r\n\r\n      // 检查是否有数据\r\n      if (!this.manualTypeList || this.manualTypeList.length === 0) {\r\n        this.$message.warning('暂无数据可导出，请先查询数据');\r\n        return;\r\n      }\r\n\r\n      // 检查会计期是否存在\r\n      if (!this.queryParams.yearMonth) {\r\n        this.$message.warning('请先选择会计期');\r\n        return;\r\n      }\r\n\r\n      // 构建请求数据，确保日期格式与查询数据时保持一致\r\n      const requestData = {\r\n        costCenterCname: this.queryParams.costCenterCname,\r\n        yearMonth: this.queryParams.yearMonth.replace('-', ''), // 将 2025-06 转换为 202506\r\n        costType: this.queryParams.costType,\r\n        // 传递处理后的表格数据\r\n        manualTypeList: this.manualTypeList,\r\n        manualArray: this.manualArray,\r\n        currentCostTypeTitle: this.currentCostTypeTitle\r\n      };\r\n\r\n      console.log('请求数据:', requestData);\r\n\r\n      this.$confirm('是否确认导出工厂成本汇总表数据?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        console.log('用户确认导出');\r\n        this.loading = true;\r\n        return exportFactoryCostSummary(requestData);\r\n      }).then(response => {\r\n        console.log('导出响应:', response);\r\n        this.download(response.msg);\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('导出失败:', error);\r\n        this.loading = false;\r\n        this.$message.error('导出失败：' + (error.message || '未知错误'));\r\n      });\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.initializeCostType();\r\n      this.getCostCenterList();\r\n      this.getList();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n</style>\r\n\r\n<style>\r\n/* 非scoped样式，确保能够覆盖vxe-table的默认样式 */\r\n\r\n/* 强制覆盖所有滚动条样式 - 15px粗细，淡蓝色主题 */\r\n.cost-summary-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;\r\n}\r\n\r\n/* 更具体的选择器，针对vxe-table */\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.cost-summary-table .vxe-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n/* Firefox 支持 */\r\n.cost-summary-table .vxe-table {\r\n  scrollbar-width: auto !important;\r\n  scrollbar-color: #64b5f6 #e3f2fd !important;\r\n}\r\n</style>\r\n/* 页面容器样式 */\r\n.app-container {\r\n  position: relative;\r\n  min-height: 100vh;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 表格标题头部容器 */\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  position: relative;\r\n  margin: 20px 0;\r\n}\r\n\r\n.table-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  margin: 0;\r\n  position: absolute;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  white-space: nowrap;\r\n}\r\n\r\n.table-scroll-container {\r\n  width: calc(100vw - 280px);\r\n  position: relative;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格单位标签 */\r\n.table-unit-label {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 30px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  font-weight: normal;\r\n  white-space: nowrap;\r\n  z-index: 1000;\r\n  background-color: #fff;\r\n  padding: 4px 12px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 表格样式 */\r\n.cost-summary-table {\r\n  min-width: 800px;\r\n  width: 100%;\r\n}\r\n\r\n.table-cell-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 汇总行样式 */\r\n.cost-summary-table ::v-deep .vxe-table--body .summary-row {\r\n  background-color: #f8f9fa;\r\n  font-weight: bold;\r\n}\r\n\r\n.cost-summary-table ::v-deep .vxe-table--body .summary-row .vxe-body--column {\r\n  background-color: #f8f9fa !important;\r\n}\r\n\r\n/* vxe-table 滚动条样式优化 - 统一15px粗细，淡蓝色主题 */\r\n/* 使用更高优先级的选择器 */\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar {\r\n  width: 15px !important;   /* 纵向滚动条宽度 - 调整为原来的三倍 */\r\n  height: 15px !important;  /* 横向滚动条高度 - 调整为原来的三倍 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-track,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;  /* 滚动条轨道背景色 - 淡蓝色背景 */\r\n  border-radius: 7px !important;   /* 轨道圆角，调整为滚动条粗细的一半 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;  /* 滚动条滑块颜色 - 淡蓝色 */\r\n  border-radius: 7px !important;   /* 滑块圆角，调整为滚动条粗细的一半 */\r\n  border: none !important;         /* 移除边框 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;  /* 鼠标悬停时的颜色 - 稍深的蓝色 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;  /* 点击时的颜色 - 更深的蓝色 */\r\n}\r\n\r\n.cost-summary-table >>> .vxe-table--body-wrapper::-webkit-scrollbar-corner,\r\n.cost-summary-table /deep/ .vxe-table--body-wrapper::-webkit-scrollbar-corner,\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper::-webkit-scrollbar-corner,\r\n.cost-summary-table .vxe-table--body-wrapper::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;  /* 滚动条交汇处的背景色 - 淡蓝色背景 */\r\n}\r\n\r\n/* 全局滚动条样式 - 确保覆盖所有可能的vxe-table滚动条 */\r\n.cost-summary-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n.cost-summary-table ::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;\r\n}\r\n\r\n/* vxe-table 固定列滚动条样式 - 保持与主表格一致的15px粗细，淡蓝色主题 */\r\n/* 由于上面的全局样式已经覆盖了所有滚动条，这里保留作为备用 */\r\n.cost-summary-table >>> .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table >>> .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table ::v-deep .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper::-webkit-scrollbar,\r\n.cost-summary-table ::v-deep .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper::-webkit-scrollbar {\r\n  width: 15px !important;   /* 固定列纵向滚动条宽度 - 调整为15px */\r\n  height: 15px !important;  /* 固定列横向滚动条高度 - 调整为15px */\r\n}\r\n\r\n/* Firefox 滚动条样式 - 淡蓝色主题，较粗滚动条 */\r\n.cost-summary-table ::v-deep .vxe-table--body-wrapper {\r\n  scrollbar-width: auto;              /* Firefox滚动条粗细：auto(较粗) | thin(细) | none(隐藏) */\r\n  scrollbar-color: #64b5f6 #e3f2fd;   /* Firefox滚动条颜色：滑块颜色(淡蓝) 轨道颜色(浅蓝) */\r\n}\r\n\r\n/* vxe-table 表格边框和样式 */\r\n.cost-summary-table ::v-deep .vxe-table {\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.cost-summary-table ::v-deep .vxe-table--border-line {\r\n  border-color: #ebeef5;\r\n}\r\n\r\n/* 表头样式 */\r\n.cost-summary-table ::v-deep .vxe-table--header-wrapper .vxe-table--header {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.cost-summary-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n"]}]}